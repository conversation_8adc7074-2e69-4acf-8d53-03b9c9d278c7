# cbo-ads-operation

基于 Vue3 + pinia + Element Plus 的后台管理系统解决方案

## 前言

基于 Vue3 + pinia + typescript，引用 Element Plus、Echarts 组件库，方便开发。

## 功能

-   [x] Element Plus
-   [x] vite 3
-   [x] pinia
-   [x] typescript
-   [x] 登录
-   [x] 表格/表单
-   [x] 图表 :echarts:
-   [x] 文件上传
-   [x] 权限管理
-   [x] 自定义图标

## 安装步骤

> 因为使用 vite3，node 版本需要 14.18+

```
git clone http://git.300.cn/trade/vue/cbo-ads-operation.git     // 下载项目
cd cbo-ads-operation    // 进入模板目录
npm install         // 安装项目依赖，等待安装完成之后，安装失败可用 cnpm 或 yarn

// 运行
npm run dev

// 执行构建命令，生成的dist文件夹放在服务器下即可访问
npm run build
```

## 项目截图

### 首页

![Image text](https://dongcha.300.cn/file/ymjztmkjbwfm/20240613/006a096d8de13cbc444bf34971751de9.png)

### 登录

![Image text](https://dongcha.300.cn/file/ymjztmkjbwfm/20240613/efc8a534ce741899aacfdb11fd6769bf.png)

