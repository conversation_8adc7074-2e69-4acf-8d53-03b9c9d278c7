import { defineStore } from "pinia";
import { ref } from "vue";

// setup
const usePermissionStore = defineStore("permission", () => {
  // state
  const arrAuth: any = ref([]);
  const buttonAuth: any = ref({});
  function setArrAuth(arr) {
    arrAuth.value = arr;
  }
  function setButtonAuth(arr) {
    buttonAuth.value = arr;
  }
  return {
    arrAuth,
    buttonAuth,
    setArrAuth,
    setButtonAuth,
  };
});
export default usePermissionStore;
