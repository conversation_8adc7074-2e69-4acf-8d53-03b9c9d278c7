import { createRouter, RouteRecordRaw, createWebHistory } from 'vue-router';
export const routes: RouteRecordRaw[] = [
    {
        path: '/',
        redirect: '/account-google/',
    },
    {
        path: '/',
        name: 'Home',
        component: () => import(/* webpackChunkName: "system-user" */ '../views/home.vue'),
        redirect: "/account-google/",
        children: [],
    },
    {
        path: '/404',
        meta: {
            title: '找不到页面',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "404" */ '../views/pages/404.vue'),
    },
    {
        path: '/report-google',
        meta: {
            title: 'google报表',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "report" */ '../views/report/google.vue'),
    }, {
        path: '/report-yandex',
        meta: {
            title: 'Yandex报表',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "report" */ '../views/report/yandex.vue'),
    }, {
        path: '/report-facebook',
        meta: {
            title: 'Facebook报表',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "report" */ '../views/report/facebook.vue'),
    }, {
        path: '/report-tikTok',
        meta: {
            title: 'TikTok报表',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "report" */ '../views/report/tikTok.vue'),
    }, {
        path: '/report-bing',
        meta: {
            title: 'Bing报表',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "report" */ '../views/report/bing.vue'),
    }, {
        path: '/report-linkedIn',
        meta: {
            title: 'LinkedIn报表',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "report" */ '../views/report/linkedIn.vue'),
    }
];
