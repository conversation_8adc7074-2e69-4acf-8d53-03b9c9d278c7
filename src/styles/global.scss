* {
  --default-bg-box: #f4f5fa;
  --el-bg-color-page: #f4f5fa;
  --el-pagination-button-height: 28px;
  --el-pagination-bg-color: none;
  --el-pagination-text-color: var(--el-color-primary);
  --el-pagination-border-radius: 4px;
  --el-pagination-button-color: #606266;
  --el-pagination-button-bg-color: none;
  --el-table-border-color: none;
}
.el-table {
  .el-table__body-header .el-table__cell {
    color: #2c2d33 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    font-family: "PingFang SC";
    background-color: #fff !important;
  }

  .el-table__row {
    background-color: #fff;
    align-items: center;
  }
  .el-checkbox {
    --el-checkbox-input-width: 22px;
    --el-checkbox-input-height: 22px;
    --el-checkbox-border-radius: 8px;
    --el-checkbox-input-border: 1px solid #cfd3d5;
    border-radius: 8px;
    .el-checkbox__inner {
      width: 22px !important;
      height: 22px !important;
    }
    .el-checkbox__inner::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 13px;
      height: 13px;
      border: 0;
      background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 14'%3e%3cpath d='M11.428,2.888181C11.8997,2.373146,12.6671,2.370247,13.142,2.881706C13.5804,3.353822,13.6165,4.0991800000000005,13.2484,4.61614L13.148,4.74034L6.39719,12.11182C5.95906,12.5902,5.26646,12.6263,4.78991,12.22065L4.67554,12.11002L0.8503700000000001,7.91707C0.379615,7.40106,0.383764,6.56893,0.8596360000000001,6.05846C1.2989039999999998,5.587260000000001,1.98657,5.55484,2.46002,5.95851L2.57366,6.06851L5.53838,9.31799L11.428,2.888181Z' fill='%23FFFFFF'/%3e%3c/svg%3e")
        no-repeat center;
      background-size: contain;
      transform: translate(-50%, -50%) scale(0);
      transition: transform 0.15s ease-in;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner::after {
      transform: translate(-50%, -50%) scale(1);
    }
    .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      height: 4px;
      top: 8px;
    }
  }
  .el-table__cell {
    border: none !important;
  }
  .el-table__header {
    height: 56px;
    line-height: 56px;
    border-bottom: 1px solid #f4f5fa !important;
    .el-table-column--selection {
      display: flex;
      align-items: center;
      height: 56px;
    }
    .el-table__cell {
      color: #2c2d33 !important;
      font-size: 14px !important;
      font-weight: 600 !important;
      font-family: "PingFang SC";
      background-color: #ffffff !important;
    }
    .el-checkbox {
      --el-checkbox-checked-text-color: #2c2d33;
    }
  }
  .el-table__body {
    .el-table__cell {
      font-weight: 400;
      height: 48px;
    }

    // 表格行悬停效果
    .el-table__row:hover {
      .el-table__cell {
        background-color: #f4f5fa !important;
      }
    }
    // 覆盖Element Plus默认斑马纹，改为从第一行开始
    .el-table__row--striped {
      .el-table__cell {
        background-color: #ffffff !important;
      }
    }

    // 单级表格样式：应用斑马纹效果
    &:not(:has(.el-table__row--level-1)) {
      // 奇数行样式（排除level-0）
      .el-table__row:nth-child(odd) {
        background-color: #f4f5fa !important;
      }
      // 偶数行样式（排除level-0）
      .el-table__row:nth-child(even) {
        background-color: #ffffff !important;
      }
    }

    // 多级表格样式：0级行使用斑马纹，1级行使用固定样式
    &:has(.el-table__row--level-1) {
      // 0级行斑马纹样式 (只对非 level-1 和非 level-2 行进行奇偶计数)
      .el-table__row:not(.el-table__row--level-1):not(
          .el-table__row--level-2
        ):nth-child(
          odd of :not(.el-table__row--level-1):not(.el-table__row--level-2)
        ) {
        background-color: #f4f5fa !important; // 奇数 L0 行灰色
      }
      .el-table__row:not(.el-table__row--level-1):not(
          .el-table__row--level-2
        ):nth-child(
          even of :not(.el-table__row--level-1):not(.el-table__row--level-2)
        ) {
        background-color: #ffffff !important; // 偶数 L0 行白色
      }

      // 1级和2级行固定样式 (仅在非 tree-table 时应用)
      .el-table__row--level-1,
      .el-table__row--level-2 {
        height: 40px;
        font-size: 13px;
        background-color: rgba(244, 245, 250, 0.4) !important;
        .el-checkbox {
          margin: 8px 0;
        }
      }
    }
  }
}
.el-table-column--selection {
  .cell {
    display: flex !important;
    height: 24px !important;
  }
}

.el-pagination {
  --el-pagination-button-height: 28px;
  --el-pagination-bg-color: none;
  --el-pagination-text-color: var(--el-color-primary);
  --el-pagination-border-radius: 4px;
  --el-pagination-button-color: #606266;
  --el-pagination-button-bg-color: none;
  font-size: 12px;
  .el-pager li.is-active {
    background-color: rgba(85, 112, 241, 0.1) !important;
    color: var(--el-color-primary) !important;
    font-weight: 500;
  }
  .el-pagination__sizes {
    .el-select__wrapper {
      height: 28px;
      font-size: 12px !important;
      min-height: 28px !important;
    }
    .el-select {
      width: 100px !important;
      height: 28px;
      font-size: 12px !important;
      .el-select__icon {
        font-size: 12px;
      }
    }
  }
  .el-pagination__jump {
    color: #606266;
    font-weight: 400;
    font-family: "PingFang SC";
    .el-input {
      width: 48px;
      height: 28px;
      border-radius: 2px;
    }
  }
}
// 表格按钮样式
.table-button {
  margin-right: 6px !important;
  text-align: center !important;
  padding: 4px 8px !important;
  border-radius: 8px !important;
  background: rgba(85, 112, 241, 0.1) !important;
  color: var(--el-color-primary) !important;
  text-align: center !important;
  font-size: 13px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  cursor: pointer !important;
  border: none !important;
  margin-left: 0px !important;
  height: auto !important;
}
.delete-table-button {
  background: rgba(255, 141, 141, 0.1) !important;
  color: #cc5f5f !important;
  margin-left: 0px !important;
}
.delete-table-button.my-el-tag-disabled {
  background: var(--el-color-info-light-8) !important;
  color: var(--el-disabled-text-color) !important;
  cursor: not-allowed !important;
  user-select: none !important;
}
.view-table-button {
  background: rgba(50, 147, 111, 0.1) !important;
  color: #519c66 !important;
  margin-left: 0px !important;
}
.el-popper {
  //padding: 0px !important;
  min-width: 120px !important;
}
.popList {
  padding: 8px 8px;
}
.pop_button {
  line-height: 20px;
  text-align: left;
  cursor: pointer;
  padding: 5px 8px;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  text-align: center;
  &:hover {
    background: #5570f11a;
    color: var(--el-color-primary);
  }
}