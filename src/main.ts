import { createApp, toRaw } from "vue";
import { create<PERSON><PERSON> } from "pinia";
import * as Sentry from "@sentry/vue";
import { Integrations } from "@sentry/tracing";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import { filterAsyncRoutes } from "@/utils/permission";
import App from "./App.vue";
import { routes } from "./router";
import { createRouter, createWebHistory } from "vue-router";
import { usePermissStore } from "./store/permiss";
import "element-plus/dist/index.css";
import "./assets/css/icon.css";
import "./assets/css/index.scss";
import "@/styles/common.scss"
import "./assets/css/index.scss";
import directives from "@/directives";
import "virtual:svg-icons-register"
declare global {
  interface Window {
    microApp: any;
    mount: CallableFunction;
    unmount: CallableFunction;
    __MICRO_APP_ENVIRONMENT__: string;
    __MICRO_APP_BASE_ROUTE__: string;
    __MICRO_APP_NAME__: string;
    __MICRO_APP_PUBLIC_PATH__: string;
    __MICRO_APP_BASE_APPLICATION__: string;
  }
}

let app: any = null;
let router: any = null;
let history: any = null;

// 将渲染操作放入 mount 函数
window.mount = () => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    const data = window.microApp.getData();
    if (routes[1].path === "/" && data.routes) {
      routes[1].children = [
        ...filterAsyncRoutes(toRaw(data.routes)),
        ...routes[1].children,
      ];
    }
  }
  history = createWebHistory(
    window.__MICRO_APP_BASE_ROUTE__ || import.meta.env.BASE_URL
  );
  router = createRouter({
    history: history,
    strict: true,
    routes: routes as any,
    scrollBehavior: () => ({ left: 0, top: 0 }),
  });

  app = createApp(App);

  Sentry.init({
    app,
    dsn: "https://<EMAIL>/121",
    debug: false, // 错误调试，在控制台打印错误
    attachStacktrace: true,
    release: import.meta.env.npm_package_version, // soureMap文件对应名
    integrations: [
      new Integrations.BrowserTracing({
        routingInstrumentation: Sentry.vueRouterInstrumentation(router),
        tracingOrigins: ["ads.300.cn", "pre-ads.300.cn", /^\//], // 设置“tracePropagationTargets”以控制应为哪些URL启用跟踪传播
      }),
    ],
    // allowUrls: ["http://localhost:8080", "https://ads.300.cn", "https://pre-ads.300.cn"],
    sampleRate: 1, // 决定sentry错误抓取的百分比，范围时0~1，比如今天项目报了100个错误，你配置的0.5，那么sentry随机给你上报50个。
    tracesSampleRate: 0.1, // 关闭性能上报，建议使用tracesSampler设置上报白名单，全开启会使sentry崩溃

    ignoreErrors: [
      // 不需要上报的错误
      "Failed to fetch",
      "NetworkError when attempting to fetch resource.",
      "ResizeObserver loop limit exceeded",
      "ResizeObserver loop completed with undelivered notifications",
      "Loading chunk",
      "Unable to preload CSS for",
      "Request aborted",
      "Network Error",
      "Cannot read properties of undefined",
      "Cannot read properties of null (reading 'googleRemainAmount')",
      "Cannot read properties of null (reading 'indexOf')"
    ],
  });
  window.$sentry = Sentry;
  app.use(createPinia());
  app.use(router);
  // 批量注册自定义指令
  Object.keys(directives).forEach((directiveName) => {
    app.directive(directiveName, directives[directiveName]);
  });
  // 注册elementplus图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
  }
  //   自定义权限指令
  const permiss = usePermissStore();
  app.directive("permiss", {
    mounted(el, binding) {
      if (binding.value && !permiss.key.includes(String(binding.value))) {
        el["hidden"] = true;
      }
    },
  });
  app.mount("#AdsApp");
};

// 将卸载操作放入 unmount 函数
window.unmount = () => {
  app && app.unmount();
  history && history.destroy();
  app = null;
  router = null;
  history = null;
  console.log("微应用vite卸载了 -- UMD模式");
};

// 非微前端环境直接渲染
if (!window.__MICRO_APP_ENVIRONMENT__) {
  window.mount();
}
