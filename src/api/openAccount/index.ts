import { request } from "@/utils/request";

/**
 * @description 获取开户主体列表(下拉列表)
 * @param data
 * @returns
 */
export const getAccountOptionListApi = (payload) => {
  return request({
    url: "/ad-trade-web/customer/mainList",
    method: "POST",
    data: { ...payload },
  });
};

/**
 * @description 获取开户主体列表
 * @param data
 * @returns
 */
export const getAccountListApi = (payload) => {
  return request({
    url: "/ad-trade-web/customer/mainPageList",
    method: "POST",
    data: { ...payload },
  });
};
/**
 * @description 获取开户主体列表
 * @param data
 * @returns
 */
export const addAndUpdateAccountMainApi = (payload) => {
  return request({
    url: "/ad-trade-web/customer/mainInfo/saveOrUpdate",
    method: "POST",
    data: { ...payload },
  });
};

// /customer/busOrgCode
//
export const getBusCodeApi = (payload) => {
  return request({
    url: "/ad-trade-web/customer/busOrgCode",
    method: "get",
    params: { ...payload },
  });
};

// 开户主体详情
export const getMainDetailApi = (payload) => {
  return request({
    url: "/ad-trade-web/customer/mainDetail",
    method: "POST",
    data: { ...payload },
  });
};
//
// 开户申请列表
export const getOpenReportListApi = (payload) => {
  return request({
    url: "/ad-trade-web/customer/openPagelist",
    method: "POST",
    data: { ...payload },
  });
};

// 开户申请
export const addAndUpdateOpenAccountApi = (payload) => {
  return request({
    url: "/ad-trade-web/customer/saveOrUpdateList",
    method: "POST",
    data: { ...payload },
  });
};

// 行业类型
export const getIndustryTypeListApi = (payload) => {
  return request({
    url: "/ad-trade-web/industryType/list",
    method: "POST",
    data: { ...payload },
  });
};
// 账户时区
export const getTimeZoneListApi = () => {
  return request({
    url: "/ad-trade-web/timeZone/list",
    method: "get",
  });
};

// 开户申请详情/customer/openDetail
export const getOpenReportDetailApi = (payload) => {
  return request({
    url: "/ad-trade-web/customer/openDetail",
    method: "POST",
    data: { ...payload },
  });
};
// 媒体开户方式接口
export const openType = (payload) => {
  return request({
    url: "/ad-trade-web/agency/openType",
    method: "POST",
    data: { ...payload },
  });
};

// 获取Facebook开户链接
export const openFacebookLink = () => {
  return request({
    url: "/ad-trade-web/facebook/customer/oeLink",
    method: "POST",
    data: { channelId: 1 },
  });
};
// 获取开户成功账户
export const openAccountSuccess = (payload) => {
  return request({
    url: "/ad-trade-web/customer/openlistByApplyId",
    method: "POST",
    data: { ...payload },
  });
};
