import {request} from '@/utils/request';

// 用户登录接口
export const userLogin = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.userName 用户名
     * @param {*} payload.password 密码
     * @param {*} payload.code 验证码
     * @param {*} payload.codeUuid 验证码k
     *  
     */
    return request({
        url: '/ad-trade-web/sys/user/login',
        method: 'post',
        data: { ...payload },
        // headers: {
        //     'Content-Type': 'application/x-www-form-urlencoded'
        // }

    })
}

// 获取登录验证码接口
export const getCodeImage = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.codeUuid 32 位的uuid
     *  
     */
    return request({
        url: '/ad-trade-web/sys/user/codeImage',
        method: 'GET',
        responseType: 'blob',
        params: { ...payload },
    })
}

// 退出登录接口
export const quitSystem = () => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.codeUuid 32 位的uuid
     *  
     */
    return request({
        url: '/ad-trade-web/sys/user/loginOut',
        method: 'GET'
    })
}