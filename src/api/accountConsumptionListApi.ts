import { request } from "@/utils/request";

// 账户消耗明细报告接口
export const costDetailPageList = (payload: Object) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.startDate 开始时间
   * @param {*} payload.endDate 结束时间
   * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
   * @param {*} payload.pageIndex 	当前页
   * @param {*} payload.mediumType 	广告账户
   * @param {*} payload.thirdAcountName 	媒体类型
   */
  return request({
    url: "/ad-trade-web/account/costDetailPageList",
    method: "POST",
    data: { ...payload },
  });
};

// 账户本月消耗接口汇总
export const costMonth = (payload: Object) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.customerId 驾驶舱客户ID
   */
  return request({
    url: "/ad-trade-web/account/costMonth",
    method: "POST",
    data: { ...payload },
  });
};

// 账户消耗报告下载接口
export const downloadCostDetail = (payload: Object) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.customerId 驾驶舱客户ID
   */
  return request({
    url: "/ad-trade-web/account/downloadCostDetail",
    method: "POST",
    data: { ...payload },
  });
};
