import {request} from '@/utils/request';

// 自动化报表分页查询接口
export const getReportList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.thirdCustomerNm 客户名称/ID
     * @param {*} payload.reportStatus 生成状态（1-待生成,2-生成中,3-成功,4-失败
     * @param {*} payload.reportType 报告类型（1-月报,2-周报,3-自定义）
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     */
    return request({
        url: '/ad-trade-web/report/pageList',
        method: 'post',
        data: { ...payload },
    })
}

// 自动化生成报告接口
export const generateReportInfo = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.mediumType 媒体类型（1-google，2-yandex）
     * @param {*} payload.customerId 客户ID（从账户列表获取）
     * @param {*} payload.thirdCustomerId 三方客户ID
     * @param {*} payload.thirdCustomerName 三方客户名称
     * @param {*} payload.reportType 报告类型(3-自定义)
     * @param {*} payload.reportStartDate 报告开始日期（yyy-MM-dd
     * @param {*} payload.reportEndDate 报告结束日期（yyy-MM-dd）
     */
    return request({
        url: '/ad-trade-web/report/save',
        method: 'post',
        data: { ...payload },
    })
}

// 查看
export const getLookReoprt = (payload: any) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.taskId 媒体类型 报表任务ID
     */
    return request({
        url: '/ad-trade-web/report/queryReportData',
        method: 'POST',
        data: {...payload},
    })
}


// 下载自动化报表
export const downloadReport = (payload: any) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.taskId 媒体类型 报表任务ID
     */
    return request({
        url: '/ad-trade-web/report/download',
        method: 'POST',
        // responseType: 'blob',
        data: {...payload},
    })
}
export const getLookReport = (payload: any) => {
    /**
     * @param {*} payload 参数
     */
    return request({
        url: '/ad-trade-web/report/queryReportData',
        method: 'POST',
        data: {...payload},
    })
  };