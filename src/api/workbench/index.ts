import { request } from "@/utils/request";

/**
 * <AUTHOR>
 * @description 获取广告账户数据统计
 * @returns Promise
 */
export const getAccountStatistics = (): any => {
  return request({
    url: "/ad-trade-web/home/<USER>",
    method: "POST",
  });
};

/**
 * <AUTHOR>
 * @description 获取消耗数据概览
 * @returns Promise
 */
export const getConsumptionOverview = (data: any): any => {
  return request({
    url: "/ad-trade-web/home/<USER>",
    method: "POST",
    data: { ...data },
  });
};

/**
 * <AUTHOR>
 * @description 获取资金概览
 * @returns Promise
 */
export const getCapitalOverview = (): any => {
  return request({
    url: "/ad-trade-web/home/<USER>",
    method: "POST",
  });
};

/**
 * <AUTHOR>
 * @description 获取资金概览
 * @returns Promise
 */
export const getrecharged = (): any => {
  return request({
    url: "/ad-trade-web/accountWarn/queryAlertWarn",
    method: "POST",
  });
};
