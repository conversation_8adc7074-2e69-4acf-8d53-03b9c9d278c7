import {request} from '@/utils/request';

// 用户列表分页查询
export const getPageList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.userName 客户名称
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     *  
     */
    return request({
        url: '/ad-trade-web/sys/user/pageList',
        method: 'POST',
        data: { ...payload },
    })
}

// 新增用户接口
export const addUserInfo = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.userName 用户名称（唯一）
     * @param {*} payload.password 用户密码
     * @param {*} payload.repassword 确认密码
     * @param {*} payload.email 邮箱 
     * 
     */
    return request({
        url: '/ad-trade-web/sys/user/save',
        method: 'POST',
        data: { ...payload },
    })
}

// 修改用户信息接口
export const editUserInfo = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.userId 用户id
     * @param {*} payload.password 用户密码
     * @param {*} payload.repassword 确认密码
     * @param {*} payload.email 邮箱 
     * 
     */
    return request({
        url: '/ad-trade-web/sys/user/update',
        method: 'POST',
        data: { ...payload },
    })
}

// 删除用户账号
export const deleteUserInfo = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.userId 用户id
     * 
     */
    return request({
        url: '/ad-trade-web/sys/user/del',
        method: 'POST',
        headers:{
            "Content-Type":'multipart/form-data'
        },
        data: { ...payload },
    })
}