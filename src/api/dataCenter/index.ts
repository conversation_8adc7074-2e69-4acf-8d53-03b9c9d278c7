import { request } from "@/utils/request";

/**
 * <AUTHOR>
 * @description 获取数据中心每日消耗
 * @returns Promise
 */
export const getDailyConsumption = (data: any): any => {
  return request({
    url: "/ad-trade-web/merchant/manage/cost/accountDayTotal",
    method: "POST",
    data: { ...data },
  });
};

/**
 * <AUTHOR>
 * @description 获取数据中心账户消耗明细
 * @returns Promise
 */
export const getAccountConsumption = (data: any): any => {
  return request({
    url: "/ad-trade-web/merchant/manage/cost/accountDetail",
    method: "POST",
    data: { ...data },
  });
};

/**
 * <AUTHOR>
 * @description 获取数据中心账户消耗汇总
 * @returns Promise
 */
export const getAccountSummary = (data: any): any => {
  return request({
    url: "/ad-trade-web/merchant/manage/cost/accounttotal",
    method: "POST",
    data: { ...data },
  });
};
