import { request } from '@/utils/request';
  /**
   *   查询页面引导
   *
   */
  export const queryGuidePageDetail = () => {
    return request({
      url: "/manage/guidePage/detail",
      method: "get",
    })
  } /**
   *   新增页面引导
   *
   */
  export const addGuidePage = (payload: any) => {
    return request({
      url: "/manage/guidePage/save",
      method: "post",
      data: payload
    })
  }
  /**
   *   编辑页面引导
   *
   */
  export const updateGuidePage = (payload: any) => {
    return request({
      url: "/manage/guidePage/update",
      method: "post",
      data: payload
    })
  }
  /**
   *   查询角色权限
   *
   */
  export const searchRoleList = (payload: any) => {
    return request({
      url: "/manage/role/listByUserId",
      method: "get",
      params: payload
    })
  }
