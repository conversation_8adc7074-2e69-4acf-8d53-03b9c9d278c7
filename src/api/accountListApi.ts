import { request } from "@/utils/request";

// 账户列表分页接口
export const accountPageList = (payload: Object) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.openName 开户主体
   * @param {*} payload.state 账户状态1:正常2:冻结
   * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
   * @param {*} payload.pageIndex 	当前页
   * @param {*} payload.customerId 	驾驶舱客户ID
   */
  return request({
    url: "/ad-trade-web/account/accountPageList",
    method: "POST",
    data: { ...payload },
  });
};

// 账户本月消耗接口汇总
export const remainAmount = (payload: Object) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.customerId 驾驶舱客户ID
   */
  return request({
    url: "/ad-trade-web/account/queryCustomerAmount",
    method: "POST",
    data: { ...payload },
  });
};

// 账户客户列表接口
export const accountMoneyList = (payload: Object) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.customerId 驾驶舱客户ID
   */
  return request({
    url: "/ad-trade-web/account/list",
    method: "POST",
    data: { ...payload },
  });
};

// 客户转账更新接口
export const updateTransferAmount = (payload: Object) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.outAccountInfoId 驾驶舱客户ID
   * @param {*} payload.inAccountInfoId 转入账户
   * @param {*} payload.outAmount 驾驶舱客户ID
   * @param {*} payload.inMediumType 转出金额
   * @param {*} payload.inAccountId 转入广告账户ID
   * @param {*} payload.inAccountName 转入广告账户名称
   * @param {*} payload.inCurrency 转入币种
   */
  return request({
    url: "/ad-trade-web/account/updateTransferAmount",
    method: "POST",
    data: { ...payload },
  });
};

// 客户账户转款分页查询接口
export const transferPageList = (payload: Object) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.inMediumType 转入媒体类型
   * @param {*} payload.outMediumType 转出媒体类型
   * @param {*} payload.thirdAccountName 广告客户名称
   * @param {*} payload.startDate 申请开始时间
   * @param {*} payload.endDate 申请结束时间
   * @param {*} payload.state 转款状态 1:转款中,2-转款完成
   * @param {*} payload.auditState 审核状态  1:待审核2:充值中,3:充值完成
   * @param {*} payload.customerId 驾驶舱客户ID
   * @param {*} payload.seoId 优化师ID
   * @param {*} payload.operatorName 操作人名称
   * @param {*} payload.pageIndex 当前页
   * @param {*} payload.pageSize 每页显示条数
   */
  return request({
    url: "/ad-trade-web/account/transferPageList",
    method: "POST",
    data: { ...payload },
  });
};

// 客户账户日预算修改接口
export const updateBudgetDay = (payload: any) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.accountInfoId 客户账户ID
   * @param {*} payload.budgetDay 日预算金额
   */
  return request({
    url: "/ad-trade-web/account/updateBudgetDay",
    method: "POST",
    data: { ...payload },
  });
};

// 预算调整记录分页查询接口
export const budgetDayPageList = (payload: any) => {
  /**
   * @param {*} payload 参数
   * @param {*} payload.accountInfoId mediumType 媒体类型 MediumTypeEnum 1-google,2-yandex,3-facebook,4-tiktok,5-bing,6-linkedin
   * @param {*} payload.startDate 调整开始时间
   * @param {*} payload.endDate 调整结束时间
   * @param {*} payload.state 调整状态
   * @param {*} payload.customerId 驾驶舱客户ID
   * @param {*} payload.seoId 优化师ID
   * @param {*} payload.operatorId 操作人ID
   * @param {*} payload.operatorName 操作人名称
   * @param {*} payload.pageIndex 当前页
   * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
   */
  return request({
    url: "/ad-trade-web/account/budgetDayPageList",
    method: "POST",
    data: { ...payload },
  });
};

// 获取开户主体列表(下拉列表)
export const getAccountOptionListApi = (payload: Object) => {
  return request({
    url: "/ad-trade-web/customer/mainList",
    method: "POST",
    data: { ...payload },
  });
};
// 获取账户列表
export const getAccountList = (payload: Object) => {
  return request({
    url: "/ad-trade-web/account/accountPageList",
    method: "POST",
    data: { ...payload },
  });
};
// 获取账户详情
export const getAccountDetail = (payload: Object) => {
  return request({
    url: "/ad-trade-web/account/detail",
    method: "GET",
    params: { ...payload },
  });
};
// 获取账户余额汇总
export const getAccountBalanceSummary = (payload: Object) => {
  return request({
    url: "/ad-trade-web/account/queryCustomerAmount",
    method: "POST",
    data: { ...payload },
  });
};
// 账户充值申请
export const accountRechargeApply = (payload: Object) => {
  return request({
    url: "/ad-trade-web/account/recharge/applyfor",
    method: "POST",
    data: { ...payload },
  });
};
// 获取客户账户列表
export const getCustomerAccountList = (payload: Object) => {
  return request({
    url: "/ad-trade-web/account/list",
    method: "POST",
    data: { ...payload },
  });
};
// 账户转款申请
export const accountTransferApply = (payload: Object) => {
  return request({
    url: "/ad-trade-web/account/transfer/applyfor",
    method: "POST",
    data: { ...payload },
  });
};
// 账户清零申请
export const accountClearZero = (payload: Object) => {
  return request({
    url: "/ad-trade-web/account/reset/applyfor",
    method: "POST",
    data: { ...payload },
  });
};
