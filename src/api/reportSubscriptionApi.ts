import { request } from '@/utils/request';

// 报表订阅分页查询接口
export const getSubscribeList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.subReportStatus 订阅状态（0-关闭,1-开启）
     * @param {*} payload.reportType 报告类型（1-月报，2-周报）
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     */
    return request({
        url: '/ad-trade-web/report/subscribe/pageList',
        method: 'post',
        data: { ...payload },
    })
}

// 报表订阅新增订阅接口
export const addSubscribe = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.reportEmailList 接收邮箱 List
     * @param {*} payload.mediumType 媒体类型(1-google,2-yandex)
     * @param {*} payload.thirdCustomerList 三方客户List
     * @param {*} payload.thirdCustomerList.customerId 客户Id
     * @param {*} payload.thirdCustomerList.thirdCustomerId 三方客户ID
     * @param {*} payload.thirdCustomerList.thirdCustomerName 三方客户名称
     * @param {*} payload.reportSubName 报告订阅名称
     * @param {*} payload.reportType 报告类型（1-月报，2-周报）
     */
    return request({
        url: '/ad-trade-web/report/subscribe/save',
        method: 'post',
        data: { ...payload },
    })
}


// 报表订阅修改订阅接口
export const editSubscribe = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.reportSubId 修改时必填；否则属于新增
     * @param {*} payload.reportEmailList 接收邮箱 List
     * @param {*} payload.mediumType 媒体类型(1-google,2-yandex)
     * @param {*} payload.thirdCustomerList 三方客户List
     * @param {*} payload.thirdCustomerList.customerId 客户Id
     * @param {*} payload.thirdCustomerList.thirdCustomerId 三方客户ID
     * @param {*} payload.thirdCustomerList.thirdCustomerName 三方客户名称
     * @param {*} payload.reportSubName 报告订阅名称
     * @param {*} payload.reportType 报告类型（1-月报，2-周报）
     */
    return request({
        url: '/ad-trade-web/report/subscribe/update',
        method: 'post',
        data: { ...payload },
    })
}

// 报表订阅发送记录分页查询接口
export const getSendHistoryList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.accountNm 账户名称/ID
     * @param {*} payload.reportType 报告类型（1-月报，2-周报）
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.reportSubId 报表订阅ID
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     */
    return request({
        url: '/ad-trade-web/report/subscribe/sendRecordPageList',
        method: 'post',
        data: { ...payload },
    })
}

// 报表订阅状态修改接口
export const editSubscribeState = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.accountNm 账户名称/ID
     * @param {*} payload.reportType 报告类型（1-月报，2-周报）
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     */
    return request({
        url: '/ad-trade-web/report/subscribe/statusUpdate',
        method: 'post',
        data: { ...payload },
    })
}

// 报表订阅上传接口
export const subscribeUpLoadFile = (payload: any) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.upLoadFile 账户名称/ID
     */
    return request({
        url: '/ad-trade-web/report/subscribe/upLoadFile',
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data;charset=UTF-8'
        },
        data: payload,
    })
}
// 报表订阅状态删除接口
export const deleteSubscribe = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.reportSubId 
     */
    return request({
        url: `/ad-trade-web/report/subscribe/unbind`,
        method: `post`,
        data: { ...payload },
    })
}