import { request } from "@/utils/request";

// 广告预警配置规则 回显
export const getTactics = (): any => {
  return request({
    url: "/ad-trade-web/accountWarn/rule",
    method: "POST",
  });
};

// 预警规则保存/修改
export const setTactics = (data: any): any => {
  return request({
    url: "/ad-trade-web/accountWarn/save",
    method: "POST",
    data: { ...data },
  });
};

// 获取配置记录
export const getTacticsLog = (): any => {
  return request({
    url: "/ad-trade-web/accountWarn/detail",
    method: "POST",
  });
};

// 预警启用停用
export const stopTactics = (data: any): any => {
  return request({
    url: "/ad-trade-web/accountWarn/onOff",
    method: "POST",
    data: { ...data },
  });
};
