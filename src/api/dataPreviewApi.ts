import {request} from '@/utils/request';

// 自动化报表分页查询接口
export const getQueryReportDataView = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.thirdCustomerId 三方客户ID
     * @param {*} payload.mediumType 媒体类型（1-google，2-yandex）
     * @param {*} payload.reportStartDate 报表开始时间
     * @param {*} payload.reportEndDate 报表结束时间
     */
    return request({
        url: '/ad-trade-web/report/queryReportDataView',
        method: 'post',
        data: { ...payload },
    })
}