import { request } from '@/utils/request';

// google广告客户列表查询接口
export const getGoogleList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.thirdCustomerNm 客户名称/ID
     * @param {*} payload.state 1：正常2:暂停
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     *  
     */
    return request({
        url: '/ad-trade-web/google/customer/pageList',
        method: 'post',
        data: { ...payload },
    })
}

// 获取google授权地址接口
export const getGoogleAuthorization = () => {
    return request({
        url: '/ad-trade-web/google/customer/oauthUrl',
        method: 'GET',
    })
}

// google代理用户授权接口
export const getGoogleAauth = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.token token 信息
     * @param {*} payload.token_expires 令牌生存期（以秒为单位）  
     * @param {*} payload.reflush_token 刷新token  
     */
    return request({
        url: '/ad-trade-web/google/customer/oauth',
        method: 'POST',
        data: { ...payload }
    })
}

// yandex广告账号列表查询接口
export const getYandexList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.thirdCustomerNm 客户名称/ID
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     *  
     */
    return request({
        url: '/ad-trade-web/yandex/customer/pageList',
        method: 'post',
        data: { ...payload },
    })
}

// yandex代理用户授权接口
export const getYandexAuthorization = () => {
    return request({
        url: '/ad-trade-web/yandex/customer/oauthUrl',
        method: 'GET',
    })
}


// yandex代理用户授权接口
export const getYandexAauth = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.token token 信息
     * @param {*} payload.token_expires 令牌生存期（以秒为单位）  
     */
    return request({
        url: '/ad-trade-web/yandex/customer/oauth',
        method: 'POST',
        data: { ...payload }
    })
}

// 通过媒体类型查询账号列表
export const searchMediumTypeList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.mediumType 媒体类型 MediumTypeEnum 1-google,2-yandex
     */
    return request({
        url: '/ad-trade-web/customer/list',
        method: 'POST',
        data: { ...payload }
    })
}
// 通过媒体类型查询账号列表
export const facebookTypeList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.mediumType 媒体类型 MediumTypeEnum 1-google,2-yandex
     */
    return request({
        url: `/ad-trade-web/facebook/customer/pageList`,
        method: `POST`,
        data: { ...payload }
    })
}
// 账号解绑
export const unBind = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.mediumType 媒体类型 MediumTypeEnum 1-google,2-yandex
     */
    return request({
        url: `/ad-trade-web/customer/unbind`,
        method: `POST`,
        data: { ...payload }
    })
}
// 获取facebook授权地址接口
export const facebookAuthorization = () => {
    return request({
        url: `/ad-trade-web/facebook/customer/oauthUrl`,
        method: `GET`,
    })
}
// facebook代理用户授权接口
export const facebookOauth = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.token token 信息
     * @param {*} payload.token_expires 令牌生存期（以秒为单位）  
     */
    return request({
        url: `/ad-trade-web/facebook/customer/oauth`,
        method: `POST`,
        data: { ...payload }
    })
}


// 获取tikTok授权地址接口
export const tikTokAuthorization = () => {
    return request({
        url: `/ad-trade-web/tiktok/customer/oauthUrl`,
        method: `GET`,
    })
}
// 通过媒体类型查询账号列表
export const tikTokTypeList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.mediumType 媒体类型 MediumTypeEnum 1-google,2-yandex
     */
    return request({
        url: `/ad-trade-web/tiktok/customer/pageList`,
        method: `POST`,
        data: { ...payload }
    })
}
// tikTok代理用户授权接口
export const tikTokOauth = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.token token 信息
     * @param {*} payload.token_expires 令牌生存期（以秒为单位）  
     */
    return request({
        url: `/ad-trade-web/tiktok/customer/oauth`,
        method: `POST`,
        data: { ...payload }
    })
}

// bing广告客户列表查询接口
export const getBingList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.thirdCustomerNm 客户名称/ID
     * @param {*} payload.state 1：正常2:暂停
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     *
     */
    return request({
        url: '/ad-trade-web/bing/customer/pageList',
        method: 'post',
        data: { ...payload },
    })
}

// 获取bing授权地址接口
export const getBingAuthorization = () => {
    return request({
        url: '/ad-trade-web/bing/customer/oauthUrl',
        method: 'GET',
    })
}

// bing代理用户授权接口
export const getBingAuth = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.token token 信息
     * @param {*} payload.token_expires 令牌生存期（以秒为单位）
     * @param {*} payload.reflush_token 刷新token
     */
    return request({
        url: '/ad-trade-web/bing/customer/oauth',
        method: 'POST',
        data: { ...payload }
    })
}

// linkedIn广告客户列表查询接口
export const getLinkedInList = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.thirdCustomerNm 客户名称/ID
     * @param {*} payload.state 1：正常2:暂停
     * @param {*} payload.pageIndex 当前页
     * @param {*} payload.pageSize 每页显示条数 每页最大返回条数100
     *
     */
    return request({
        url: '/ad-trade-web/linkedin/customer/pageList',
        method: 'post',
        data: { ...payload },
    })
}

// 获取linkedIn授权地址接口
export const getLinkedInAuthorization = () => {
    return request({
        url: '/ad-trade-web/linkedin/customer/oauthUrl',
        method: 'GET',
    })
}

// linkedIn代理用户授权接口
export const getLinkedInAuth = (payload: Object) => {
    /**
     * @param {*} payload 参数
     * @param {*} payload.token token 信息
     * @param {*} payload.token_expires 令牌生存期（以秒为单位）
     * @param {*} payload.reflush_token 刷新token
     */
    return request({
        url: '/ad-trade-web/linkedin/customer/oauth',
        method: 'POST',
        data: { ...payload }
    })
}
