.el-message-box {
    border-radius: 12px !important;
    padding: 0 !important;
    padding-bottom: 24px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .el-message-box__header {
        font-size: 20px !important;
        font-weight: bold !important;
        font-family: "PingFang SC" !important;
        color: #333333 !important;
        margin-top: 24px !important;
        margin-left: 24px !important;
    }
    .el-message-box__content {
        margin-top: 10px !important;
    }
    .el-message-box__message p {
        color: #333333 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        font-family: "PingFang SC" !important;
        padding-right: 24px !important;
    }
    .el-message-box__headerbtn {
        width: 24px !important;
        height: 24px !important;
        border-radius: 8px !important;
        opacity: 1 !important;
        background: #fff2e2 !important;
        margin: 24px 24px 0 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    .el-message-box-icon--warning {
        color: #cc5f5f !important;
        width: 16px !important;
        height: 16px !important;
        position: absolute !important;
        top: 4px !important;
        left: 40px !important;
        background: url("@/assets/svg/Iconly-Light-DangerCircle.svg") !important;
        background-size: contain !important;
        path {
            display: none !important;
        }
    }
    .el-message-box__container {
        position: relative !important;
        gap: 8px !important;
        padding-left: 64px !important;
    }
    .el-message-box__btns {
        margin-top: 20px !important;
        padding-right: 24px !important;
    }
    .el-button {
        outline: none !important;
    }
}
.el-overlay {
    background: #a3a3a333 !important;
    backdrop-filter: blur(8px) !important;
}
.el-dialog {
    border-radius: 12px !important;
    padding: 24px;
    .el-dialog__header {
        font-size: 20px !important;
        font-weight: 500 !important;
        font-family: "PingFang SC" !important;
        color: #333333 !important;
    }
    .el-dialog__headerbtn {
        width: 24px !important;
        height: 24px !important;
        border-radius: 8px !important;
        opacity: 1 !important;
        background: #fff2e2 !important;
        margin: 24px 24px 0 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
}
