[class*=" el-icon-lx"],
[class^="el-icon-lx"] {
    font-family: lx-iconfont !important;
}
.f {
    display: flex;
}
.aic {
    align-items: center;
}
.fd-c {
    flex-direction: column;
}
.jcs {
    justify-content: flex-start;
}
.jcc {
    justify-content: center;
}
 .aiss{
    align-items: self-start;
 }
.none {
    display: none !important;
}
.block {
    display: block !important;
}
.pt-31 {
    padding-top: 31px;
}
.form-status {
    display: inline-flex;
    width: 56px;
    opacity: 1;
    background: var(--el-color-primary);
    font-weight: 400;
    font-family: "PingFang SC";
    line-height: 14px;
    color: #ffffff;
    font-size: 12px;
    border-radius: 12px 0 12px 0;
    height: 20px;
    justify-content: center;
    align-items: center;
}
