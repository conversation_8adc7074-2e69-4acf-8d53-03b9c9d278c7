export interface HistoryList {
    id: number,
    reportSubName: string,
    mediumType: string,
    thirdCustomerName: string,
    thirdCustomerId: number,
    reportType: number,
    reportStartDate: string,
    reportEndDate: string,
    reportEmail: string,
    reportSendEmailDate: string,
    reportSendStatus: string,
    
} 
export interface TableList {
    subReportStatus: number,
    reportSubName: string,
    mediumType: number,
    reportType:number,
    subReportDate: string,
    thirdCustomerCount:number,
    thirdCustomerList:any[],
    reportEmailList: any[],
    
}  
export interface AddSubscriptionForm {
        reportType: any,
        reportEmailList: any[],
        mediumType:number,
        user:any[],
        reportSubName: string
} 