export interface OuthInfo {
    agencyCustomer: string,
    oauthTime: string,
    oauthState: string
}

export interface GoogleList {
    thirdCustomerName: string,
    thirdCustomerId: string,
    state: string,
    companyName: string,
    mcc: string,
    remainAmount: string,
    agencyId: string,
    agencyCustomer: string,
    oauthTime: string,
    oauthState: string,
    oauthToken: string,
    customerView: any
}

export interface YandexList {
    thirdCustomerName: string,
    thirdCustomerId: string,
    state: string,
    companyName: string,
    remainAmount: string
}