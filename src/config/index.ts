// 环境判断函数
export const getEnvPrefix = () => {
  // 开发环境可以通过环境变量指定使用哪个环境的API
  if (import.meta.env.MODE === "development") {
    // 可以根据需要返回开发环境使用的前缀，默认使用pre环境
    return import.meta.env.VITE_API_ENV || "pre-";
  }

  // 预发布环境
  if (window.location.href.indexOf("pre-") !== -1 || window.location.href.indexOf("localhost") !== -1) {
    return "pre-";
  }

  // 测试环境
  if (window.location.href.indexOf("test-") !== -1) {
    return "test-";
  }

  // 生产环境，无前缀
  return "";
};

// 根据环境生成URL
const generateURL = (baseDomain: string) => {
  const prefix = getEnvPrefix();
  // 从基础域名中提取协议和域名部分
  const domainParts = baseDomain.split("//");
  return `${domainParts[0]}//${prefix}${domainParts[1]}`;
};

// 服务基础域名配置
const domains = {
  baseURL: "//ads.gmarketing.tech",
};

export default {
  baseURL: generateURL(domains.baseURL),
};
