<template>
  <el-tour
    v-model="isShow"
    type="primary"
    @finish="finish"
    :close-on-press-escape="false"
    @close="close"
    :target-area-clickable="false"
    :scroll-into-view-options="{ block: 'center' }"
  >
    <el-tour-step
      v-for="(item, index) in list"
      :key="item.target"
      :target="'#' + item.target"
      :placement="item.placement"
      :title="item.title"
      :description="item.description"
      :next-button-props="index == list.length - 1 ? iKnow : next"
    >
      <template #default>
        <div>{{ item.description.split(";")[0] }}</div>
        <br />
        <div>{{ item.description.split(";")[1] }}</div>
      </template>
    </el-tour-step>
  </el-tour>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue"
import { updateGuidePage } from "../../api/tourSetpApi"
const guideDetail = JSON.parse(localStorage.getItem("guideDetail") || "{}")
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  tourStep: {
    type: Array,
    default: () => []
  },
  typeName: {
    type: String,
    default: ""
  }
})
const iKnow = ref({
  children: "知道了"
})
const next = ref({
  children: "下一步"
})
watch(
  () => props.open,
  (newVal) => {
    isShow.value = newVal
  }
)
const emit = defineEmits(["finish", "close"])
const finish = async () => {
  const res: any = await updateGuidePage({
    [props.typeName]: 1,
    guideId: guideDetail.guideId
  })
  if (res?.code == 200) {
    guideDetail[props.typeName] = 1
    localStorage.setItem("guideDetail", JSON.stringify(guideDetail))
    console.log("当前页面引导完成")
  }
  emit("finish")
}
const close = async () => {
  emit("close")
  const res: any = await updateGuidePage({
    [props.typeName]: 1,
    guideId: guideDetail.guideId
  })
  if (res?.code == 200) {
    guideDetail[props.typeName] = 1
    localStorage.setItem("guideDetail", JSON.stringify(guideDetail))
    console.log("当前页面引导完成")
  }
}
console.log(props.open)
const isShow = ref(props.open)
const list = ref<any[]>(props.tourStep)
console.log(props)
</script>
