<template>
  <div class="breadcrumb-container">
    <el-breadcrumb :separator-icon="SeparatorIcon">
      <!-- 默认首页图标 -->
      <el-breadcrumb-item>
        <svg-icon icon-class="ads-default" />
      </el-breadcrumb-item>
      <!-- 动态面包屑项 -->
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbList"
        :key="index"
        @click="handleBreadcrumbClick(item.path, index)"
        :class="{ 'breadcrumb-clickable': item.path }"
      >
        {{ item.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed, defineComponent, h } from "vue"
import { useRoute, useRouter } from "vue-router"
import SvgIcon from "@/components/svgIcon/index.vue"

/**
 * 面包屑项接口定义
 */
interface BreadcrumbItem {
  /** 显示文本 */
  title: string
  /** 跳转路径（可选，最后一项通常不设置） */
  path?: string
  /** 图标名称（Element Plus Icons）*/
  icon?: string
}

/**
 * 面包屑组件属性接口
 */
interface BreadcrumbProps {
  /** 自定义面包屑列表 */
  items?: BreadcrumbItem[]
  /** 是否自动根据路由生成面包屑 */
  autoGenerate?: boolean
}

const router = useRouter()

// 创建分隔符组件
const SeparatorIcon = defineComponent({
  render() {
    return h(SvgIcon, { iconClass: "separator" })
  }
})

const props = withDefaults(defineProps<BreadcrumbProps>(), {
  items: () => [],
  autoGenerate: () => true
})

const route = useRoute()

// 面包屑列表
const breadcrumbList = computed(() => {
  // 优先使用手动传入的面包屑项
  if (props.items && props.items.length > 0) {
    return props.items
  }

  // 其次使用自动生成（如果开启）
  if (props.autoGenerate) {
    return generateBreadcrumbFromRoute()
  }

  // 默认返回空数组
  return []
})

// 处理面包屑点击跳转
function handleBreadcrumbClick(path: string | undefined, index: number) {
  // 无需跳转
  console.log(path, "path", index)
  // path && router.push(path)
}
const toDashboard = () => {
  // router.push({ name: "Dashboard" })
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: "/dashboard",
      name: "ads",
      parentPath: "/",
      isChild: false,
    });
  }
}

// 根据路由自动生成面包屑
function generateBreadcrumbFromRoute(): BreadcrumbItem[] {
  const matched = route.matched.filter((item) => {
    return item?.name && item?.name !== "Home" && item?.path && item.path !== "/"
  })
  console.log(matched, "matched")
  return matched.map((item) => {
    return {
      title: item.meta?.title as string,
      path: item.path,
      icon: item.meta?.icon as string
    }
  })
}
</script>

<style scoped lang="scss">
.breadcrumb-container {
  :deep(.el-breadcrumb) {
    font-size: 14px;
    display: flex;
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #b1b7c2;

        &:hover {
          color: var(--el-color-primary);
        }

        .el-icon {
          font-size: 16px;
        }
      }

      &:last-child {
        .el-breadcrumb__inner {
          color: #333333;
          font-weight: 500;

          &:hover {
            color: #333333;
          }
        }
      }
    }
  }
}

.breadcrumb-clickable {
  cursor: pointer;
}

.breadcrumb-clickable:hover {
  color: var(--el-color-primary);
}
</style>
