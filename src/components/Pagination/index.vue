<template>
  <div class="zq-pagination">
    <el-pagination
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :small="isSMall"
    />
  </div>
</template>
<script lang="ts">
export default {
  props: {
    total: {
      type: Number,
      required: true,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    isSMall: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["handleCurrentChange", "update:pageSize"],

  methods: {
    handleCurrentChange(val: number) {
      console.log(`当前页: ${val}`);
      this.$emit("handleCurrentChange", val);
    },
    handleSizeChange(val: number) {
      console.log(`每页 ${val} 条`);

      this.$emit("update:pageSize", val);
    },
  },
};
</script>
<style lang="scss" scoped>
.zq-pagination {
  display: flex;
  justify-content: flex-end;
  background-color: #ffffffff;
  height: 72px;
  align-items: center;
}
</style>
