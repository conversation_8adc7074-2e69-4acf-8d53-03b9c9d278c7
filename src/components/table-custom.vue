<template>
  <div>
    <el-table
      class="mgb20"
      :border="border"
      :data="sortTableData"
      @selection-change="handleSelectionChange"
      stripe
      @sort-change="sortChange"
      table-layout="auto"
    >
      <template v-for="item in columns" :key="item.prop">
        <el-table-column
          v-if="item.visible"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :type="item.type"
          :align="item.align || 'center'"
          show-overflow-tooltip
          :sortable="
            isSortable &&
            item.prop !== 'keyWords' &&
            item.prop !== 'keyWordsZh' &&
            item.prop !== 'positionName'
              ? 'custom'
              : false
          "
        >
          <template #default="{ row }" v-if="item.type == 'operation'">
            <el-button
              class="table-button"
              :icon="Money"
              @click="recharge(row)"
              disabled
              v-if="isAdmin"
            >
              充值
            </el-button>
            <el-button
              class="table-button"
              :icon="TrendCharts"
              @click="transfer(row)"
              disabled
              v-if="isAdmin"
            >
              转账
            </el-button>
            <el-button
              class="table-button"
              :icon="Edit"
              @click="editFunc(row)"
              v-if="subscriptionFlag || auotoFlag || adminShow"
            >
              编辑
            </el-button>
            <el-button
              class="table-button"
              :icon="Download"
              @click="download(row)"
              v-if="auotoFlag"
            >
              下载
            </el-button>
            <el-button
              class="table-button"
              :icon="Histogram"
              @click="sendHistory(row)"
              v-if="subscriptionFlag"
            >
              发送记录
            </el-button>
            <el-button
              class="table-button delete-table-button"
              :icon="Delete"
              @click="handleDelete(row)"
              v-if="adminShow"
            >
              删除
            </el-button>
            <span v-else-if="item.formatter">
              {{ item.formatter(row[item.prop]) }}
            </span>
            <span v-else>
              {{ row[item.prop] }}
            </span>
          </template>
          <template
            #default="{ row }"
            v-if="item.prop == 'state' && props.isHistoryPage"
          >
            <div>
              <span
                class="zq-state-dot"
                :style="{
                  width: '6px',
                  height: '6px',
                  'background-color':
                    row.state === '调整中' ? '#1C7CFF' : '#00B700',
                }"
              ></span>
              <span>{{ row.state }}</span>
            </div>
          </template>
          <template
            #default="{ row }"
            v-if="item.prop == 'state' && !props.isHistoryPage"
          >
            <div>
              <span
                v-if="
                  row.state !== '转款中' &&
                  row.state !== '转款完成' &&
                  row.state !== '转款失败'
                "
                :style="{
                  'background-color':
                    row.state === '正常' ? '#00B700' : '#FF0000',
                }"
                class="zq-state-dot"
              ></span>
              <span>{{ row.state }}</span>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <div class="zq-pagination">
      <el-pagination
        v-if="total && isShowPagination"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :layout="layout"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs, PropType, ref, watch } from "vue";
import {
  Histogram,
  Edit,
  View,
  Refresh,
  Download,
  Money,
  TrendCharts,
  Delete,
} from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";

const props = defineProps({
  recharge: {
    type: Function,
    default: () => {},
  },
  // 表格相关
  tableData: {
    type: Array,
    default: [],
  },
  columns: {
    type: Array as PropType<any[]>,
    default: [],
  },
  rowKey: {
    type: String,
    default: "id",
  },
  total: {
    type: Number,
    default: 0,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 10,
  },

  layout: {
    type: String,
    default: "total, sizes, prev, pager, next, jumper",
  },
  delFunc: {
    type: Function,
    default: () => {},
  },
  transfer: {
    type: Function,
    default: () => {},
  },
  download: {
    type: Function,
    default: () => {},
  },
  editFunc: {
    type: Function,
    default: () => {},
  },
  delSelection: {
    type: Function,
    default: () => {},
  },
  refresh: {
    type: Function,
    default: () => {},
  },
  changePage: {
    type: Function,
    default: () => {},
  },
  changePageSize: {
    type: Function,
    default: () => {},
  },
  selectionChange: {
    type: Function,
    default: () => {},
  },
  auotoFlag: {
    type: Boolean,
    default: false,
  },
  subscriptionFlag: {
    type: Boolean,
    default: false,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
  adminShow: {
    type: Boolean,
    default: false,
  },
  sendHistory: {
    type: Function,
    default: () => {},
  },
  height: {
    type: Number,
    default: 515,
  },
  isShowPagination: {
    type: Boolean,
    default: true,
  },
  isSortable: {
    type: Boolean,
    default: false,
  },
  isHistoryPage: {
    type: Boolean,
    default: false,
  },
  border: {
    type: Boolean,
    default: true,
  },
});

let { tableData, columns, rowKey, total, currentPage, pageSize, layout } =
  toRefs(props);
const sortTableData = ref<any>(tableData);
columns.value.forEach((item) => {
  if (item.visible === undefined) {
    item.visible = true;
  }
});

// 当选择项发生变化时会触发该事件
const multipleSelection = ref([]);
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection;
  props.selectionChange(selection);
};

// 当前页码变化的事件
const handleCurrentChange = (val: number) => {
  console.log(`当前页: ${val}`);
  props.changePage(val);
};
const handleSizeChange = (val: number) => {
  props.changePageSize(val);
  console.log(`当前页条数: ${val}`);
};

const handleDelete = (row) => {
  ElMessageBox.confirm("确定要删除吗？", "提示", {
    type: "warning",
  })
    .then(async () => {
      props.delFunc(row);
    })
    .catch(() => {});
};

const getIndex = (index: number) => {
  return index + 1 + (currentPage.value - 1) * pageSize.value;
};
const sortChange = (obj: any) => {
  console.log(obj);
  let typeName = obj.prop;
  let order = obj.order;
  if (order == "ascending") {
    sortTableData.value.sort(
      (a, b) => parseFloat(a[typeName]) - parseFloat(b[typeName])
    );
  } else if (order == "descending") {
    sortTableData.value.sort(
      (a, b) => parseFloat(b[typeName]) - parseFloat(a[typeName])
    );
  } else {
    sortTableData.value.sort((a, b) => parseFloat(b.cost) - parseFloat(a.cost));
  }
  console.log(sortTableData.value);
};
watch(
  () => sortTableData.value,
  (newVal) => {
    console.log(newVal);
    sortTableData.value = newVal;
  },
  { deep: true }
);
</script>

<style scoped>
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 10px;
}

.columns-setting-icon {
  display: block;
  font-size: 18px;
  cursor: pointer;
  color: #676767;
}
</style>
<style lang="scss" scoped>
.table-header .cell {
  color: #333;
}
.zq-pagination {
  display: flex;
  justify-content: flex-end;
}
.zq-state-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}
.mgb20 {
  max-height: 600px;
  overflow-y: scroll;
}
</style>
