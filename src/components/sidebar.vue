<template>
    <div class="sidebar">
        <el-menu
            class="sidebar-el-menu"
            :default-active="onRoutes"
            :collapse="sidebar.collapse"
            :background-color="sidebar.bgColor"
            :text-color="sidebar.textColor"
            router
        >
            <template v-for="item in menuList" :key="item.index" >
                <el-sub-menu :index="item.index" v-if="item.children.length">
                <template #title>
                    <el-icon><component :is="item.icon"></component></el-icon>
                    <span>{{ item.title }}</span>
                </template>
                <el-menu-item-group v-for="(threeItem, i) in item.children" :key="i" :index="threeItem.index" >
                    <el-menu-item :index="threeItem.index">{{ threeItem.title }}</el-menu-item>
                </el-menu-item-group>
                </el-sub-menu>
                <el-menu-item :index="item.index" v-else>
                    <template #title>
                        <el-icon><component :is="item.icon"></component></el-icon>
                        <span>{{ item.title }}</span>
                    </template>
                </el-menu-item>
            </template>
        </el-menu>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue';
import { useSidebarStore } from '../store/sidebar';
import { useRoute } from 'vue-router';
import { menuData, menuAdminRoutes } from '@/components/menu';

const route = useRoute();
const menuList = ref<any>([])
watchEffect(()=>{
    console.log(route)
    if(route.name == "administration"){
        menuList.value = menuAdminRoutes
    } else {
        menuList.value = menuData
    }
    console.log(menuList.value)
})
const onRoutes = computed(() => {
    console.log(route.path)
    return route.path;
});

const sidebar = useSidebarStore();
</script>

<style lang="scss" scoped>
.sidebar {
    display: block;
    position: absolute;
    left: 0;
    top: 70px;
    bottom: 0;
    overflow-y: scroll;
}

.sidebar::-webkit-scrollbar {
    width: 0;
}

.sidebar-el-menu:not(.el-menu--collapse) {
    width: 250px;
}

.sidebar-el-menu {
    min-height: 100%;
}
</style>
