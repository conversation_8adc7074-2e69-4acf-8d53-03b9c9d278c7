<template>
    <div class="more-icon">
      <el-icon class="more-icon-icon"><MoreFilled size="14" /></el-icon>
    </div>
  </template>
  
  <script setup lang="ts">
  import { MoreFilled } from "@element-plus/icons-vue"
  </script>
  
  <style scoped lang="scss">
  .more-icon {
    width: 24px;
    height: 24px;
    border-radius: 8px;
    opacity: 1;
    background: #5570f11a;
    display: flex;
    align-items: center;
    justify-content: center;
    .more-icon-icon {
      transform: rotate(90deg);
      color: #5570f1;
    }
  }
  </style>
  