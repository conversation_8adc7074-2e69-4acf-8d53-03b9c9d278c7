import { Menus } from '@/types/menu';

export const menuData: Menus[] = [
    {
        id: '1',
        title: '广告账户管理',
        index: '1',
        icon: 'UserFilled',
        children: [
            {
                id: '1',
                pid: '1',
                index: '/authorized-agent/',
                title: '账户授权',
            }
        ],
    },{
        id: '2',
        title: '数据中心',
        index: '2',
        icon: 'Histogram',
        children: [
            {
                id: '1',
                pid: '1',
                index: '/automated-reporting',
                title: '自动化报表',
            },
            {
                id: '2',
                pid: '2',
                index: '/report-subscription',
                title: '报表订阅',
            },
            {
                id: '3',
                pid: '3',
                index: '/data-preview',
                title: '数据中心',
            }
        ],
    }
];

export const menuAdminRoutes: Menus[]  = [{
    id: '1',
    title: '账号管理',
    index: '1',
    icon: 'Tools',
    children: [
        {
            id: '1',
            pid: '1',
            index: '/administration',
            title: '修改账号',
        },
    ],
}]