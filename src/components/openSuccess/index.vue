<template>
  <el-dialog
    v-model="dialogVisible"
    width="380"
    :show-close="false"
    class="success-dialog"
  >
    <div class="success-container">
      <img src="@/assets/img/openSuccess.png" alt="" class="success-img" />
      <div class="success-title">提交成功</div>
      <p class="success-desc">
        {{ midumeTypeMapping[midumeType] }}开户大约需2～3个工作日，请耐心等待
      </p>
      <el-button type="primary" @click="openFacebookLink">
        查看开户进度
      </el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { midumeTypeMapping } from "@/utils/mapping";
const midumeType = ref(1);
const dialogVisible = ref(false);
const applyId = ref("");
const openFacebookLink = () => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path:
        "/ads/openAccount/account-open-main?mediaType=" +
        midumeType.value +
        "&applyId=" +
        applyId.value,
      name: "ads",
      parentPath: "/ads/openAccount/account-open-main",
      isChild: true,
    });
  }
};
const open = (params: any) => {
  dialogVisible.value = true;
  midumeType.value = params.type;
  applyId.value = params.applyId;
  setTimeout(() => {
    dialogVisible.value = false;
  }, 3000);
};
defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
.success-dialog {
  .el-dialog__header {
    display: none;
  }
}
.success-container {
  text-align: center;
  .success-img {
    width: 45px;
    height: 45px;
    margin-bottom: 17px;
  }
  .success-title {
    font-size: 14px;
    font-weight: 500;
    font-family: "PingFang SC";
    line-height: 22px;
    color: #1d2129;
    margin-bottom: 24px;
  }
  .success-desc {
    margin-bottom: 24px;
  }
}
</style>
