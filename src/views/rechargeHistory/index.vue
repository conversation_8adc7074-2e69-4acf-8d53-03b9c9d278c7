<template>
  <h2 class="ads-title">
    <Breadcrumb />
  </h2>
  <div class="recharge-history">
    <!-- 公共tabs组件 -->
    <CommonTabs :tab-list="tabList" @tabs-change="tabsChange" />
    <!-- 表格组件 -->
    <DynamicTable
      ref="dyTableRef"
      :form-items="formItems"
      :columns="columns"
      :initial-data="tableData"
      :showFormLable="false"
      v-loading="loading"
      @search="getRechargeList"
    >
      <template #formBtn>
        <el-button
          type="primary"
          @click="exportExcel"
          v-if="
            permissionStore?.buttonAuth?.RechargeHistory?.indexOf(
              'RechargeHistoryBtn',
            ) != -1
          "
          >导出</el-button
        >
      </template>
      <template #headerSlot="{ row }">
        <div style="display: flex; align-items: center">
          {{ row.label }}
          <el-tooltip
            effect="dark"
            content="到账金额=充值金额*当日实时汇率"
            placement="top"
          >
            <el-icon style="margin-left: 5px"><Warning /></el-icon>
          </el-tooltip>
        </div>
      </template>
      <template #custom="{ row }">
        <div class="tooltip-div f aic">
          {{
            row[ETableColumnKeys.RECHARGE_STATUS] == 1
              ? "充值中"
              : row[ETableColumnKeys.RECHARGE_STATUS] == 2
              ? "充值成功"
              : "充值失败"
          }}
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.auditMsg"
            placement="bottom"
            v-if="row[ETableColumnKeys.RECHARGE_STATUS] == 3"
          >
            <el-icon class="select_icon">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </div>
      </template>
    </DynamicTable>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from "vue";
import CommonTabs from "@/views/pages/accountList/components/common/commonTabs.vue";
import DynamicTable from "@/components/dynamicTable.vue";
import {
  EFormPropKeys,
  ETableColumnKeys,
  ERechargeType,
} from "./enum/rechargeHistory";
import { EMediaType } from "./enum/components";
import { ElInput, ElDatePicker, ElSelect } from "element-plus";
import { getRechargeHistory } from "@/api/rechargeHistory";
import { formatterMediaType, formatterRechargeStatus } from "@/utils/formatter";
import {
  downloadFile,
  getQueryParamsByKey,
  validateTimeRange,
} from "@/utils/common";
import configLocation from "@/config";
import { useRoute } from "vue-router";
import usePermissionStore from "@/store/modules/permission";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

let route = useRoute();
const permissionStore = usePermissionStore();
// 当前tabs类型
const currentTab = ref<any>(ERechargeType.RECHARGE_PROGRESS);
// tabs列表
const tabList = ref([
  {
    label: "充值进度",
    name: ERechargeType.RECHARGE_PROGRESS,
  },
  {
    label: "充值记录",
    name: ERechargeType.RECHARGE_RECORD,
  },
]);

// 表格实例组件
const dyTableRef = ref<InstanceType<typeof DynamicTable>>(null);
// 动态账户名称
const accountName = ref<string>(getQueryParamsByKey("accountName") ?? "");
// 动态表单配置
const formItems = computed(() => {
  return [
    {
      label: "广告账户名称",
      prop: EFormPropKeys.ACCOUNT_NAME,
      component: ElInput,
      placeholder: "广告账户名称或ID搜索",
      defaultValue: accountName.value,
    },
    {
      label:
        currentTab.value === ERechargeType.RECHARGE_PROGRESS
          ? "申请充值时间"
          : "完成充值时间",
      prop: EFormPropKeys.APPLY_TIME,
      component: ElDatePicker,
      defaultValue: [],
      type: "datetimerange",
      rangeSeparator: "-",
      props: {
        startPlaceholder: "开始日期",
        endPlaceholder: "结束日期",
        valueFormat: "YYYY-MM-DD hh:mm:ss",
        clearable: true,
        onChange: validateTimeRange,
      },
    },
    {
      label: "充值状态",
      prop: EFormPropKeys.RECHARGE_STATUS,
      component: ElSelect,
      placeholder: "请选择充值状态",
      props: {
        selectOptions: [
          {
            label: "充值中",
            value: 1,
          },
          {
            label: "充值成功",
            value: 2,
          },
          {
            label: "充值失败",
            value: 3,
          },
        ],
      },
    },
    {
      label: "媒体",
      prop: EFormPropKeys.MEDIA,
      component: ElSelect,
      placeholder: "请选择媒体",
      props: {
        selectOptions: [
          {
            label: "google",
            value: EMediaType.GOOGLE,
          },
          {
            label: "yandex",
            value: EMediaType.YANDEX,
          },
          {
            label: "meta",
            value: EMediaType.META,
          },
          {
            label: "tiktok",
            value: EMediaType.TIKTOK,
          },
          {
            label: "bing",
            value: EMediaType.BING,
          },
          {
            label: "linkedin",
            value: EMediaType.LINKEDIN,
          },
        ],
      },
    },
    {
      label: "订单号搜索",
      prop: EFormPropKeys.ORDER_NO,
      component: ElInput,
      placeholder: "订单号搜索",
      defaultValue: accountName.value,
    },
  ].filter((item) => {
    if (currentTab.value === ERechargeType.RECHARGE_PROGRESS) {
      return item.prop !== EFormPropKeys.ORDER_NO;
    } else {
      return item.prop !== EFormPropKeys.RECHARGE_STATUS;
    }
  });
});

// 动态表格列配置
const columns = computed(() => {
  return currentTab.value === ERechargeType.RECHARGE_RECORD
    ? rechargeRecordKeys.value
    : rechargeProgressKeys.value;
});

// 充值记录列配置
const rechargeRecordKeys = ref([
  {
    label: "充值订单号",
    prop: ETableColumnKeys.ORDER_NO,
  },
  {
    label: "媒体",
    prop: ETableColumnKeys.MEDIA,
    formatter: formatterMediaType,
  },
  {
    label: "广告账户名称",
    prop: ETableColumnKeys.NAME,
  },
  {
    label: "广告账户ID",
    prop: ETableColumnKeys.ID,
  },
  {
    label: "充值金额（CNY）",
    prop: ETableColumnKeys.RECHARGE_AMOUNT,
  },
  {
    label: "账户币种",
    prop: ETableColumnKeys.RECHARGE_CURRENCY,
  },
  {
    label: "到账金额",
    prop: ETableColumnKeys.ARRIVAL_AMOUNT,
    headerType: "custom",
  },
  {
    label: "汇率",
    prop: ETableColumnKeys.EXCHANGE_RATE,
  },
  {
    label: "完成充值时间",
    prop: ETableColumnKeys.COMPLETE_TIME,
  },
]) as any;

// 充值进度列配置
const rechargeProgressKeys = ref([
  {
    label: "媒体",
    prop: ETableColumnKeys.MEDIA,
    formatter: formatterMediaType,
  },
  {
    label: "广告账户名称",
    prop: ETableColumnKeys.NAME,
  },
  {
    label: "广告账户ID",
    prop: ETableColumnKeys.ID,
  },
  {
    label: "账户币种",
    prop: ETableColumnKeys.RECHARGE_CURRENCY,
  },
  {
    label: "充值金额（CNY）",
    prop: ETableColumnKeys.RECHARGE_AMOUNT,
  },
  {
    label: "充值状态",
    prop: ETableColumnKeys.RECHARGE_STATUS,
    type: "custom",
  },
  {
    label: "充值订单号",
    prop: ETableColumnKeys.ORDER_NO,
  },
  {
    label: "申请充值时间",
    prop: ETableColumnKeys.APPLY_TIME,
  },
  {
    label: "操作人",
    prop: ETableColumnKeys.OPERATOR,
  },
]) as any;

// loading
const loading = ref(false);
// 表格数据
const tableData = ref([]);
// 记录查询参数数据
const saveParams = ref({});

// 充值管理类型切换
const tabsChange = (tab) => {
  currentTab.value = tab;
  dyTableRef.value.resetForm();
};

// 格式化查询参数
const formatQueryParams = (data) => {
  let params = {} as any;
  if (data) {
    const [startDate, endDate] = data[EFormPropKeys.APPLY_TIME];
    params = {
      pageIndex: data.currentPage,
      pageSize: data.pageSize,
      [EFormPropKeys.ACCOUNT_NAME]: data[EFormPropKeys.ACCOUNT_NAME],
      [EFormPropKeys.RECHARGE_STATUS]: data[EFormPropKeys.RECHARGE_STATUS],
      startDate,
      endDate,
      [EFormPropKeys.MEDIA]: data[EFormPropKeys.MEDIA],
      [EFormPropKeys.ORDER_NO]: data[EFormPropKeys.ORDER_NO],
    };
  } else {
    params = {
      pageIndex: 1,
      pageSize: 10,
      [EFormPropKeys.ACCOUNT_NAME]: accountName.value,
    };
  }
  // 区分 记录和进度
  params.selectType =
    currentTab.value === ERechargeType.RECHARGE_PROGRESS ? 1 : 2;
  saveParams.value = params;
  return params;
};

// 获取充值列表
const getRechargeList = (data?) => {
  loading.value = true;
  const params = formatQueryParams(data);
  getRechargeHistory(params)
    .then((res) => {
      loading.value = false;
      tableData.value = res.data.list;
      dyTableRef.value.setPageTotal(res.data.total);
    })
    .catch((err) => {
      console.log(err, "err");
      loading.value = false;
    });
};

// excel数据导出
const exportExcel = () => {
  downloadFile({
    url: configLocation.baseURL + "/ad-trade-web/account/recharge/viewDownload",
    params: {
      ...saveParams.value,
      selectType: currentTab.value === ERechargeType.RECHARGE_PROGRESS ? 1 : 2,
    },
    method: "POST",
    headers: {},
    name:
      currentTab.value === ERechargeType.RECHARGE_PROGRESS
        ? "充值进度"
        : "充值记录",
  });
};

onMounted(() => {
  if (route.query && route.query.currentTab) {
    currentTab.value = route.query.currentTab;
  }
  getRechargeList();
});
</script>

<style lang="scss" scoped>
.ads-title {
  font-family: "PingFangSC-Medium";
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  cursor: pointer;
  display: flex;
  align-items: center;
  span {
    flex: 1;
    margin-left: 10px;
  }
}
.recharge-history {
  width: 100%;
  height: calc(100% - 80px);
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  overflow-y: auto;
}
</style>
