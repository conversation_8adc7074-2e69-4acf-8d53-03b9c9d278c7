<template>
  <h2 class="ads-title">
    <el-icon size="24" @click="goBack()"><Back /></el-icon>
    <span>详情</span>
  </h2>
  <div style="background-color: #ffffff; padding-top: 0">
    <el-tabs
      class="demo-tabs"
      v-model="activeTabs"
      style="background-color: #ffffff"
    >
      <el-tab-pane key="账户待充值" name="1">
        <template #label>
          <span class="custom-tabs-label">
            <span>账户待充值</span>
          </span>
        </template>
      </el-tab-pane>
    </el-tabs>
    <DynamicTable
      ref="dyTableRef"
      :form-items="[]"
      :columns="columns"
      :initial-data="tableData"
      dy-table-radius="0 0 8px 8px"
      backgroundColor="#f5f5fa"
      max-height="360px"
      v-loading="loading"
      @search="getTransferPageList"
    >
      <template #middle>
        <div class="export-btn">
          <el-button type="primary" @click="exportExcel">导出</el-button>
        </div>
      </template>
      <template #custom="{ row }">
        <!-- 余额 -->
        <div class="tooltip-div">
          <div>
            {{
              row.mediumType === 1
                ? "Google"
                : row.mediumType === 2
                ? "Yandex"
                : row.mediumType === 3
                ? "Meta"
                : row.mediumType === 4
                ? "Tiktok"
                : row.mediumType === 5
                ? "Bing"
                : row.mediumType === 6
                ? "Linkedin"
                : ""
            }}
          </div>
        </div>
      </template>
    </DynamicTable>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, h, computed } from "vue";
import { getrecharged } from "@/api/workbench";
import { ElLink } from "element-plus";
import DynamicTable from "@/components/dynamicTable.vue";
import { downloadFile } from "@/utils/common";
import configLocation from "@/config";

const activeTabs = ref("1");
const loading = ref(false);

const rowHandler = (row: any) => {};
const listButtonNode = (row: any) => {
  let btnListConfigsList = [
    {
      label: "充值",
      callback: rowHandler,
      auth: "WorkbenchOperation",
    },
  ];
  return h(
    "div",
    {},
    btnListConfigsList.map((item, index) =>
      h(
        ElLink,
        {
          style: {
            paddingRight: "10px",
          },
          type: "primary",
          underline: false,
          onClick: () => item.callback(row),
          permission: item.auth,
        },
        () => item.label
      )
    )
  );
};

let columns = computed(() => {
  return [
    { type: "index", label: "序号", width: "60" },
    { prop: "mediumType", label: "媒体平台", width: "200", type: "custom" },
    { prop: "thirdAccountName", label: "广告账户名称", minWidth: "200" },
    { prop: "thirdAccountId", label: "广告账户ID", width: "150" },
    { prop: "currency", label: "账户币种", width: "150" },
    { prop: "remainAmount", label: "余额", width: "150" },
    {
      label: "操作",
      fixed: "right",
      prop: (row: any) => listButtonNode(row),
      width: 80,
    },
  ].filter((item) => {
    return true;
  });
});

let tableData = ref([]);
onMounted(() => {
  getTransferPageList();
});

const getTransferPageList = async () => {
  try {
    loading.value = true;
    const res: any = await getrecharged();
    loading.value = false;
    if (res?.code == 200 && res?.data && res?.data.length > 0) {
      tableData.value = res?.data;
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.log(error);
  }
};
const goBack = () => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "routerGo",
      path: "/ads/workbench",
      name: "ads",
      parentPath: "/ads/workbench",
      isChild: false,
      go: -1,
    });
  }
};
// excel数据导出
const exportExcel = () => {
  downloadFile({
    url: configLocation.baseURL + "/ad-trade-web/accountWarn/exportAlertWarn",
    params: {},
    method: "POST",
    headers: {},
    name: "账户待充值",
  });
};
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: PingFangSC-Medium;
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  cursor: pointer;
  display: flex;
  align-items: center;
  span {
    flex: 1;
    margin-left: 10px;
  }
}
.details-text {
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  letter-spacing: 0;
  margin-bottom: 20px;
}
.ads-remainAmount {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  .remainAmount-item {
    width: 221px;
    height: 91px;
    background: #ffffff;
    border-radius: 3px;
    padding: 16px;
    box-sizing: border-box;
    .item-top {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Medium;
      font-weight: bold;
      font-size: 16px;
      color: #303133;
      .top-icon {
        width: 16px;
        height: 16px;
        margin-right: 12px;
      }
    }
    .item-bottom {
      text-align: right;
      font-family: HelveticaNeue-Medium;
      font-weight: 500;
      font-size: 24px;
      color: #303133;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 12px;
    }
  }
}

.demo-tabs {
  box-sizing: border-box;
  padding: 20px;
  //   height: calc(100vh - 251px);
}

.custom-tabs-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;

  .tabs-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}
.export-btn {
  width: 100%;
  background-color: #fff;
  text-align: right;
  padding-right: 20px;
}
:deep(.table-container) {
  border-radius: 0 !important;
}
</style>
