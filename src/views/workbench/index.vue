<template>
  <RouterView v-if="currentRoute.name !== 'Workbench'" />
  <div class="dashboard-home" v-else>
    <Breadcrumb />
    <div class="advertising-information">
      <div class="mainLeft">
        <!-- banner图 -->
        <div class="banner co-existence">
          <el-carousel height="240px">
            <el-carousel-item v-for="item in bannerList" :key="item.id">
              <img :src="item.img" alt="" />
            </el-carousel-item>
          </el-carousel>
        </div>
        <!-- 媒体开户 -->
        <div class="media-account-opening co-existence">
          <div class="media-account-opening-title">
            媒体开户
            <div
              class="goMore"
              @click="goUrl('/ads/openAccount/account-open-apply')"
            >
              全部媒体>>
            </div>
          </div>
          <div class="media-account-opening-list">
            <div
              v-for="item in conferenceMediaList"
              :key="item.icon"
              class="media-account-opening-item"
            >
              <div class="media-account-opening-item-title">
                <img class="applyImg" :src="item.icon" alt="" />
                <div class="apply-title">{{ item.title }}</div>
              </div>
              <div class="media-account-opening-item-desc">
                <el-tooltip>
                  <template #content>
                    <div v-html="item.descTool"></div>
                  </template>
                  {{ item.desc }}
                </el-tooltip>
              </div>
              <div class="opening-btn">
                <el-button type="primary" @click="openAccount(item.type)">
                  立即开户
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 消耗数据概览 -->
        <div class="data-overview co-existence">
          <div class="data-overview-title">
            消耗数据概览
            <div class="data-overview-screen">
              <el-select
                v-model="dataSourceVal"
                placeholder="请选择"
                @change="mediaChange"
              >
                <el-option
                  v-for="item in dataSourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-date-picker
                v-model="dataDateVal"
                type="daterange"
                range-separator="～"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                @change="dateChange"
              />
            </div>
          </div>
          <div class="data-overview-chart"></div>
        </div>
      </div>
      <div class="mainRight">
        <!-- 广告账户数据统计 -->
        <div class="company-details">
          <div class="company-title">{{ companyName || merchantName || "--" }}</div>
          <div class="maturity-time"></div>
          <div class="company-data-info">
            <div
              class="company-data-item"
              @click="goUrl('/ads/openAccount/account-open-main')"
            >
              <el-tooltip
                class="box-item"
                effect="dark"
                content="已提交申请开户的申请单总数"
                placement="top"
              >
                <div class="company-data-item-title">开户申请数量</div>
              </el-tooltip>
              <div class="company-data-item-value">
                {{ accountData.applyCount || 0 }}
              </div>
            </div>
            <div
              class="company-data-item"
              @click="goUrl('/ads/openAccount/account-open-main?auditState=1')"
            >
              <el-tooltip
                class="box-item"
                effect="dark"
                content="审核中的开户申请总数"
                placement="top"
              >
                <div class="company-data-item-title">开户审核中</div>
              </el-tooltip>
              <div class="company-data-item-value">
                {{ accountData.auditing || 0 }}
              </div>
            </div>
            <div
              class="company-data-item"
              @click="goUrl('/ads/openAccount/account-open-main?auditState=4')"
            >
              <el-tooltip
                class="box-item"
                effect="dark"
                content="媒介审核驳回开户申请总数"
                placement="top"
              >
                <div class="company-data-item-title">开户审核驳回</div>
              </el-tooltip>
              <div class="company-data-item-value">
                {{ accountData.auditFail || 0 }}
              </div>
            </div>
            <div class="company-data-item" @click="goDetails()">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="触发余额预警的账号总数"
                placement="top"
              >
                <div class="company-data-item-title">账号待充值</div>
              </el-tooltip>
              <div class="company-data-item-value">
                {{ accountData.waitRecharge || 0 }}
              </div>
            </div>
            <!-- <div class="company-data-item">
              <div class="company-data-item-title">账户状态异常</div>
              <div class="company-data-item-value">
                {{ accountData.failCount || 0 }}
              </div>
            </div> -->
            <div class="company-data-item">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="广告账户有余额的总数"
                placement="top"
              >
                <div class="company-data-item-title">活跃账户数</div>
              </el-tooltip>
              <div class="company-data-item-value">
                {{ accountData.activeAccount || 0 }}
              </div>
            </div>
          </div>
        </div>
        <!-- 常用工具 -->
        <div class="tool">
          <div class="toolTitle">常用工具</div>
          <div class="toolList">
            <div
              class="toolRow"
              v-for="(item, index) in toolList"
              :key="index"
              @click="goUrl(item.path)"
            >
              <img class="toolRowImg" :src="item.toolImg" alt="" />
              <div class="toolRowLable">{{ item.toolName }}</div>
            </div>
          </div>
        </div>
        <!-- 服务推荐 -->
        <div class="tool recommended">
          <div class="toolTitle">服务推荐</div>
          <div class="recommendedList">
            <div
              class="recommendedRow"
              v-for="(item, index) in recommendedList"
              :key="index"
              @click="openUrl(item.recommendedUrl)"
            >
              <img class="recommendedImg" :src="item.recommendedImg" alt="" />
              <div class="recommendedRowMain">
                <div class="recommendedRowMainTitle">
                  {{ item.recommendedTitle }}
                </div>
                <div class="recommendedRowMainDesc">
                  {{ item.recommendedDesc }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 资金概览 -->
        <div class="funding-overview">
          <div class="funding-overview-title">
            资金概览
            <div class="titleTime">最近更新时间：{{ updateTime }}</div>
          </div>
          <div class="pay">
            <div class="payLeft">
              <img :src="pay1" alt="" class="pay1" />
              可用余额
            </div>
            <div class="payRight">
              <span class="payUnit">¥</span>
              {{ walletBalance }}
            </div>
          </div>
          <div class="funding-overview-list">
            <div
              v-for="item in mediaFundsList"
              :key="item.type"
              class="funding-overview-item"
            >
              <div class="funding-overview-item-title">
                <img :src="item.icon" alt="" class="zjIcon" />
                <div class="funding-overview-item-text">{{ item.title }}</div>
              </div>
              <div class="funding-overview-item-balance">
                <span>{{ item.currency }}</span>
                {{ item.balance }}
                <img
                  :src="pay2"
                  alt=""
                  class="pay2"
                  @click="goUrl(item.path)"
                />
              </div>
            </div>
          </div>
          <div class="lookAll" @click="goUrl('/ads/fundManage/purse')">
            查看全部>>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import { useRouter } from "vue-router";
import home_banner1 from "@/assets/images/home/<USER>";
import banner1 from "@/assets/images/home/<USER>";
import banner2 from "@/assets/images/home/<USER>";
import tool_khsq from "@/assets/images/home/<USER>";
import tool_zhyj from "@/assets/images/home/<USER>";
import tool_zhlb from "@/assets/images/home/<USER>";
import tool_xhbg from "@/assets/images/home/<USER>";
import tuijian1 from "@/assets/images/home/<USER>";
import tuijian2 from "@/assets/images/home/<USER>";
import tuijian3 from "@/assets/images/home/<USER>";
import iconMeta from "@/assets/images/home/<USER>";
import iconGoogle from "@/assets/images/home/<USER>";
import iconTikTok from "@/assets/images/home/<USER>";
import iconBing from "@/assets/images/home/<USER>";
import iconYandex from "@/assets/images/home/<USER>";
import pay1 from "@/assets/images/home/<USER>";
import pay2 from "@/assets/images/home/<USER>";
import * as echarts from "echarts";
import { EMediaType } from "@/views/workbench/enum";
import {
  getAccountStatistics,
  getCapitalOverview,
  getConsumptionOverview,
} from "@/api/workbench";
import { useRoute } from "vue-router";
import usePermissionStore from "@/store/modules/permission";
import { ElMessage } from "element-plus";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const permissionStore = usePermissionStore();
const currentRoute = useRoute();
// router
const router = useRouter();
const companyName = computed(() => {
  const data = JSON.parse(localStorage.getItem("currentSelectTenant"));
  console.log(data);
  return data?.name ? data.name : "";
})
// banner 列表
const bannerList = ref([
  {
    id: 1,
    img: banner1,
  },
  {
    id: 2,
    img: banner2,
  },
]);
let toolList = ref([
  {
    toolName: "开户申请",
    toolImg: tool_khsq,
    path: "/ads/openAccount/account-open-apply",
  },
  {
    toolName: "账户预警",
    toolImg: tool_zhyj,
    path: "/ads/accountManage/account-list/tactics",
  },
  {
    toolName: "账户列表",
    toolImg: tool_zhlb,
    path: "/ads/accountManage/account-list",
  },
  {
    toolName: "消耗报告",
    toolImg: tool_xhbg,
    path: "/ads/dataReport/data-consumption",
  },
]);
let recommendedList = ref([
  {
    recommendedTitle: "全渠道海外营销解决服务",
    recommendedDesc: "一站式营销解决方案，省心专业",
    recommendedImg: tuijian1,
    recommendedUrl: "https://www.ceglobal.cn/marketing.html",
  },
  {
    recommendedTitle: "新一代全渠道增长营销CRM",
    recommendedDesc: "一键同步所有询盘到集客助手（免费）",
    recommendedImg: tuijian2,
    recommendedUrl: "https://www.ceglobal.cn/clientcrm.html",
  },
  {
    recommendedTitle: "社媒营销管家",
    recommendedDesc: "一式社媒营销好帮手，私域营销",
    recommendedImg: tuijian3,
    recommendedUrl: "https://www.ceglobal.cn/export.html",
  },
]);
// 渠道公司名称
const merchantName = ref("");
const updateTime = ref("");

// 广告账户数据
const accountData = ref<any>({});

// 媒体开户配置列表
const conferenceMediaList = ref([
  {
    type: EMediaType.META,
    icon: iconMeta,
    title: "Meta",
    desc: "全球最大的社交平台，亦是全球最佳精准的营销平台之一。依托24亿用户的庞大全球化社群发展你的业务。",
    descTool:
      "全球最大的社交平台，亦是全球<br />最佳精准的营销平台之一。依托<br />24亿用户的庞大全球化社群发展<br />你的业务。",
  },
  {
    type: EMediaType.GOOGLE,
    icon: iconGoogle,
    title: "Google Ads",
    desc: "全球最大的搜索引擎公司，拥有近30亿全球最大的搜索引擎公司，拥有近30亿月活用户，在客户搜索您的产品或服务的那一刻，恰当其时展示广告。",
    descTool:
      "全球最大的搜索引擎公司，拥有<br />近30亿全球最大的搜索引擎公司，<br />拥有近30亿月活用户，在客户搜<br />索您的产品或服务的那一刻，恰<br />当其时展示广告。",
  },
  {
    type: EMediaType.TIKTOK,
    icon: iconTikTok,
    title: "Tiktok",
    desc: "抖音国际版，风靡全球短视频社交平台，海量新潮年轻用户，通过强导流强曝光的短视频内容带来更高转化率。",
    descTool:
      "抖音国际版，风靡全球短视频社<br />交平台，海量新潮年轻用户，通<br />过强导流强曝光的短视频内容带<br />来更高转化率。",
  },
  {
    type: EMediaType.BING,
    icon: iconBing,
    title: "Bing ads",
    desc: "全球第二大搜索引擎 ，覆盖全球36个国家和地区，拥有超过6亿高购买力用户，购买力指数比平均联网用户高101%。",
    descTool:
      "全球第二大搜索引擎 ，覆盖全球<br />36个国家和地区，拥有超过6亿<br />高购买力用户，购买力指数比平<br />均联网用户高101%。",
  },
]);

// 消耗数据概览媒体
const dataSourceVal = ref<number>(1);
// 消耗数据概览媒体配置
const dataSourceOptions = ref([
  {
    value: EMediaType.GOOGLE,
    label: "Google",
  },
  {
    value: EMediaType.META,
    label: "Mete",
  },
  {
    value: EMediaType.TIKTOK,
    label: "Tiktok",
  },
  {
    value: EMediaType.BING,
    label: "Bing ads",
  },
  {
    value: EMediaType.YANDEX,
    label: "Yandex",
  },
  {
    value: EMediaType.LINKEDIN,
    label: "LinkedIn",
  },
]);
// 消耗数据概览日期
const dataDateVal = ref("");
// 开始时间 - 结束时间
const startTime = ref("");
const endTime = ref("");
// 消耗数据概览图标配置
const chartOption = ref({
  grid: {
    top: "10",
    left: "50",
    width: "96%",
    height: "85%",
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: "#000000a6",
    },
    axisLine: {
      show: true, // 显示坐标轴线
      lineStyle: {
        type: "solid", // 设置为实线
        color: "#DBDBDB",
        width: 2,
      },
    },
  },
  yAxis: {
    type: "value",
    axisLabel: {
      color: "#000000a6",
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: "dashed", // 网格线设置为实线
        color: "#ddd",
      },
    },
  },
  series: [
    {
      data: [], // 数据源
      type: "line", // 折线图
      symbol: "none", // 去掉折线上的点
      lineStyle: {
        color: "#2893FF",
      },
    },
  ],
});

// 钱包余额
const walletBalance = ref("0.00");
// 资金概览媒体列表
const mediaFundsList = ref([
  {
    icon: iconMeta,
    type: EMediaType.META,
    title: "Meta",
    currency: "$",
    balance: "0.00",
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.META}`,
  },
  {
    icon: iconGoogle,
    type: EMediaType.GOOGLE,
    title: "Google",
    currency: "¥",
    balance: "0.00",
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.GOOGLE}`,
  },
  {
    icon: iconTikTok,
    type: EMediaType.TIKTOK,
    title: "Tiktok",
    currency: "$",
    balance: "0.00",
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.TIKTOK}`,
  },
  {
    icon: iconYandex,
    type: EMediaType.YANDEX,
    title: "Yandex",
    currency: "$",
    balance: "0.00",
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.YANDEX}`,
  },
]);
// 判断权限
const canGo = () => {
  if (
    permissionStore?.buttonAuth?.Workbench?.indexOf("WorkbenchOperation") != -1
  ) {
    return true;
  } else {
    // 提示没有权限
    ElMessage({
      message: "没有权限",
      type: "warning",
    });
    return false;
  }
};
// 媒体开户
const openAccount = (type) => {
  goUrl("/ads/openAccount/account-open-apply?mediaType=" + type);
};

// 媒体来源切换
const mediaChange = () => {
  getConsumptionDataOverview();
};

// 日期禁用限制
const disabledDate = (date) => {
  // 当前时间
  const nowDate = new Date().getTime();
  // 当前时间减掉一天
  const formerlyDate = nowDate - 24 * 60 * 60 * 1000;
  return date && date.valueOf() > formerlyDate;
  // console.log(date.valueOf(), "data", nowDate);
};

// 日期切换
const dateChange = (date) => {
  // if (date) {
  //   validateTimeRange(date);
  // }
  if (dataDateVal.value) {
    startTime.value = dataDateVal.value[0];
    endTime.value = dataDateVal.value[1];
  } else {
    startTime.value = "";
    endTime.value = "";
  }
  getConsumptionDataOverview();
};

// 获取广告账户数据
const getAccountStatisticsData = () => {
  getAccountStatistics()
    .then((res) => {
      if (res && res.code === "200" && res.data) {
        accountData.value = res.data;
      }
    })
    .catch((err) => {
      console.log(err, "err");
    });
};

// 获取消耗数据概览
const getConsumptionDataOverview = () => {
  getConsumptionOverview({
    mediumType: dataSourceVal.value,
    startTime: startTime.value,
    endTime: endTime.value,
  })
    .then((res) => {
      if (res && res.code === "200" && res.data) {
        const reportDataDayResList = res.data.reportDataDayResList;
        if (
          Array.isArray(reportDataDayResList) &&
          reportDataDayResList.length
        ) {
          chartOption.value.xAxis.data = reportDataDayResList.map(
            (item) => item.reportDate
          );
          chartOption.value.series[0].data = reportDataDayResList.map(
            (item) => item.cost
          );
        } else {
          chartOption.value.xAxis.data = [];
          chartOption.value.series[0].data = [];
        }
        renderDataChart();
      }
    })
    .catch((err) => {
      console.log(err, "err");
    });
};

// 获取资金概览
const getMediaFundsOverview = () => {
  getCapitalOverview()
    .then((res) => {
      if (res && res.code === "200" && res.data) {
        walletBalance.value = res.data.balance.toFixed(2);
        const accountBalances = res.data.accountBalances;
        mediaFundsList.value.forEach((item) => {
          item.balance = accountBalances[item.type].remainAmount;
        });
        merchantName.value = res.data.merchantName;
        updateTime.value = res.data.updateTime;
      }
    })
    .catch((err) => {
      console.log(err, "err");
    });
};

// 渲染折线图数据
const renderDataChart = () => {
  const chartDom = document.querySelector(".data-overview-chart") as any;
  const myChart = echarts.init(chartDom);
  chartOption.value && myChart.setOption(chartOption.value);
};
// 跳转详情
const goDetails = () => {
  if (!canGo()) {
    return;
  }
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: "/ads/workbench/workbenchDetails",
      name: "ads",
      parentPath: "/ads/workbench",
      isChild: true,
    });
  }
};
// 跳转链接
const goUrl = (path: string) => {
  if (!canGo()) {
    return;
  }
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: path,
      name: "ads",
      parentPath: "/ads/workbench",
      isChild: false,
    });
  }
};
// 打开链接
const openUrl = (url: any) => {
  // 新页面打开
  window.open(url, "_blank");
};
onMounted(() => {
  console.log(currentRoute.name, "currentRoute");
  if (currentRoute.name && currentRoute.name === "Workbench") {
    getAccountStatisticsData();
    getConsumptionDataOverview();
    getMediaFundsOverview();
  }
});
</script>

<style lang="scss" scoped>
.dashboard-home {
  width: 100%;
  height: auto;
  min-width: 1228px;
  padding-bottom: 20px;
  .advertising-information {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .mainLeft {
      display: flex;
      flex-direction: column;
      .banner {
        border-radius: 8px;
        &.co-existence {
          margin-right: 20px;
        }
        img {
          width: 100%;
          height: 240px;
          border-radius: 8px;
        }
      }
      .media-account-opening {
        border-radius: 8px;
        background: #fff;
        margin-top: 20px;
        &.co-existence {
          margin-right: 21px;
        }
        .media-account-opening-title {
          color: #333333;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 16px;
          margin-top: 18px;
          padding: 0 20px;
          position: relative;
          display: flex;
          justify-content: space-between;
          &::after {
            content: "";
            position: absolute;
            left: 0px;
            top: 0px;
            width: 4px;
            height: 24px;
            border-radius: 0 2px 2px 0;
            background: var(--el-color-primary);
          }
          .goMore {
            color: var(--el-color-primary);
            font-size: 14px;
            font-weight: 400;
            font-family: "PingFang SC";
            cursor: pointer;
            text-decoration: underline;
          }
        }
        .media-account-opening-list {
          display: flex;
          flex-wrap: wrap;
          padding: 0 20px;
          justify-content: space-between;
          .media-account-opening-item {
            position: relative;
            width: calc(25% - 30px);
            height: 240px;
            border-radius: 8px;
            background: #f5f5fa;
            box-shadow: 0 2px 7px 0 #d6dae080;
            margin-bottom: 20px;
            padding: 20px;
            .media-account-opening-item-title {
              display: flex;
              align-items: center;
              height: 48px;
              .applyImg {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                margin-right: 8px;
              }
              .apply-title {
                color: #333333;
                font-size: 16px;
                font-weight: 600;
                font-family: "PingFang SC";
              }
            }
            .media-account-opening-item-desc {
              margin-top: 10px;
              line-height: 20px;
              display: -webkit-box;
              -webkit-line-clamp: 4; /* 显示的最大行数 */
              line-clamp: 4; /* 显示的最大行数 */
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              // height: 100px;
              color: #333333;
              font-size: 12px;
              font-weight: 400;
              font-family: "PingFang SC";
            }
            .opening-btn {
              position: absolute;
              bottom: 24px;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }
      .data-overview {
        height: 448px;
        border-radius: 8px;
        background: #ffffff;
        border-radius: 8px;
        margin-top: 20px;
        &.co-existence {
          margin-right: 20px;
        }
        .data-overview-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #333333;
          font-size: 16px;
          font-weight: 600;
          padding: 18px 20px 16px;
          border-bottom: 1px solid #f0f0f0;
          position: relative;
          &::after {
            content: "";
            position: absolute;
            left: 0px;
            top: 22px;
            width: 4px;
            height: 24px;
            border-radius: 0 2px 2px 0;
            background: var(--el-color-primary);
          }
        }
        .data-overview-screen {
          :deep(.el-select) {
            width: 180px;
            margin-right: 16px;
          }
          :deep(.el-range-editor) {
            width: 294px;
          }
        }
        .data-overview-chart {
          width: 100%;
          height: 340px;
          margin-top: 22px;
          padding: 0 20px;
        }
      }
    }
    .mainRight {
      display: flex;
      flex-direction: column;
      width: 416px;
      .company-details {
        width: 416px;
        height: 240px;
        border-radius: 8px;
        position: relative;
        padding: 20px 0 20px 20px;
        border-radius: 8px;
        background: #ffffff;
        box-sizing: border-box;
        .company-title {
          width: 100%;
          height: 22px;
          color: #333333;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
        }
        .maturity-time {
          width: 100%;
          height: 20px;
          color: #999999;
          font-size: 13.8px;
          font-weight: 400;
        }
        .company-data-info {
          margin-top: 20px;
          display: flex;
          flex-wrap: wrap;
          .company-data-item {
            width: 33.3%;
            height: 48px;
            margin-bottom: 16px;
            .company-data-item-title {
              color: #666666;
              font-size: 14px;
              font-weight: 400;
              margin-bottom: 4px;
            }
            .company-data-item-value {
              color: #333333;
              font-size: 20px;
              font-weight: 700;
              cursor: pointer;
              // hover
              &:hover {
                color: var(--el-color-primary);
              }
            }
          }
        }
        .level-tag {
          position: absolute;
          top: 21px;
          right: 0;
          width: 96px;
          height: 35px;
          padding: 0 5px 0 13px;
          opacity: 1;
          border: 1px solid #fecd91;
          border-right: none;
          background: linear-gradient(90deg, #fffaf1 0%, #ffeac6 100%);
          border-radius: 20px 0 0 20px;
          display: flex;
          align-items: center;
          .level-text {
            color: #ea8c18;
            font-size: 14px;
            font-weight: 600;
          }
        }
      }
      .tool {
        width: 416px;
        height: 150px;
        margin: 20px 0;
        border-radius: 8px;
        background: #ffffff;
        padding: 20px 0;
        .toolTitle {
          color: #333333;
          font-size: 16px;
          font-weight: 600;
          padding-left: 20px;
          position: relative;
          &::after {
            content: "";
            position: absolute;
            left: 0px;
            top: 0px;
            width: 4px;
            height: 24px;
            border-radius: 0 2px 2px 0;
            background: var(--el-color-primary);
          }
        }
        .toolList {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20px 30px;
          .toolRow {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            .toolRowImg {
              width: 40px;
              height: 40px;
              border-radius: 8px;
            }
            .toolRowLable {
              color: #333333;
              font-size: 14px;
              font-weight: 600;
              font-family: "PingFang SC";
              margin-top: 8px;
            }
          }
        }
      }
      .recommended {
        height: 231px;
        margin-top: 0;
        .recommendedList {
          padding: 0 20px;
          box-sizing: border-box;
          .recommendedRow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 15px;
            cursor: pointer;
            .recommendedImg {
              width: 30px;
              height: 30px;
            }
            .recommendedRowMain {
              width: calc(100% - 40px);
              .recommendedRowMainTitle {
                width: 100%;
                color: #333333;
                font-size: 14px;
                font-weight: 600;
                font-family: "PingFang SC";
              }
              .recommendedRowMainDesc {
                width: 100%;
                color: #333333;
                font-size: 12px;
                font-weight: 400;
                font-family: "PingFang SC";
              }
            }
          }
        }
      }
      .funding-overview {
        flex: 1;
        height: 348px;
        padding: 20px 0;
        border-radius: 8px;
        background: #ffffff;
        .funding-overview-title {
          display: flex;
          justify-content: space-between;
          color: #333333;
          font-size: 16px;
          font-weight: 600;
          padding: 0 20px 15px;
          border-bottom: 1px solid #f0f0f0;
          position: relative;
          &::after {
            content: "";
            position: absolute;
            left: 0px;
            top: 0px;
            width: 4px;
            height: 24px;
            border-radius: 0 2px 2px 0;
            background: var(--el-color-primary);
          }
          .titleTime {
            color: #374151;
            font-size: 12px;
            font-weight: 400;
            font-family: "PingFang SC";
          }
        }
        .pay {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20px 20px 10px;
          .payLeft {
            display: flex;
            align-items: center;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            font-family: "PingFang SC";
            .pay1 {
              width: 22px;
              height: 22px;
              margin-right: 8px;
            }
          }
          .payRight {
            color: #111827;
            font-size: 18px;
            font-weight: 600;
            font-family: "PingFang SC";
            .payUnit {
              margin-right: 2px;
            }
          }
        }
        .funding-overview-list {
          padding: 10px 20px 20px;
          .funding-overview-item {
            width: 100%;
            height: 36px;
            padding: 0 21px 0 10px;
            border-radius: 8px;
            background: #f3f5f7;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .funding-overview-item-title {
              display: flex;
              align-items: center;
              .zjIcon {
                width: 24px;
                height: 24px;
                margin-right: 8px;
              }
              .funding-overview-item-text {
                color: #333333;
                font-size: 12px;
                font-weight: 500;
              }
            }
            .funding-overview-item-balance {
              color: #333333;
              font-size: 14px;
              font-weight: 700;
              display: flex;
              align-items: center;
              span {
                color: #333333;
                font-size: 10px;
                font-weight: 400;
                margin-right: 2px;
              }
              .pay2 {
                width: 19px;
                height: 19px;
                margin-left: 4px;
                cursor: pointer;
              }
            }
          }
        }
        .lookAll {
          width: 100%;
          text-align: center;
          color: var(--el-color-primary);
          font-size: 14px;
          font-weight: 400;
          font-family: "PingFang SC";
          cursor: pointer;
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
