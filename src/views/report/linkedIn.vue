<template>
  <div class="zq-report-body" id="linkedIn_pdf">
    <header>
      <img src="@/assets/img/linkedIn.png" class="linkedIn-logo" alt="" />
      <h1>linkedIn 广告账户报告</h1>
      <img src="@/assets/img/zq-logo.png" class="zq-logo" alt="" />
    </header>
    <div class="zq-report-header">
      <div>
        <label>公司名称</label><br />
        <strong>{{ reportTableData?.task?.thirdCustomerName }}</strong>
      </div>
      <div>
        <label>广告账户ID</label><br />
        <strong>{{ reportTableData?.task?.thirdCustomerId }}</strong>
      </div>
      <div>
        <label>统计周期</label><br />
        <strong>{{ reportTableData?.task?.cycle }}</strong>
      </div>
    </div>
    <div class="zq-report-first">
      <h2>一、帐户效果</h2>
      <el-table
        :data="[reportTableData?.account]"
        border
        class="zq-tikTok-report_table"
      >
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="CPM(¥)" prop="cpm" />
        <el-table-column label="CPC(¥)" prop="cpc" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="总参与次数" prop="totalEngagements" />
        <el-table-column label="单线索成本(¥)" prop="costPerQualifiedLead" />
        <el-table-column label="总消耗(¥)" prop="totalCost" />
        <el-table-column label="平均日消耗(¥)" prop="avgDayCost" />
      </el-table>
    </div>
    <div class="zq-report-second page">
      <h2>二、广告系列效果</h2>
      <el-table
        :data="reportTableData?.campaignList"
        border
        class="zq-tikTok-report_table"
      >
        <el-table-column label="广告系列" prop="campaignName" />
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="CPM(¥)" prop="cpm" />
        <el-table-column label="CPC(¥)" prop="cpc" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="总参与次数" prop="totalEngagements" />
        <el-table-column label="单线索成本(¥)" prop="costPerQualifiedLead" />
        <el-table-column label="总消耗(¥)" prop="cost" />
      </el-table>
    </div>
    <div class="zq-report-third page">
      <h2>三、每日花费明细</h2>
      <div ref="chartBarAndLine" style="width: 100%; height: 500px"></div>
      <el-table
        :data="reportTableData?.dayList"
        border
        class="zq-tikTok-report_table"
      >
        <el-table-column label="日期" prop="reportDate" />
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="CPM(¥)" prop="cpm" />
        <el-table-column label="CPC(¥)" prop="cpc" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="总参与次数" prop="totalEngagements" />
        <el-table-column label="单线索成本(¥)" prop="costPerQualifiedLead" />
        <el-table-column label="总消耗(¥)" prop="cost" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, watch } from "vue";
import { getLookReport } from "@/api/autoReportApi";
import ExportJsonExcel from "js-export-excel";
import { useRoute } from "vue-router";
import * as echarts from "echarts";
import { ElMessage, ElLoading } from "element-plus";
const lineList = ref([]);
const barList = ref([]);
const dateList = ref([]);
const chartBarAndLine = ref<any>(null);
const route = useRoute();
const reportTableData = ref<any>({
  dayList: [],
});
const props = defineProps({
  taskId: {
    type: String,
    default: "",
  },
  typeName: {
    type: String,
    default: "pdf",
  },
  downloadExcelName: {
    type: [Object, null],
    default: null,
  },
});
watch(
  () => props.taskId,
  (newVal) => {
    if (newVal) {
      getReportTableData(newVal, props.typeName);
    } else {
      getReportTableData("", "");
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
async function getReportTableData(taskId: string, typeName: string) {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: taskId && typeName == "excel" ? "正在导出excel..." : "加载中...",
      background: "rgba(0, 0, 0, 0.7)",
    });

    const params = {
      reportTaskId: route.query.taskId || taskId,
    };

    const res: any = await getLookReport(params);
    loading.close();

    if (res?.code == 200) {
      reportTableData.value = res.data;

      // 排序数据
      const { dayList, countryList, keyWorldList, campaignList } =
        reportTableData.value || {};

      if (dayList?.length) {
        dayList.sort(
          (a: any, b: any) =>
            new Date(a.reportDate).getTime() - new Date(b.reportDate).getTime()
        );
        barList.value = dayList.map((item: any) => item.cost);
        lineList.value = dayList.map((item: any) => item.impressions);
        dateList.value = dayList.map((item: any) => item.reportDate);
      }

      if (countryList?.length) {
        countryList.sort((a, b) => b.cost - a.cost);
      }

      if (keyWorldList?.length) {
        keyWorldList.sort((a, b) => b.cost - a.cost);
      }

      if (campaignList?.length) {
        campaignList.sort((a, b) => b.cost - a.cost);
      }
      // 初始化图表
      initBarAndLine();

      // 导出Excel
      if (taskId && typeName == "excel") {
        downloadEXCEL();
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.log(error);
    // 注释掉的 Sentry 捕获代码
  }
}
const emit = defineEmits(["colseExcel"]);
// 下载报表 excel
const downloadEXCEL = () => {
  const loading = ElLoading.service({
    lock: true,
    text: "下载中，请稍等！",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    const reportType = {
      1: "月报",
      2: "周报",
      3: "自定义",
    };

    const data = props.downloadExcelName;
    const fileName = data
      ? `${data.thirdCustomerName}-TikTok-${reportType[data.reportType]}-报表`
      : "TikTok-报表";

    const option = {
      fileName,
      datas: [
        {
          sheetData: [reportTableData.value?.account],
          sheetName: "帐户效果数据",
          sheetFilter: [
            "impressions",
            "clicks",
            "cpm",
            "cpc",
            "ctr",
            "conversions",
            "convCost",
            "totalCost",
            "avgDayCost",
          ],
          sheetHeader: [
            "展示量",
            "点击量",
            "CPM(¥)",
            "CPC(¥)",
            "点击率",
            "转化量",
            "CPA(¥)",
            "总消耗(¥)",
            "平均日消耗(¥)",
          ],
        },
        {
          sheetData: reportTableData.value?.campaignList,
          sheetName: "广告系列效果数据",
          // 待定
          sheetFilter: [
            "campaignName",
            "impressions",
            "clicks",
            "cpm",
            "cpc",
            "ctr",
            "conversions",
            "convCost",
            "cost",
          ],
          sheetHeader: [
            "广告系列",
            "展示量",
            "点击量",
            "CPM(¥)",
            "CPC(¥)",
            "点击率",
            "转化量",
            "CPA(¥)",
            "总费用(¥)",
          ],
        },
        {
          sheetData: reportTableData.value?.dayList,
          sheetName: "每日花费明细",
          // 待定
          sheetFilter: [
            "reportDate",
            "impressions",
            "clicks",
            "cpm",
            "cpc",
            "ctr",
            "convCost",
            "conversions",
            "cost",
          ],
          sheetHeader: [
            "日期",
            "展示量",
            "点击量",
            "CPM(¥)",
            "CPC(¥)",
            "点击率(%)",
            "CPA(¥)",
            "转化量",
            "总消耗(¥)",
          ],
        },
      ],
    };

    const toExcel = new ExportJsonExcel(option);
    toExcel.saveExcel();
    loading.close();
    emit("colseExcel");
  } catch (error) {
    console.log(error);
    loading.close();
    emit("colseExcel");
    ElMessage.error("导出失败！");
  }
};
function initBarAndLine() {
  // 初始化ECharts实例
  const chart = echarts.init(chartBarAndLine.value);

  // 图表配置
  const option = {
    xAxis: {
      type: "category",
      data: dateList.value,
    },
    yAxis: {
      type: "value",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["总费用", "成效"],
      align: "left",
    },
    color: ["rgb(195, 53, 49)", "rgb(46, 69, 83)", "rgb(96, 160, 169)"],
    series: [
      {
        name: "总费用",
        data: barList.value,
        type: "line",
      },
      {
        name: "成效",
        data: lineList.value,
        type: "line",
      },
    ],
  };

  // 使用设置好的选项显示图表
  chart.setOption(option);
}
</script>

<style lang="scss" scoped>
.zq-report-body {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: rgb(247, 247, 247);
}

header {
  width: 1300px;
  height: 100px;
  margin: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  box-sizing: border-box;
  padding: 0 20px;

  .zq-logo {
    width: 110px;
    height: auto;
  }

  .linkedIn-logo {
    width: 110px;
    height: auto;
  }

  h1 {
    margin-left: -150px;
  }
}

thead {
  background: rgb(0, 112, 192) !important;
}

.zq-report-header {
  width: 1300px;
  margin: auto;
  display: flex;
  justify-content: space-around;
  background: #ffffff;
  padding: 20px 40px;
  box-sizing: border-box;

  div {
    line-height: 30px;
    text-align: center;
    label {
      font-size: 16px;
      color: #333333;
      font-weight: 600;
    }
    strong {
      font-size: 14px;
      color: #333333;
      font-weight: 400;
    }
  }
}

.zq-report-first,
.zq-report-second,
.zq-report-third,
.zq-report-four,
.zq-report-five {
  width: 1300px;
  background: #ffffff;
  box-sizing: border-box;
  padding: 20px;
  margin-bottom: 20px;

  h2 {
    padding: 15px 0;
    font-size: 20px;
  }

  h4 {
    margin: 10px;
    text-indent: 20px;
    font-size: 16px;
    color: #666666;
    padding: 15px;
  }
}

.zq-report-five {
  margin-bottom: 30px;
}

.page {
  height: auto;
}

.zq-report-auto_location {
  font-size: 12px;
  color: #666666;
  margin-bottom: 12px;
}

::v-deep(.zq-tikTok-report_table th) {
  background-color: #477fff !important;
  color: #ffffff;
  margin: auto;
}
</style>
