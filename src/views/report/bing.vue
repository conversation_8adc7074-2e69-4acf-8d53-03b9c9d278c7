<template>
  <div class="zq-report-body" id="bing_pdf">
    <header>
      <img src="@/assets/img/bing.png" class="bing-logo" />
      <h1>Bing 广告账户报告</h1>
      <img src="@/assets/img/zq-logo.png" class="zq-logo" />
    </header>
    <div class="zq-report-header">
      <div>
        <label>公司名称</label><br />
        <strong>{{ reportTableData?.task?.companyName }}</strong>
      </div>
      <div>
        <label>广告账户ID</label><br />
        <strong>{{ reportTableData?.task?.thirdCustomerId }}</strong>
      </div>
      <div>
        <label>统计周期</label><br />
        <strong>{{ reportTableData?.task?.cycle }}</strong>
      </div>
    </div>
    <div class="zq-report-first">
      <h2>一、帐户效果</h2>
      <el-table
        :data="[reportTableData?.account]"
        border
        class="zq-bing-report_table"
      >
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="平均CPC($)" prop="cpc" />
        <el-table-column label="平均CPM($)" prop="cpm" />
        <el-table-column label="最高展示率(%)" prop="topImpressionRatePercent" />
        <el-table-column
            label="绝对顶部展示率(%)"
            prop="absoluteTopImpressionRatePercent"
            width="180"
        />
        <el-table-column label="平均日消耗($)" prop="avgDayCost" />
        <el-table-column label="总消耗($)" prop="totalCost" />
      </el-table>
    </div>
    <div class="zq-report-second page">
      <h2>二、广告系列效果</h2>
      <el-table
        :data="reportTableData?.campaignList"
        border
        class="zq-bing-report_table"
      >
        <el-table-column label="广告系列" prop="campaignName" />
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="平均CPC($)" prop="cpc" />
        <el-table-column label="平均CPM($)" prop="cpm" />
        <el-table-column label="最高展示率(%)" prop="topImpressionRatePercent" />
        <el-table-column
          label="绝对顶部展示率(%)"
          prop="absoluteTopImpressionRatePercent"
          width="180"
        />
        <el-table-column label="总消耗($)" prop="cost" />
      </el-table>
    </div>
    <div class="zq-report-third page">
      <h2>三、每日花费明细</h2>
      <div ref="chartLine" class="zq-charts"></div>
      <el-table
        :data="reportTableData?.dayList"
        border
        class="zq-bing-report_table"
      >
        <el-table-column label="日期" prop="reportDate" />
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="平均CPM($)" prop="cpm" />
        <el-table-column label="平均CPC($)" prop="cpc" />
        <el-table-column label="最高展示率(%)" prop="topImpressionRatePercent" />
        <el-table-column
          label="绝对顶部展示率(%)"
          prop="absoluteTopImpressionRatePercent"
          width="180"
        />
        <el-table-column label="总消耗($)" prop="cost" />
      </el-table>
    </div>
    <div class="zq-report-four page">
      <h2>四、TOP10地理位置数据</h2>
      <div class="zq-charts" ref="chartBarAndLine"></div>
      <el-table
        :data="reportTableData?.countryList?.slice(0, 10)"
        border
        class="zq-bing-report_table"
      >
        <el-table-column label="国家" prop="positionName" />
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="平均CPC($)" prop="cpc" />
        <el-table-column label="平均CPM($)" prop="cpm" />
        <el-table-column label="最高展示率(%)" prop="topImpressionRatePercent" />
        <el-table-column
          label="绝对顶部展示率(%)"
          prop="absoluteTopImpressionRatePercent"
          width="180"
        />
        <el-table-column label="总消耗($)" prop="cost" />
      </el-table>
    </div>
    <div class="zq-report-five">
      <h2>五、TOP10关键词数据</h2>
      <div ref="chartBar" class="zq-charts"></div>
      <el-table :data="keywordTopTen" border class="zq-bing-report_table">
        <el-table-column label="关键词" prop="keyWords" />
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="平均CPC($)" prop="cpc" />
        <el-table-column label="平均CPM($)" prop="cpm" />
        <el-table-column label="最高展示率(%)" prop="topImpressionRatePercent" />
        <el-table-column
          label="绝对顶部展示率(%)"
          prop="absoluteTopImpressionRatePercent"
          width="180"
        />
        <el-table-column label="总消耗($)" prop="cost" />
      </el-table>
    </div>
    <div class="zq-report-five">
      <h2>六、所有关键词数据</h2>
      <el-table
          :data="reportTableData?.keyworkdsList"
          border
          class="zq-bing-report_table"
      >
        <el-table-column label="关键词" prop="keyWords" />
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />
        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="平均CPC($)" prop="cpc" />
        <el-table-column label="平均CPM($)" prop="cpm" />
        <el-table-column label="最高展示率(%)" prop="topImpressionRatePercent" />
        <el-table-column
            label="绝对顶部展示率(%)"
            prop="absoluteTopImpressionRatePercent"
            width="180"
        />
        <el-table-column label="总消耗($)" prop="cost" />
      </el-table>
    </div>
    <div class="zq-report-five">
      <h2>七、所有地理位置数据</h2>
      <el-table
          :data="reportTableData?.countryList"
          border
          class="zq-bing-report_table"
      >
        <el-table-column label="国家" prop="positionName" />
        <el-table-column label="展示量" prop="impressions" />
        <el-table-column label="点击量" prop="clicks" />

        <el-table-column label="点击率(%)" prop="ctr" />
        <el-table-column label="平均CPC($)" prop="cpc" />
        <el-table-column label="平均CPM($)" prop="cpm" />
        <el-table-column label="最高展示率(%)" prop="topImpressionRatePercent" />
        <el-table-column
            label="绝对顶部展示率(%)"
            prop="absoluteTopImpressionRatePercent"
            width="180"
        />
        <el-table-column label="总消耗($)" prop="cost" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, watch } from "vue";
import { getLookReport } from "@/api/autoReportApi";
import ExportJsonExcel from "js-export-excel";
import { useRoute } from "vue-router";
import * as echarts from "echarts";
import { ElMessage, ElLoading } from "element-plus";
const locationTopTen = ref([]);
const dateList = ref([]);
const countryList = ref([]);
const manList = ref([]);
const womanList = ref([]);
const ageRangeList = ref([]);
const chartBarAndLine = ref<any>(null);
const route = useRoute();
const unknownList = ref([]);
const allFourData = ref([]);
const clickNumber = ref([]);
const costNumber = ref([]);
const keywordTopTen = ref([]);
const geographicLocationBarList = ref([]);
const geographicLocationLineList = ref([]);
const chartLine = ref<any>(null);
const chartBar = ref<any>(null);
const keyWordsNameList = ref([]);
const keyWordsImpressionsList = ref([]);
const keyWordsClicksList = ref([]);
const reportTableData = ref<any>({
  dayList: [],
});
const props = defineProps({
  taskId: {
    type: String,
    default: "",
  },
  typeName: {
    type: String,
    default: "pdf",
  },
  downloadExcelName: {
    type: Object || null,
    default: null,
  },
});
watch(
  () => props.taskId,
  (newVal) => {
    if (newVal) {
      getReportTableData(newVal, props.typeName);
    } else {
      getReportTableData("", "");
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
async function getReportTableData(taskId: string, typeName: string) {
  console.log(taskId);
  try {
    const loading = ElLoading.service({
      lock: true,
      text: taskId && typeName == "excel" ? "正在导出excel..." : "加载中...",
      background: "rgba(0, 0, 0, 0.7)",
    });
    const params = {
      reportTaskId: route.query.taskId || taskId,
    };
    const res: any = await getLookReport(params);
    loading.close();
    if (res?.code == 200) {
      reportTableData.value = res.data;
      reportTableData.value?.dayList?.sort((a: any, b: any) => {
        return (
          new Date(a.reportDate).getTime() - new Date(b.reportDate).getTime()
        );
      });
      clickNumber.value = reportTableData.value?.dayList?.map((item: any) => {
        return item.clicks;
      });
      costNumber.value = reportTableData.value?.dayList?.map((item: any) => {
        return item.cost;
      });
      reportTableData.value?.countryList?.sort((a, b) => {
        return b.cost - a.cost;
      });
      reportTableData.value?.keyWorldList?.sort((a, b) => {
        return b.cost - a.cost;
      });
      reportTableData.value?.campaignList?.sort((a, b) => {
        return b.cost - a.cost;
      });
      dateList.value = reportTableData.value?.dayList?.map((item: any) => {
        return item.reportDate;
      });
      geographicLocationBarList.value = reportTableData.value?.countryList?.map(
          (item: any) => {
            return item.cost;
          },
      );
      geographicLocationLineList.value =
          reportTableData.value?.countryList?.map((item: any) => {
            return item.clicks;
          });
      countryList.value = reportTableData.value?.countryList?.map(
          (item: any) => {
            return item.positionName;
          },
      );
      // 关键词热度排名前十
      keywordTopTen.value = reportTableData.value?.keyworkdsList
        ?.sort((a: any, b: any) => {
          return b.cost - a.cost;
        })
        ?.slice(0, 10);
      keyWordsNameList.value = keywordTopTen.value?.map((item: any) => {
        return item.keyWords;
      });
      keyWordsImpressionsList.value = keywordTopTen.value?.map((item: any) => {
        return item.impressions;
      });
      keyWordsClicksList.value = keywordTopTen.value?.map((item: any) => {
        return item.clicks;
      });
      const list = reportTableData.value?.ageGenderList?.map((item) => {
        if (item.sexType == 1) {
          item.manType = "男性";
          item.manReach = item.reach;
        }
        if (item.sexType == 2) {
          item.womanType = "女性";
          item.womanReach = item.reach;
        }
        if (item.sexType == 3) {
          item.unknownType = "未知";
          item.unknownReach = item.reach;
        }
        delete item.reach;
        return { ...item };
      });
      var mergedArray = list.reduce(function (result, obj) {
        var target = result.find(function (item) {
          return item.ageRange === obj.ageRange;
        });

        if (target) {
          Object.assign(target, obj);
        } else {
          result.push(obj);
        }

        return result;
      }, []);

      console.log(mergedArray);

      console.log(mergedArray);
      allFourData.value = mergedArray;
      womanList.value = mergedArray?.map((item: any) => {
        return Number(item.womanReach);
      });
      manList.value = mergedArray?.map((item: any) => {
        return Number(item.manReach);
      });
      ageRangeList.value = mergedArray?.map((item: any) => {
        return item.ageRange;
      });
      unknownList.value = mergedArray?.map((item: any) => {
        return Number(item.unknownReach);
      });
      initBarAndLine();
      initBar();
      initLine();
      console.log(typeName);
      if (taskId && typeName == "excel") {
        downloadEXCEL();
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.log(error);
    // //globalThis.$sentry.captureMessage(error);
  }
}
const emit = defineEmits(["colseExcel"]);
// 下载报表 excel
const downloadEXCEL = () => {
  const loading = ElLoading.service({
    lock: true,
    text: "下载中，请稍等！",
    background: "rgba(0, 0, 0, 0.7)",
  });
  const reportType = {
    1: "月报",
    2: "周报",
    3: "自定义",
  };
  const data = props.downloadExcelName;
  var option = {
    fileName: `${data?.thirdCustomerName}-Facebook-${
      reportType[data.reportType]
    }-报表`,
    datas: [
      {
        sheetData: [reportTableData.value?.account],
        sheetName: "帐户效果数据",
        sheetFilter: [
          "impressions",
          "clicks",
          "ctr",
          "cpc",
          "cpm",
          "topImpressionRatePercent",
          "absoluteTopImpressionRatePercent",
          "totalCost",
          "avgDayCost",
        ],
        sheetHeader: [
          "展示量",
          "点击量",
          "点击率(%)",
          "平均CPC($)",
          "平均CPM($)",
          "最高展示率(%)",
          "绝对顶部展示率(%)",
          "总消耗($)",
          "平均日消耗($)",
        ],
      },
      {
        sheetData: reportTableData.value?.campaignList,
        sheetName: "广告系列效果数据",
        // 待定
        sheetFilter: [
          "campaignName",
          "impressions",
          "clicks",
          "ctr",
          "cpc",
          "cpm",
          "topImpressionRatePercent",
          "absoluteTopImpressionRatePercent",
          "cost",
        ],
        sheetHeader: [
          "广告系列",
          "展示量",
          "点击量",
          "点击率(%)",
          "平均CPC($)",
          "平均CPM($)",
          "最高展示率(%)",
          "绝对顶部展示率(%)",
          "总消耗($)",
        ],
      },
      {
        sheetData: reportTableData.value?.dayList,
        sheetName: "每日花费明细",
        // 待定
        sheetFilter: [
          "reportDate",
          "impressions",
          "clicks",
          "ctr",
          "cpc",
          "cpm",
          "topImpressionRatePercent",
          "absoluteTopImpressionRatePercent",
          "cost",
        ],
        sheetHeader: [
          "日期",
          "展示量",
          "点击量",
          "点击率(%)",
          "平均CPC($)",
          "平均CPM($)",
          "最高展示率(%)",
          "绝对顶部展示率(%)",
          "总消耗($)",
        ],
      },
      {
        sheetData: reportTableData.value?.keyworkdsList,
        sheetName: "所有关键词数据",
        // 待定
        sheetFilter: [
          "keyWords",
          "impressions",
          "clicks",
          "ctr",
          "cpc",
          "cpm",
          "topImpressionRatePercent",
          "absoluteTopImpressionRatePercent",
          "cost",
        ],
        sheetHeader: [
          "关键字",
          "展示量",
          "点击量",
          "点击率(%)",
          "平均CPC($)",
          "平均CPM($)",
          "最高展示率(%)",
          "绝对顶部展示率(%)",
          "总消耗($)",
        ],
      },
      {
        sheetData: reportTableData.value?.countryList,
        sheetName: "所有地理位置数据",
        // 待定
        sheetFilter: [
          "positionName",
          "impressions",
          "clicks",
          "ctr",
          "cpc",
          "cpm",
          "topImpressionRatePercent",
          "absoluteTopImpressionRatePercent",
          "cost",
        ],
        sheetHeader: [
          "国家",
          "展示量",
          "点击量",
          "点击率(%)",
          "平均CPC($)",
          "平均CPM($)",
          "最高展示率(%)",
          "绝对顶部展示率(%)",
          "总消耗($)",
        ],
      },
    ],
  };
  let toExcel = new ExportJsonExcel(option);
  toExcel.saveExcel();
  loading.close();
  emit("colseExcel");
};
function initBarAndLine() {
  // 初始化ECharts实例
  const chart = echarts.init(chartBarAndLine.value);
  // 设置图表的选项和数据
  const option = {
    xAxis: {
      type: "category",
      data: countryList.value?.slice(0, 10),
    },
    yAxis: {
      type: "value",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["总费用", "点击量"],
      align: "left",
    },
    color: ["rgb(195, 53, 49)", "rgb(46, 69, 83)", "rgb(96, 160, 169)"],
    series: [
      {
        name: "总费用",
        data: geographicLocationBarList.value?.slice(0, 10),
        type: "bar",
      },
      {
        name: "点击量",
        data: geographicLocationLineList.value?.slice(0, 10),
        type: "line",
      },
    ],
  };

  // 使用设置好的选项显示图表
  chart.setOption(option);
}
function initLine() {
  const chart = echarts.init(chartLine.value);
  const option = {
    xAxis: {
      type: "category",
      data: dateList.value,
    },
    yAxis: {
      type: "value",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["总费用", "点击次数"],
      align: "left",
    },
    color: ["#1C78B5", "#FFD528"],
    series: [
      {
        name: "总费用",
        data: costNumber.value,
        type: "line",
      },
      {
        name: "点击次数",
        data: clickNumber.value,
        type: "line",
      },
    ],
  };

  // 使用设置好的选项显示图表
  chart.setOption(option);
}
function initBar() {
  // 初始化ECharts实例
  const chart = echarts.init(chartBar.value);
  // 设置图表的选项和数据
  const option = {
    title: {
      // text: 'World Population'
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["展示次数", "点击次数"],
      align: "left",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      boundaryGap: [0, 0.01],
    },
    yAxis: {
      type: "category",
      data: keyWordsNameList.value,
    },
    color: ["#317DEF", "#FCC422"],
    series: [
      {
        name: "展示次数",
        type: "bar",
        data: keyWordsImpressionsList.value,
      },
      {
        name: "点击次数",
        type: "bar",
        data: keyWordsClicksList.value,
      },
    ],
  };
  // 使用设置好的选项显示图表
  chart.setOption(option);
}
</script>

<style lang="scss" scoped>
.zq-report-body {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: rgb(247, 247, 247);
}

header {
  width: 1300px;
  height: 100px;
  margin: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  box-sizing: border-box;
  padding: 0 20px;

  .zq-logo {
    width: 110px;
    height: auto;
  }

  .bing-logo {
    width: 110px;
    height: auto;
  }
  h1 {
    font-size: 26px;
    font-weight: 600;
    color: #333333;
  }
}

thead {
  background: rgb(0, 112, 192) !important;
}

.zq-report-header {
  width: 1300px;
  margin: auto;
  display: flex;
  justify-content: space-around;
  background: #ffffff;
  padding: 20px 40px;
  box-sizing: border-box;

  div {
    line-height: 30px;
    text-align: center;
    label {
      font-size: 16px;
      color: #333333;
      font-weight: 600;
    }
    strong {
      font-size: 14px;
      color: #333333;
      font-weight: 400;
    }
  }
}

.zq-report-first,
.zq-report-second,
.zq-report-third,
.zq-report-four,
.zq-report-five {
  width: 1300px;
  margin: auto;
  background: #ffffff;
  padding: 0 5px;
  box-sizing: border-box;
  padding: 20px;
  margin-bottom: 20px;
  :deep(.el-table__header th) {
    background-color: #477FFF !important;
    color: #ffffff;
    margin: auto;
  }
  h2 {
    padding: 15px 0;
    font-size: 20px;
    font-weight: 600;
    color: #333333;
  }

  h4 {
    margin: 10px;
    text-indent: 20px;
    font-size: 16px;
    color: #666666;
    padding: 15px;
  }
}

.zq-report-five {
  margin-bottom: 30px;
}

.page {
  height: auto;
}

.zq-report-auto_location {
  font-size: 12px;
  color: #666666;
  margin-bottom: 12px;
}
.zq-charts {
  width: 100%;
  height: 500px;
}
</style>
