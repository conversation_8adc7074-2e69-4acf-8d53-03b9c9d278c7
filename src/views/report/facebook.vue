<template>
  <div class="zq-report-body" id="facebook_pdf">
    <header>
      <img src="../../assets/img/facebook.png" class="facebook-logo" alt="" />
      <h1>Facebook 广告账户报告</h1>
      <img src="../../assets/img/zq-logo.png" class="zq-logo" alt="" />
    </header>
    <div class="zq-report-header">
      <div>
        <label>公司名称</label><br />
        <strong>{{ reportTableData?.task?.thirdCustomerName }}</strong>
      </div>
      <div>
        <label>广告账户ID</label><br />
        <strong>{{ reportTableData?.task?.thirdCustomerId }}</strong>
      </div>
      <div>
        <label>统计周期</label><br />
        <strong>{{ reportTableData?.task?.cycle }}</strong>
      </div>
    </div>
    <div class="zq-report-first">
      <h2>一、帐户效果数据</h2>
      <el-table
        :data="[reportTableData?.account]"
        class="zq-facebook-report_table"
      >
        <el-table-column label="展示量" prop="impressions"></el-table-column>
        <el-table-column label="点击量" prop="clicks"></el-table-column>
        <el-table-column label="CPM($)" prop="cpm"></el-table-column>
        <el-table-column label="CPC($)" prop="cpc"></el-table-column>
        <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
        <el-table-column label="总消耗($)" prop="totalCost"></el-table-column>
        <el-table-column
          label="平均日消耗($)"
          prop="avgDayCost"
        ></el-table-column>
      </el-table>
    </div>
    <div class="zq-report-second page">
      <h2>二、广告系列效果数据</h2>
      <el-table
        :data="reportTableData?.campaignList"
        class="zq-facebook-report_table"
      >
        <el-table-column label="广告系列" prop="campaignName"></el-table-column>
        <el-table-column label="展示量" prop="impressions"></el-table-column>
        <el-table-column label="点击量" prop="clicks"></el-table-column>
        <el-table-column label="CPM($)" prop="cpm"></el-table-column>
        <el-table-column label="CPC($)" prop="cpc"></el-table-column>
        <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
        <el-table-column label="CPA($)" prop="convCost"></el-table-column>
        <!-- 待定 -->
        <el-table-column
          label="成效多种转化"
          prop="conversions"
        ></el-table-column>
        <el-table-column label="总消耗($)" prop="cost"></el-table-column>
      </el-table>
    </div>
    <div class="zq-report-third page">
      <h2>三、每日花费明细</h2>
      <div ref="chartBarAndLine" style="width: 100%; height: 500px"></div>
      <el-table
        :data="reportTableData?.dayList"
        class="zq-facebook-report_table"
      >
        <el-table-column label="日期" prop="reportDate"></el-table-column>
        <el-table-column label="展示量" prop="impressions"></el-table-column>
        <el-table-column label="点击量" prop="clicks"></el-table-column>
        <el-table-column label="CPM($)" prop="cpm"></el-table-column>
        <el-table-column label="CPC($)" prop="cpc"></el-table-column>
        <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
        <el-table-column label="CPA($)" prop="convCost"></el-table-column>
        <!-- 待定 -->
        <el-table-column
          label="成效多种转化"
          prop="conversions"
        ></el-table-column>
        <el-table-column label="总消耗($)" prop="cost"></el-table-column>
      </el-table>
    </div>
    <div class="zq-report-four page">
      <h2>四、年龄和性别分布</h2>
      <div
        class="zq-report-auto_location"
        id="chartBar"
        style="width: 100%; height: 500px"
      ></div>
    </div>
    <div class="zq-report-five page">
      <h2>五、地理位置数据</h2>
      <el-table
        :data="reportTableData?.countryList"
        class="zq-facebook-report_table"
      >
        <el-table-column label="国家" prop="positionName"></el-table-column>
        <el-table-column label="展示量" prop="impressions"></el-table-column>
        <el-table-column label="点击量" prop="clicks"></el-table-column>
        <el-table-column label="CPM($)" prop="cpm"></el-table-column>
        <el-table-column label="CPC($)" prop="cpc"></el-table-column>
        <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
        <el-table-column label="CPA($)" prop="convCost"></el-table-column>
        <!-- 待定 -->
        <el-table-column
          label="成效多种转化"
          prop="conversions"
        ></el-table-column>
        <el-table-column label="总费用($)" prop="cost"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, watch } from "vue";
import { getLookReoprt } from "@/api/autoReportApi";
import ExportJsonExcel from "js-export-excel";
import { useRoute } from "vue-router";
import * as echarts from "echarts";
import { ElMessage, ElLoading } from "element-plus";
const lineList = ref([]);
const barList = ref([]);
const dateList = ref([]);
const manList = ref([]);
const womanList = ref([]);
const ageRangeList = ref([]);
const chartBarAndLine = ref<any>(null);
const route = useRoute();
const unknownList = ref([]);
const allFourData = ref([]);
const reportTableData = ref<any>({
  dayList: [],
});
defineExpose({
  htmlToPdf,
});
const props = defineProps({
  taskId: {
    type: String,
    default: "",
  },
  typeName: {
    type: String,
    default: "pdf",
  },
  downloadExcelName: {
    type: Object || null,
    default: null,
  },
});
watch(
  () => props.taskId,
  (newVal) => {
    if (newVal) {
      getreportTableData(newVal, props.typeName);
    } else {
      getreportTableData("", "");
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
async function getreportTableData(taskId: string, typeName: string) {
  console.log(taskId);
  try {
    const loading = ElLoading.service({
      lock: true,
      text: taskId && typeName == "excel" ? "正在导出excel..." : "加载中。。。",
      background: "rgba(0, 0, 0, 0.7)",
    });
    const params = {
      reportTaskId: route.query.taskId || taskId,
    };
    const res: any = await getLookReoprt(params);
    loading.close();
    if (res?.code == 200) {
      reportTableData.value = res.data;
      reportTableData.value?.dayList?.sort((a: any, b: any) => {
        return (
          new Date(a.reportDate).getTime() - new Date(b.reportDate).getTime()
        );
      });
      reportTableData.value?.countryList?.sort((a, b) => {
        return b.cost - a.cost;
      });
      reportTableData.value?.keyWorldList?.sort((a, b) => {
        return b.cost - a.cost;
      });
      reportTableData.value?.campaignList?.sort((a, b) => {
        return b.cost - a.cost;
      });
      barList.value = reportTableData.value?.dayList?.map((item: any) => {
        return item.cost;
      });
      lineList.value = reportTableData.value?.dayList?.map((item: any) => {
        return item.conversions;
      });
      dateList.value = reportTableData.value?.dayList?.map((item: any) => {
        return item.reportDate;
      });
      const list = reportTableData.value?.ageGenderList?.map((item) => {
        if (item.sexType == 1) {
          item.manType = "男性";
          item.manReach = item.reach;
        }
        if (item.sexType == 2) {
          item.womanType = "女性";
          item.womanReach = item.reach;
        }
        if (item.sexType == 3) {
          item.unknownType = "未知";
          item.unknownReach = item.reach;
        }
        delete item.reach;
        return { ...item };
      });
      var mergedArray = list.reduce(function (result, obj) {
        var target = result.find(function (item) {
          return item.ageRange === obj.ageRange;
        });

        if (target) {
          Object.assign(target, obj);
        } else {
          result.push(obj);
        }

        return result;
      }, []);

      console.log(mergedArray);
      allFourData.value = mergedArray;
      console.log(mergedArray);
      womanList.value = mergedArray?.map((item: any) => {
        return Number(item.womanReach);
      });
      manList.value = mergedArray?.map((item: any) => {
        return Number(item.manReach);
      });
      ageRangeList.value = mergedArray?.map((item: any) => {
        return item.ageRange;
      });
      unknownList.value = mergedArray?.map((item: any) => {
        return Number(item.unknownReach);
      });
      initBarAndLine();
      initBar();
      if (taskId && typeName == "excel") {
        downloadEXCEL();
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.log(error);
    // globalThis.$sentry.captureMessage(error);
  }
}
const emit = defineEmits(["colseExcel"]);
// 下载报表 excel
const downloadEXCEL = () => {
  const loading = ElLoading.service({
    lock: true,
    text: "下载中，请稍等！",
    background: "rgba(0, 0, 0, 0.7)",
  });
  const reportType = {
    1: "月报",
    2: "周报",
    3: "自定义",
  };
  const data = props.downloadExcelName;
  var option = {
    fileName: `${data?.thirdCustomerName}-Facebook-${
      reportType[data.reportType]
    }-报表`,
    datas: [
      {
        sheetData: [reportTableData.value?.account],
        sheetName: "帐户效果数据",
        sheetFilter: [
          "impressions",
          "clicks",
          "cpm",
          "cpc",
          "ctr",
          "totalCost",
          "avgDayCost",
        ],
        sheetHeader: [
          "展示量",
          "点击量",
          "CPM($)",
          "CPC($)",
          "点击率($)",
          "总消耗($)",
          "平均日消耗($)",
        ],
      },
      {
        sheetData: reportTableData.value?.campaignList,
        sheetName: "广告系列效果数据",
        // 待定
        sheetFilter: [
          "campaignName",
          "impressions",
          "clicks",
          "cpm",
          "cpc",
          "ctr",
          "convCost",
          "conversions",
          "cost",
        ],
        sheetHeader: [
          "广告系列",
          "展示量",
          "点击量",
          "CPM($)",
          "CPC($)",
          "点击率",
          "CPA($)",
          "成效多种转化",
          "总费用($)",
        ],
      },
      {
        sheetData: reportTableData.value?.dayList,
        sheetName: "每日花费明细",
        // 待定
        sheetFilter: [
          "reportDate",
          "impressions",
          "clicks",
          "cpm",
          "cpc",
          "ctr",
          "convCost",
          "conversions",
          "cost",
        ],
        sheetHeader: [
          "日期",
          "展示量",
          "点击量",
          "CPM($)",
          "CPC($)",
          "点击率(%)",
          "CPA(%)",
          "成效多种转化",
          "总消耗($)",
        ],
      },
      {
        sheetData: allFourData.value,
        sheetName: "年龄和性别分布",
        sheetFilter: ["ageRange", "manReach", "womanReach", "unknownReach"],
        sheetHeader: ["年齡范围", "男性数量", "女性数量", "未知数量"],
      },
      {
        sheetData: reportTableData.value?.countryList,
        sheetName: "所有地理位置数据",
        // 待定
        sheetFilter: [
          "positionName",
          "impressions",
          "clicks",
          "cpm",
          "cpc",
          "ctr",
          "convCost",
          "conversions",
          "cost",
        ],
        sheetHeader: [
          "国家/地区",
          "展示量",
          "点击量",
          "CPM($)",
          "CPC($)",
          "点击率(%)",
          "CPA($)",
          "成效多种转化",
          "总费用",
        ],
      },
    ],
  };
  let toExcel = new ExportJsonExcel(option);
  toExcel.saveExcel();
  loading.close();
  emit("colseExcel");
};
function htmlToPdf() {}

function initBarAndLine() {
  // 初始化ECharts实例
  const chart = echarts.init(chartBarAndLine.value);
  // 设置图表的选项和数据
  const option = {
    xAxis: {
      type: "category",
      data: dateList.value,
    },
    yAxis: {
      type: "value",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["总费用", "成效"],
      align: "left",
    },
    color: ["#1C78B5", "#FFD528"],
    series: [
      {
        name: "总费用",
        data: barList.value,
        type: "line",
      },
      {
        name: "成效",
        data: lineList.value,
        type: "line",
      },
    ],
  };

  // 使用设置好的选项显示图表
  chart.setOption(option);
}
function initBar() {
  // 初始化ECharts实例
  const chart = echarts.init(document.getElementById("chartBar"));
  // 设置图表的选项和数据
  const option = {
    title: {
      // text: 'World Population'
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["男性", "女性", "未知"],
      align: "left",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: ageRangeList.value,
    },
    yAxis: {
      type: "value",
      name: "覆盖人数",
      // data: keyWordsNameList.value
    },
    color: ["rgb(195, 53, 49)", "rgb(46, 69, 83)", "rgb(96, 160, 169)"],
    series: [
      {
        name: "男性",
        type: "bar",
        data: manList.value,
      },
      {
        name: "女性",
        type: "bar",
        data: womanList.value,
      },
      {
        name: "未知",
        data: unknownList.value,
        type: "bar",
      },
    ],
  };
  // 使用设置好的选项显示图表
  chart.setOption(option);
}
</script>

<style lang="scss" scoped>
.zq-report-body {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: rgb(247, 247, 247);
}

header {
  width: 1300px;
  height: 100px;
  margin: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  box-sizing: border-box;
  padding: 0 20px;

  .zq-logo {
    width: 100px;
    height: 47px;
  }

  .facebook-logo {
    width: 100px;
    height: 40px;
  }
}
thead {
  background: rgb(0, 112, 192) !important;
}
.zq-report-header {
  width: 1300px;
  margin: auto;
  display: flex;
  justify-content: space-around;
  background: #ffffff;
  padding: 20px 40px;
  box-sizing: border-box;
  div {
    line-height: 30px;
    text-align: center;
  }
}

.zq-report-first,
.zq-report-second,
.zq-report-third,
.zq-report-four,
.zq-report-five {
  width: 1300px;
  background: #ffffff;
  box-sizing: border-box;
  padding: 20px;
  margin-bottom: 20px;

  h2 {
    padding: 15px 0;
    font-size: 20px;
  }

  h4 {
    margin: 10px;
    text-indent: 20px;
    font-size: 16px;
    color: #666666;
    padding: 15px;
  }
}
.zq-report-five {
  margin-bottom: 30px;
}
.page {
  height: auto;
}
.zq-report-auto_location {
  font-size: 12px;
  color: #666666;
  margin-bottom: 12px;
}
::v-deep(.zq-facebook-report_table th) {
  background-color: #477FFF !important;
  color: #ffffff;
  margin: auto;
}
</style>
