<template>
    <div class="zq-report-body" id="google_pdf">
        <header>
            <img src="../../assets/img/google.png" class="google-logo" alt="" style="width: 120px;" />
            <h1>Google Ads账户报告</h1>
            <img src="../../assets/img/zq-logo.png" class="zq-logo" alt="" />
        </header>
        <div class="zq-report-header">
            <div>
                <label>公司名称</label><br/>
                <strong>{{ reportTableData?.task?.thirdCustomerName }}</strong>
            </div>
            <div>
                <label>广告账户ID</label><br/>
                <strong>{{ reportTableData?.task?.thirdCustomerId }}</strong>
            </div>
            <div>
                <label>统计周期</label><br/>
                <strong>{{ reportTableData?.task?.cycle }}</strong>
            </div>
        </div>
        <div class="zq-report-first">
            <h2>一、帐户效果数据</h2>
            <el-table :data="[reportTableData?.account]" border class="zq-google-report_table">
                <el-table-column label="展示量" prop="impressions"></el-table-column>
                <el-table-column label="点击量" prop="clicks"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="CPC(￥)" prop="cpc"></el-table-column>
                <el-table-column label="页首展示次数百分比(%)" prop="homeViewsRate" width="200"></el-table-column>
                <el-table-column label="绝对页首展示次数百分比(%)" prop="absoluteHomeViewsRate" width="200"></el-table-column>
                <el-table-column label="总消耗(￥)" prop="totalCost"></el-table-column>
                <el-table-column label="平均日消耗(￥)" prop="avgDayCost"></el-table-column>
                <!-- <el-table-column label="账户余额(￥)" prop="remainAmount"></el-table-column>
                <el-table-column label="预计消耗天数" prop="remainDays"></el-table-column> -->
            </el-table>
        </div>

        <div class="zq-report-second">
            <h2>二、广告系列效果数据</h2>
            <el-table :data="reportTableData?.campaignList" border class="zq-google-report_table">
                <el-table-column label="广告系列" prop="campaignName"></el-table-column>
                <el-table-column label="预算" prop="budgetDay"></el-table-column>
                <el-table-column label="点击次数" prop="clicks"></el-table-column>
                <el-table-column label="展示次数" prop="impressions"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="平均每次点击费用(￥)" prop="cpc"></el-table-column>
                <el-table-column label="费用(￥)" prop="cost"></el-table-column>
            </el-table>
        </div>
        <div class="zq-report-third">
            <h2>三、每日花费明细</h2>
            <div id="chartLine" class="zq-charts"></div>
            <el-table :data="reportTableData?.dayList" border class="zq-google-report_table">
                <el-table-column label="日期" prop="reportDate"></el-table-column>
                <el-table-column label="点击次数" prop="clicks"></el-table-column>
                <el-table-column label="展示次数" prop="impressions"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="平均每次点击费用(￥)" prop="cpc"></el-table-column>
                <el-table-column label="总费用(￥)" prop="cost"></el-table-column>
            </el-table>
        </div>
        <div class="zq-report-four">
            <h2>四、关键词数据</h2>
            <h4>4.1 TOP10关键词热度排名（按照费用高低从下到上排序）</h4>
            <div id="chartBar" class="zq-charts"></div>
            <h4>4.2 TOP10关键词详细数据</h4>
            <el-table :data="keywordTopTen" border class="zq-google-report_table">
                <el-table-column label="关键字" prop="keyWords"></el-table-column>
                <el-table-column label="点击次数" prop="clicks"></el-table-column>
                <el-table-column label="展示次数" prop="impressions"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="平均每次点击费用(￥)" prop="cpc"></el-table-column>
                <el-table-column label="总费用(￥)" prop="cost"></el-table-column>
            </el-table>
        </div>
        <div class="zq-report-five">
            <h2>五、TOP10地理位置数据</h2>
            <div id="chartBarAndLine"  class="zq-charts"></div>
            <el-table :data="locationTopTen" border class="zq-google-report_table">
                <el-table-column label="国家/地区" prop="positionName"></el-table-column>
                <el-table-column label="点击次数" prop="clicks"></el-table-column>
                <el-table-column label="展示次数" prop="impressions"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="平均每次点击费用(￥)" prop="cpc"></el-table-column>
                <el-table-column label="总费用(￥)" prop="cost"></el-table-column>
            </el-table>
        </div>
        <div class="zq-report-six">
            <h2>六、所有关键词数据</h2>
            <el-table :data="reportTableData?.keyworkdsList" border class="zq-google-report_table">
                <el-table-column label="关键字" prop="keyWords"></el-table-column>
                <el-table-column label="点击次数" prop="clicks"></el-table-column>
                <el-table-column label="展示次数" prop="impressions"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="平均每次点击费用(￥)" prop="cpc"></el-table-column>
                <el-table-column label="总费用(￥)" prop="cost"></el-table-column>
            </el-table>
        </div>
        <div class="zq-report-seven">
            <h2>七、所有地理位置数据</h2>
            <el-table :data="reportTableData?.countryList" border class="zq-google-report_table">
                <el-table-column label="国家/地区" prop="positionName"></el-table-column>
                <el-table-column label="点击次数" prop="clicks"></el-table-column>
                <el-table-column label="展示次数" prop="impressions"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="平均每次点击费用(￥)" prop="cpc"></el-table-column>
                <el-table-column label="总费用(￥)" prop="cost"></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import {  ref,defineProps,watch } from "vue";
import { useRoute } from "vue-router";
import { getLookReoprt } from "@/api/autoReportApi";
import { ElMessage } from "element-plus";
import ExportJsonExcel from 'js-export-excel';
// import html2canvas from "html2canvas";
// import jsPDF from "jspdf";
import * as echarts from "echarts";
import { ElLoading } from 'element-plus'
const route = useRoute()
const reportTableData = ref<any>(null)
const props = defineProps({
    taskId:{
        type:String,
        default:''
    },
    typeName:{
        type:String,
        default:'pdf'
    },
    downloadExcelName: {
        type: Object || null,
        default: null
    }
})
watch(
    () => props.taskId,
    (newVal) => {
        if(newVal){
            getreportTableData(newVal,props.typeName) 
        } else {
            getreportTableData('','') 
        }
    },
    {
        deep: true,
        immediate: true,
    }
)


const dayCostList = ref([])
const dayClicksList = ref([])
const dayDateList =ref([])
const keyWordsImpressionsList = ref([])
const keyWordsClicksList = ref([])
const keyWordsNameList =ref([])
const countryCostList = ref([])
const countryClicksList = ref([])
const countryNameList =ref([])
const keywordTopTen = ref([])
const locationTopTen = ref([])
 async function getreportTableData(taskId:string,typeName:string) {
    try {
        const loading = ElLoading.service({
            lock: true,
            text: taskId && typeName == 'excel' ? '正在导出excel...' : '加载中。。。',
            background: 'rgba(0, 0, 0, 0.7)',
        })
        const params = {
            reportTaskId: route.query.taskId || taskId
        }
        const res:any = await getLookReoprt(params)
        loading.close()
        if(res?.code == 200){
            reportTableData.value = res.data
            reportTableData.value?.dayList?.sort((a:any,b:any)=>{ return new Date(a.reportDate).getTime()- new Date(b.reportDate).getTime()})
            reportTableData.value?.countryList?.sort((a,b)=>{ return b.cost - a.cost})
            reportTableData.value?.keyworkdsList?.sort((a,b)=>{ return b.cost - a.cost})
            reportTableData.value?.campaignList?.sort((a,b)=>{ return b.cost - a.cost})
            dayCostList.value = reportTableData.value?.dayList?.map((item:any)=>{
                return item.cost
            })
            dayClicksList.value = reportTableData.value?.dayList?.map((item:any)=>{
                return item.clicks
            })
            dayDateList.value = reportTableData.value?.dayList?.map((item:any)=>{
                return item.reportDate
            })
            // 关键词热度排名前十
            keywordTopTen.value = reportTableData.value?.keyworkdsList?.sort((a:any,b:any)=>{
                return b.cost - a.cost
            })?.slice(0,10)
            keyWordsImpressionsList.value = keywordTopTen.value?.map((item:any)=>{
                return item.impressions
            })
            keyWordsClicksList.value = keywordTopTen.value?.map((item:any)=>{
                return item.clicks
            })
            console.log( keywordTopTen.value)
            keyWordsNameList.value = keywordTopTen.value?.map((item:any)=>{
                return item.keyWords
            })

            // 地理位置热度排名前十
            locationTopTen.value = reportTableData.value?.countryList?.sort((a:any,b:any)=>{
                return b.cost - a.cost
            })?.slice(0,10)
            countryCostList.value = locationTopTen.value?.map((item:any)=>{
                return item.cost
            })
            countryClicksList.value = locationTopTen.value?.map((item:any)=>{
                return item.clicks
            })
            countryNameList.value = locationTopTen.value?.map((item:any)=>{
                return item.positionName
            })
            initLine();
            initBar();
            initBarAndLine();
            console.log(typeName)
            if(taskId && typeName == 'excel'){
                downloadEXCEL()
            }
        }else {
            ElMessage.error(res.msg)
        }
    } catch (error) {
        globalThis.$sentry.captureMessage(error)
        
    }
 }
 const emit = defineEmits(["colseExcel"]);
// 下载报表 excel
const downloadEXCEL = () => {
    const loading = ElLoading.service({
            lock: true,
            text: '下载中，请稍等！',
            background: 'rgba(0, 0, 0, 0.7)',
    })
    const reportType = {
        1: '月报',
        2: '周报',
        3: '自定义'
    }
    const data = props.downloadExcelName
    var option = {
        fileName: `${data?.thirdCustomerName}-Google-${reportType[data?.reportType]}-报表`,
        datas: [
            {
                sheetData: [reportTableData.value?.account],
                sheetName: '帐户效果数据',
                sheetFilter: ['impressions', 'clicks', 'ctr', 'cpc', 'homeViewsRate','absoluteHomeViewsRate','totalCost', 'avgDayCost'],
                sheetHeader: ['展示量', '点击量', '点击率(%)', 'CPC(￥)', '页首展示次数百分比(%)','绝对页首展示次数百分比(%)','总消耗(￥)', '平均日消耗(￥)'],
            },
            {
                sheetData: reportTableData.value?.campaignList,
                sheetName: '广告系列效果数据',
                sheetFilter: ['campaignName', 'budgetDay', 'clicks', 'impressions', 'ctr', 'cpc', 'cost'],
                sheetHeader: ['广告系列', '预算', '点击次数', '展示次数', '点击率(%)', '平均每次点击费用', '费用'],
            },
            {
                sheetData: reportTableData.value?.dayList,
                sheetName: '每日花费明细',
                sheetFilter: ['reportDate', 'impressions', 'clicks', 'ctr', 'cpc', 'cost'],
                sheetHeader: ['日期', '展示次数', '点击次数', '点击率(%)', '平均每次点击费用(￥)', '总费用(￥)'],
            },
            {

                sheetData: reportTableData.value?.keyworkdsList,
                sheetName: '所有关键词数据',
                sheetFilter: ['keyWords', 'impressions', 'clicks', 'ctr', 'cpc', 'cost'],
                sheetHeader: ['关键字', '展示次数', '点击次数', '点击率(%)', '平均每次点击费用(￥)', '总费用(￥)'],
            },
            {
                sheetData: reportTableData.value?.countryList,
                sheetName: '所有地理位置数据',
                sheetFilter: ['positionName', 'impressions', 'clicks', 'ctr', 'cpc', 'cost'],
                sheetHeader: ['国家/地区', '展示次数', '点击次数', '点击率(%)', '平均每次点击费用(￥)', '总费用(￥)'],
            }
        ]
    }
    let toExcel = new ExportJsonExcel(option);
    toExcel.saveExcel();
    loading.close()
    emit("colseExcel");
}

function initLine() {
    // 初始化ECharts实例
    const chart = echarts.init(document.getElementById("chartLine"));
    // 设置图表的选项和数据
    const option = {
        title: {
            // text: 'ECharts 示例'
        },
        legend: {
            data: ["总费用", "点击次数"],
            align: "left",

        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "shadow",
            },
        },
        xAxis: {
            data: dayDateList.value,
        },
        yAxis: {},
        series: [
            {
                name: "总费用",
                type: "line",
                data: dayCostList.value,
            },
            {
                name: "点击次数",
                type: "line",
                data: dayClicksList.value,
            },
        ],
    };
    // 使用设置好的选项显示图表
    chart.setOption(option);
}
function initBar() {
    // 初始化ECharts实例
    const chart = echarts.init(document.getElementById("chartBar"));
    // 设置图表的选项和数据
    const option = {
        title: {
            // text: 'World Population'
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "shadow",
            },
        },
        legend: {
            data: ["展示次数", "点击次数"],
            align: "left",

        },
        grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
        },
        xAxis: {
            type: "value",
            boundaryGap: [0, 0.01],
        },
        yAxis: {
            type: "category",
            data: keyWordsNameList.value
        },
        colof: ["#317DEF", "#FCC422"],
        series: [
            {
                name: "展示次数",
                type: "bar",
                data: keyWordsImpressionsList.value
            },
            {
                name: "点击次数",
                type: "bar",
                data: keyWordsClicksList.value
            },
        ],
    };
    // 使用设置好的选项显示图表
    chart.setOption(option);
}
function initBarAndLine() {
    // 初始化ECharts实例
    const chart = echarts.init(document.getElementById("chartBarAndLine"));
    // 设置图表的选项和数据
    const option = {
        xAxis: {
            type: "category",
            data: countryNameList.value?.slice(0,10),
        },
        yAxis: {
            type: "value",
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "shadow",
            },
        },
        legend: {
            data: ["总费用", "点击量"],
            align: "left",
        },
        color: ["#1C78B5", "#FFD528"],
        series: [
            {
                data: countryCostList.value?.slice(0,10),
                type: "bar",
                name: "总费用",
            },
            {
                data: countryClicksList.value?.slice(0,10),
                type: "line",
                name: "点击量",
            },
        ],
    };
    // 使用设置好的选项显示图表
    chart.setOption(option);
}
</script>

<style lang="scss" scoped>
.zq-report-body {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background: rgb(247, 247, 247);
}

header {
    width: 1300px;
    height: 100px;
    margin: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    padding: 0 20px;
    box-sizing: border-box;
}

header h1 {
    font-size: 26px;
}

.google-logo,.zq-logo  {
    width: 100px;
    height: 47px;
}

.zq-report-header {
    width: 1300px;
    margin: auto;
    display: flex;
    justify-content: space-around;
    background: #ffffff;
    padding: 20px 40px;
    box-sizing: border-box;
    div{
        line-height: 30px;
        text-align: center;
    }
}

.zq-report-first,
.zq-report-second,
.zq-report-third,
.zq-report-four,
.zq-report-five,
.zq-report-six,
.zq-report-seven {
    width: 1300px;
    margin: auto;
    background: #ffffff;
    padding: 20px;
    box-sizing: border-box;
    margin-bottom: 20px;

    h2 {
        padding: 15px 0;
        font-size: 20px;
    }

    h4 {
        margin: 10px;
        // text-indent: 20px;
        font-size: 16px;
        color: #666666;
        padding: 15px 0;
    }
}

.zq-report-seven {
    margin-bottom: 30px;
}
.zq-charts{
    width: 1300px;
    height: 600px;
    margin: auto;
}
::v-deep(.zq-google-report_table th) {
    background-color: #477FFF !important;
    color: #ffffff;
}
</style>