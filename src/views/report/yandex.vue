<template>
    <div class="zq-report-body" id="yandex_pdf">
            <header>
                <img src="../../assets/img/yandex.png" class="yandex-logo" alt="" />
                <h1>Yandex广告账户报告</h1>
                <img src="../../assets/img/zq-logo.png" class="zq-logo" alt="" />
            </header>
            <div class="zq-report-header">
                <div>
                    <label>公司名称</label><br/>
                    <strong>{{ reportTableData?.task?.thirdCustomerName }}</strong>
                </div>
                <div>
                    <label>统计周期</label><br/>
                    <strong>{{ reportTableData?.task?.cycle }}</strong>
                </div>
            </div>
            <div class="zq-report-first">
                <h2>一、帐户效果数据</h2>
                <el-table :data="[reportTableData?.account]" border class="zq-yandex-report_table">
                    <el-table-column label="展示量" prop="impressions"></el-table-column>
                    <el-table-column label="点击量" prop="clicks"></el-table-column>
                    <el-table-column label="CTR(%)" prop="ctr"></el-table-column>
                    <el-table-column label="CPC($)" prop="cpc"></el-table-column>
                    <el-table-column label="总消耗($)" prop="totalCost"></el-table-column>
                    <el-table-column label="平均日消耗($)" prop="avgDayCost"></el-table-column>
                    <!-- <el-table-column label="账户余额($)" prop="remainAmount"></el-table-column> -->
                    <!-- <el-table-column label="预计消耗天数" prop="remainDays"></el-table-column> -->
                </el-table>
            </div>
        <div class="zq-report-second page">
            <h2>二、广告系列效果数据</h2>
            <el-table :data="reportTableData?.campaignList" border class="zq-yandex-report_table">
                <el-table-column label="广告系列" prop="campaignName"></el-table-column>
                <el-table-column label="展示次数" prop="impressions"></el-table-column>
                <el-table-column label="点击次数" prop="clicks"></el-table-column>
                <el-table-column label="CTR" prop="ctr"></el-table-column>
                <el-table-column label="CPC($)" prop="cpc"></el-table-column>
                <el-table-column label="总费用($)" prop="cost"></el-table-column>
            </el-table>
        </div>
        <div class="zq-report-third page">
            <h2>三、每日花费明细</h2>
            <div ref="chartBarAndLine" style="width: 100%; height: 500px;"></div>
            <el-table :data="reportTableData?.dayList" border class="zq-yandex-report_table">
                <el-table-column label="日期" prop="reportDate"></el-table-column>
                <el-table-column label="展示量" prop="impressions"></el-table-column>
                <el-table-column label="点击量" prop="clicks"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="CPC($)" prop="cpc"></el-table-column>
                <el-table-column label="总消耗($)" prop="cost"></el-table-column>
            </el-table>
        </div>
        <div class="zq-report-four page">
            <h2>四、所有关键词数据</h2>
            <div class="zq-report-auto_location">自动定向（autotargeting）：Yandex Direct系统根据广告文案及落地页面中的产品信息分析、抓取符合用户搜索，兴趣的关键词信息，自动对标适用人群，从而吸引到额外的定向流量。</div>
            <el-table :data="reportTableData?.keyworkdsList" border class="zq-yandex-report_table">
                <el-table-column label="Impression criteria" prop="keyWords"></el-table-column>
                <el-table-column label="展示词" prop="keyWordsZh"></el-table-column>
                <el-table-column label="展示量" prop="impressions"></el-table-column>
                <el-table-column label="点击量" prop="clicks"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="总消耗($)" prop="cost"></el-table-column>
                <el-table-column label="CPC($)	" prop="cpc"></el-table-column>
                <el-table-column label="平均展示位置" prop="avgImpressionPosition"></el-table-column>
                <el-table-column label="平均点击位置" prop="avgClickPosition"></el-table-column>
            </el-table>
        </div>
        <div class="zq-report-five page">
            <h2>五、地理位置数据</h2>
            <el-table :data="reportTableData?.countryList" border class="zq-yandex-report_table">
                <el-table-column label="城市" prop="positionName"></el-table-column>
                <el-table-column label="城市(翻译)" prop="positionNameCn"></el-table-column>
                <el-table-column label="展示次数" prop="impressions"></el-table-column>
                <el-table-column label="点击次数" prop="clicks"></el-table-column>
                <el-table-column label="点击率(%)" prop="ctr"></el-table-column>
                <el-table-column label="平均每次点击费用($)" prop="cpc"></el-table-column>
                <el-table-column label="总费用($)" prop="cost"></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import {  ref, defineProps,watch  } from 'vue';
// import html2canvas from 'html2canvas';
import { getLookReoprt } from "@/api/autoReportApi";
import ExportJsonExcel from 'js-export-excel';
import { useRoute } from "vue-router";
// import jsPDF from 'jspdf';
import * as echarts from 'echarts';
import { ElMessage, ElLoading } from "element-plus";
const lineList = ref([])
const barList = ref([])
const dateList = ref([])
const chartBarAndLine = ref<any>(null);
const route = useRoute()
const reportTableData = ref<any>({
    dayList:[]
})
const props = defineProps({
    taskId:{
        type:String,
        default:''
    },
    typeName:{
        type:String,
        default:'pdf'
    },
    downloadExcelName: {
        type: Object || null,
        default: null
    }
})
watch(
    () => props.taskId,
    (newVal) => {
        if(newVal){
            getreportTableData(newVal, props.typeName)
        } else {
            getreportTableData('', '')
        }
    },
    {
        deep: true,
        immediate: true,
    }
)
async function getreportTableData(taskId:string, typeName:string) {
    console.log(taskId)
    try {
        const loading = ElLoading.service({
            lock: true,
            text: taskId && typeName == 'excel' ? '正在导出excel...' : '加载中。。。',
            background: 'rgba(0, 0, 0, 0.7)',
        })
        const params = {
            reportTaskId: route.query.taskId || taskId
        }
        const res:any = await getLookReoprt(params)
        loading.close()
        if(res?.code == 200){
            reportTableData.value = res.data
            reportTableData.value?.dayList?.sort((a:any,b:any)=>{ return new Date(a.reportDate).getTime()- new Date(b.reportDate).getTime()})
            reportTableData.value?.countryList?.sort((a,b)=>{ return b.cost - a.cost})
            reportTableData.value?.keyworkdsList?.sort((a,b)=>{ return b.cost - a.cost})
            reportTableData.value?.campaignList?.sort((a,b)=>{ return b.cost - a.cost})
            barList.value = reportTableData.value?.dayList?.map((item:any)=>{
                return item.cost
            })
            lineList.value = reportTableData.value?.dayList?.map((item:any)=>{
                return item.clicks
            })
            dateList.value =reportTableData.value?.dayList?.map((item:any)=>{
                return item.reportDate
            })
            initBarAndLine();
            if(taskId && typeName == 'excel'){
                downloadEXCEL()
            }
        }else {
            ElMessage.error(res.msg)
        }
    } catch (error) {
         globalThis.$sentry.captureMessage(error)
        
    }
 }
 const emit = defineEmits(["colseExcel"]);
 // 下载报表 excel
const downloadEXCEL = () => {
    const loading = ElLoading.service({
            lock: true,
            text: '下载中，请稍等！',
            background: 'rgba(0, 0, 0, 0.7)',
    })
    const reportType = {
        1: '月报',
        2: '周报',
        3: '自定义'
    }
    const data = props.downloadExcelName
    var option = {
        fileName: `${data?.thirdCustomerName}-Yandex-${reportType[data.reportType]}-报表`,
        datas: [
            {
               
                sheetData: [reportTableData.value?.account],
                sheetName: '帐户效果数据',
                sheetFilter: ['impressions', 'clicks', 'ctr', 'cpc', 'totalCost','avgDayCost', 'remainAmount','remainDays'],
                sheetHeader: ['展示量', '点击量', 'CTR(%)', 'CPC($)', '总消耗($)','平均日消耗($)', '账户余额($)','预计消耗天数'],
            },
            {
                sheetData: reportTableData.value?.campaignList,
                sheetName: '广告系列效果数据',
                sheetFilter: ['campaignName', 'impressions', 'clicks', 'ctr', 'cpc', 'cost'],
                sheetHeader: ['广告系列', '展示次数', '点击次数', 'CTR', 'CPC($)', '总费用($)'],
            },
            {

                sheetData: reportTableData.value?.dayList,
                sheetName: '每日花费明细',
                sheetFilter: ['reportDate', 'impressions', 'clicks', 'ctr', 'cpc', 'cost'],
                sheetHeader: ['日期', '展示量', '点击量', '点击率(%)', 'CPC($)', '总消耗($)'],
            },
            {
                sheetData: reportTableData.value?.keyworkdsList,
                sheetName: '所有关键词数据',
                sheetFilter: ['keyWords', 'keyWordsZh', 'impressions', 'clicks', 'ctr', 'cost','cpc', 'avgImpressionPosition', 'avgClickPosition'],
                sheetHeader: ['Impression criteria','展示词', '展示量', '点击量', '点击率(%)', '总消耗($)', 'CPC($)','平均展示位置','平均点击位置'],
            },
            {
                sheetData: reportTableData.value?.countryList,
                sheetName: '所有地理位置数据',
                sheetFilter: ['positionName', 'impressions', 'clicks', 'ctr', 'cpc', 'cost'],
                sheetHeader: ['国家/地区', '展示次数', '点击次数', '点击率(%)', '平均每次点击费用', '总费用'],
            }
        ]
    }
    let toExcel = new ExportJsonExcel(option);
    toExcel.saveExcel();
    loading.close()
    emit("colseExcel");
}

function initBarAndLine() {
    // 初始化ECharts实例  
    const chart = echarts.init(chartBarAndLine.value);
    // 设置图表的选项和数据  
    const option = {
        xAxis: {
            type: 'category',
            data: dateList.value,
        },
        yAxis: {
            type: 'value',
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
        legend: {
            data: ["总费用", "点击量"],
            align: "left",

        },
        color: ['#1C78B5', '#FFD528'],
        series: [
            {
                name:"总费用",
                data: barList.value,
                type: 'bar',
            },
            {
                name: "点击量",
                data: lineList.value,
                type: 'line',
            },
        ],
    };

    // 使用设置好的选项显示图表  
    chart.setOption(option);
}
</script>

<style lang="scss" scoped>
.zq-report-body {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background: rgb(247, 247, 247);
}

header {
    width: 1300px;
    height: 100px;
    margin: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    box-sizing: border-box;
    padding: 0 20px;

    .zq-logo {
        width: 100px;
        height: 47px;
    }

    .yandex-logo {
        width: 89.2px;
        height: 20px;
    }
}
thead{
    background: rgb(0,112,192) !important;
}
.zq-report-header {
    width: 1300px;
    margin: auto;
    display: flex;
    justify-content: space-around;
    background: #ffffff;
    padding: 20px 40px;
    box-sizing: border-box;
    div{
        line-height: 30px;
        text-align: center;
    }
}

.zq-report-first,
.zq-report-second,
.zq-report-third,
.zq-report-four,
.zq-report-five {
    width: 1300px;
    background: #ffffff;
    box-sizing: border-box;
    padding: 20px;
    margin-bottom: 20px;

    h2 {
        padding: 15px 0;
        font-size: 20px;
    }

    h4 {
        margin: 10px;
        text-indent: 20px;
        font-size: 16px;
        color: #666666;
        padding: 15px;
    }
}
.zq-report-five{
    margin-bottom: 30px;
}
.page{
    height: auto;
}
.zq-report-auto_location {
    font-size: 12px;
    color: #666666;
    margin-bottom: 12px;

}
::v-deep(.zq-yandex-report_table tr th) {
	background-color: rgb(0,112,192) !important;
    color: #ffffff;
    margin: auto;
}
</style>