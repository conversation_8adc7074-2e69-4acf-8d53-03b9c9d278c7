<template>
  <h2 class="ads-title">
    <Breadcrumb />
  </h2>
  <div class="equityInstrument">
    <div
      class="mainRow"
      v-for="(item, index) in list"
      :key="index"
      :class="{ noLast: index % 3 != 0 || index == 0 }"
    >
      <div class="rowTop">
        <img :src="item.icon" alt="" class="topImg" />
        <div class="topTitle">{{ item.title }}</div>
      </div>
      <div class="rowList">
        <div class="rowListMain">
          <div
            class="rowMain"
            v-for="(itemChild, indexChile) in item.descList"
            :key="indexChile"
          >
            <img :src="dui" alt="" class="iconImg" />
            <div>{{ itemChild }}</div>
          </div>
        </div>
        <div class="rowListMain">
          <div
            class="rowMain"
            v-for="(itemChild, indexChile) in item.descListTwo"
            :key="indexChile"
          >
            <img :src="dui" alt="" class="iconImg" />
            <div>{{ itemChild }}</div>
          </div>
        </div>
      </div>
      <div class="btn">请联系BD</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import dui from "@/assets/img/dui.png";
import equityInstrument1 from "@/assets/img/equityInstrument1.jpg";
import equityInstrument2 from "@/assets/img/equityInstrument2.jpg";
import equityInstrument3 from "@/assets/img/equityInstrument3.jpg";
import equityInstrument4 from "@/assets/img/equityInstrument4.jpg";
import equityInstrument5 from "@/assets/img/equityInstrument5.jpg";
import equityInstrument6 from "@/assets/img/equityInstrument6.jpg";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

let list = ref([
  {
    title: "优质创意设计服务",
    descList: ["设计师1V1服务", "提供高质量创意设计"],
    icon: equityInstrument1,
  },
  {
    title: "落地页建设服务",
    descList: [
      "专业团队1V1服务",
      "落地页开发上线服务",
      "落地页策划服务",
      "落地页语言翻译",
    ],
    icon: equityInstrument2,
  },
  {
    title: "专业广告账户搭建服务",
    descList: [
      "优化师1V1服务",
      "深入学习及调研",
      "账户搭建方案",
      "数据分析代码",
    ],
    descListTwo: ["规划广告结构", "广告系列设置", "账户设置结构"],
    icon: equityInstrument3,
  },
  {
    title: "1V1账户诊断优化服务",
    descList: [
      "优化师1V1服务",
      "广告账户数据分析",
      "账户优化建议方案",
      "深入学习及调研",
      "账户问题定位",
    ],
    icon: equityInstrument4,
  },
  {
    title: "跨境品类解析报告",
    descList: ["全球热门品类市场解析", "挖掘潜力品类和投放建议"],
    icon: equityInstrument5,
  },
  {
    title: "专家数据解读攻略",
    descList: [
      "专家投放数据观测指标解读",
      "指标背后问题发现技巧",
      "数据背后问题优化建议",
    ],
    icon: equityInstrument6,
  },
]);
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: "PingFangSC-Medium";
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  cursor: pointer;
  display: flex;
  align-items: center;
  span {
    flex: 1;
    margin-left: 10px;
  }
}
.equityInstrument {
  width: 100%;
  height: calc(100% - 80px);
  border-radius: 8px;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  //display: flex;
  //flex-wrap: wrap;
  .mainRow {
    width: 290px;
    height: 400px;
    border-radius: 8px;
    background: #ffffff;
    padding: 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    margin-right: 20px;
    position: relative;
    .rowTop {
      .topImg {
        width: 250px;
        height: 120px;
      }
      .topTitle {
        color: #202020;
        font-size: 16px;
        font-weight: 500;
        font-family: "PingFang SC";
        width: 100%;
        text-align: center;
        padding: 16px 0 16px;
      }
    }
    .rowList {
      display: flex;
      justify-content: space-between;
      .rowListMain {
        .rowMain {
          color: #202020;
          font-size: 13px;
          font-weight: 500;
          font-family: "PingFang SC";
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          .iconImg {
            width: 13px;
            height: 11px;
            margin-right: 4px;
          }
        }
      }
    }
    .btn {
      position: absolute;
      width: 120px;
      height: 32px;
      border-radius: 4px;
      background: var(--el-color-primary);;
      text-align: center;
      line-height: 32px;
      cursor: pointer;
      color: #ffffff;
      font-size: 14px;
      font-weight: 400;
      font-family: "PingFang SC";
      left: 85px;
      bottom: 20px;
    }
  }
  .noLast {
    // margin-right: calc((100% - 290px * 4) / 3);
  }
}
</style>
