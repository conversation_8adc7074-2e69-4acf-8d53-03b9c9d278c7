<template>
  <div class="zq-user-body" v-loading="loading">
    <el-form
      ref="searchRef"
      :model="searchForm"
      :inline="true"
      class="zq-advert-search"
    >
      <el-form-item>
        <el-input
          v-model="searchForm.thirdCustomerNm"
          style="width: 240px"
          placeholder="账户名称或ID"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="props.activeTabs == '1'">
        <el-select
          v-model="searchForm.state"
          placeholder="账户状态"
          style="width: 240px"
          clearable
        >
          <el-option
            v-for="item in generateStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="Unlock"
          @click="unlockAccount"
          :disabled="commonControls"
          >解绑</el-button
        >
        <el-button type="primary" :icon="Search" @click="search"
          >查询</el-button
        >
        <el-button
          id="add_authorization"
          type="primary"
          :icon="CirclePlusFilled"
          @click="addAuthorization"
          :disabled="commonControls"
          >新增授权</el-button
        >
      </el-form-item>
    </el-form>
    <el-collapse
      class="zq-el-collapse"
      v-model="activeName"
      accordion
      v-if="tableData.length"
    >
      <el-collapse-item
        v-for="item in tableData"
        :key="item?.agencyCustomer"
        title="Consistency"
        :name="item.agencyCustomer"
      >
        <template #title>
          <div class="zq-top-authorize">
            <div
              class="zq-top-authorize_name"
              :style="{ background: item?.belong == 1 ? '#F56C6C' : '#68C23A' }"
            >
              <img
                src="../../../assets/img/outh-logo.png"
                style="width: 14px; height: 14px; margin-right: 8px"
                alt="授权"
              />
              <strong>{{
                item?.belong == 1 ? "优化师授权" : "本人授权"
              }}</strong>
            </div>
            <div class="zq-top-authorize_item">
              <strong> {{ item?.agencyCustomer }}</strong>
              <i>授权账号</i>
            </div>
            <div class="zq-top-authorize_item">
              <strong>{{ item?.oauthTime }}</strong>
              <i>最近授权时间</i>
            </div>
            <div class="zq-top-authorize_item" v-if="activeTabs == '3'">
              <strong>{{ item?.email }}</strong>
              <i>授权邮箱</i>
            </div>
            <div class="zq-top-authorize_itemText">
              <span
                :style="{
                  'background-color':
                    item?.oauthState === '已授权' ? '#03D097' : '#FF0000',
                }"
              ></span>
              <i>{{ item?.oauthState }}</i>
            </div>
            <div class="zq-top-authorize_itemText" style="cursor: pointer">
              <el-button
                link
                type="primary"
                :icon="UserFilled"
                @click="addAuthorization"
                :disabled="commonControls || item?.belong == 1"
                >重新授权</el-button
              >
            </div>
          </div>
        </template>

        <TableCustom
          style="margin-top: 12px"
          :columns="columns"
          :height="0"
          :tableData="item.customerView"
          :isShowPagination="false"
          :isAdmin="true"
          :selectionChange="selectionChange"
        >
        </TableCustom>
      </el-collapse-item>
    </el-collapse>
    <el-empty v-else description="暂无数据" :image-size="200" />
    <TourStep
      :open="open"
      :tourStep="tourPageList"
      :typeName="'publicPoolManage'"
    />
  </div>
</template>

<script setup lang="ts" name="system-user">
import { ref, reactive, computed, defineProps, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  getGoogleList,
  getGoogleAuthorization,
  getGoogleAauth,
  getYandexList,
  getYandexAuthorization,
  getYandexAauth,
  facebookAuthorization,
  facebookOauth,
  facebookTypeList,
  tikTokTypeList,
  tikTokAuthorization,
  tikTokOauth,
  getBingList,
  getBingAuthorization,
  getBingAuth,
  getLinkedInList,
  getLinkedInAuthorization,
  getLinkedInAuth,
  unBind,
} from "@/api/adUserSetingApi";
import TableCustom from "@/components/table-custom.vue";
import {
  Search,
  CirclePlusFilled,
  Unlock,
  UserFilled,
} from "@element-plus/icons-vue";
import { ElLoading, ElMessageBox } from "element-plus";
import Cookies from "js-cookie";
import usePermissionStore from "@/store/modules/permission";
import TourStep from "@/components/tourStep/index.vue";
import { microForceDispatch } from "@/utils/index";
const commonControls = ref(false);
const guideDetail = JSON.parse(localStorage.getItem("guideDetail") || "{}");
const permissionStore = usePermissionStore();
const props = defineProps({
  typeName: {
    type: String,
    default: "Google",
  },
  columns: {
    type: Array,
    default: () => [],
  },
  activeTabs: {
    type: String,
    default: "1",
  },
});

onMounted(() => {
  window.parent.postMessage(
    {
      type: "CHILD_COMPONENT_MOUNTED",
      component: "MyComponent",
    },
    "*"
  );
});

const GoogleRuleButtonAuth = computed(
  () =>
    permissionStore?.buttonAuth?.AuthorizedAgent?.indexOf("GoogleControls") >
      -1 && props.activeTabs == "1"
);
const YandexRuleButtonAuth = computed(
  () =>
    permissionStore?.buttonAuth?.AuthorizedAgent?.indexOf("YandexControls") >
      -1 && props.activeTabs == "2"
);
const FacebookRuleButtonAuth = computed(
  () =>
    permissionStore?.buttonAuth?.AuthorizedAgent?.indexOf("FacebookControls") >
      -1 && props.activeTabs == "3"
);
const TikTokRuleButtonAuth = computed(
  () =>
    permissionStore?.buttonAuth?.AuthorizedAgent?.indexOf("TikTokControls") >
      -1 && props.activeTabs == "4"
);
const BingRuleButtonAuth = computed(
  () =>
    permissionStore?.buttonAuth?.AuthorizedAgent?.indexOf("BingADSControls") >
      -1 && props.activeTabs == "5"
);
const LinkedInRuleButtonAuth = computed(
  () =>
    permissionStore?.buttonAuth?.AuthorizedAgent?.indexOf("LinkInADSControls") >
      -1 && props.activeTabs == "6"
);
const customerIdList = ref<any[]>([]);
const open = ref(false);

const tourPageList = [
  {
    target: "add_authorization",
    placement: "left",
    title: "新增授权",
    description: "快速绑定广告账户，绑定后即可查看投放数据。",
  },
];
const loading = ref(false);
const activeName = ref("1");
const route = useRoute();
const router = useRouter();
const isOauthSuccess = ref<boolean>(false); // 是否显示新增授权
const page = reactive({
  index: 1,
  size: 10,
  total: 0,
});
const tableData = ref<any[]>([]);

const searchForm = ref({
  thirdCustomerNm: "",
  state: "",
  time: "",
});

const generateStatus = [
  {
    value: " ",
    label: "全部",
  },

  {
    value: 1,
    label: "正常",
  },
  {
    value: 2,
    label: "暂停",
  },
];

const visible = ref(false);
//
const isSHowTour = computed(() => {
  return guideDetail.publicPoolManage == 0 || !guideDetail.publicPoolManage;
});
console.log(route);
// 代理用户授权接口yandex
const yandexOauth = async () => {
  try {
    const access_token: any = Cookies.get("access_token") || false;
    if (!access_token && route.hash) {
      const param = {
        token: route.fullPath.split("&")[0].split("#access_token=")[1] || "",
        tokenExpires:
          Number(route.fullPath.split("&")[2].split("expires_in=")[1]) || null,
      };
      const loading = ElLoading.service({
        lock: true,
        text: "授权中，请稍等！",
        background: "rgba(0, 0, 0, 0.7)",
      });
      const res: any = await getYandexAauth(param);
      loading.close();
      if (res.code == 200) {
        ElMessage.success("授权成功");
        visible.value = false;
        isOauthSuccess.value = true;
        Cookies.set(
          "access_token",
          route.fullPath.split("&")[0].split("#access_token=")[1]
        );
        getYandexData();
      } else {
        isOauthSuccess.value = false;
        ElMessage.error(res.msg);
      }
      router.replace("/accountManage/authorized-agent")
    } else {
      getYandexData();
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 代理用户授权接口google
const googleOauth = async () => {
  try {
    const googleCode: any = Cookies.get("googleCode") || false;
    console.log(googleCode);
    if (!googleCode && route.query.code) {
      const param = {
        code: route.query.code,
      };
      const loading = ElLoading.service({
        lock: true,
        text: "授权中，请稍等！",
        background: "rgba(0, 0, 0, 0.7)",
      });
      const res: any = await getGoogleAauth(param);
      loading.close();
      if (res.code == 200) {
        ElMessage.success("已授权，由于数据量太多请稍后查看！");
        isOauthSuccess.value = true;
        Cookies.set("googleCode", route.query.code);
        getGoogleData();
      } else {
        ElMessage.error(res.msg);
        isOauthSuccess.value = false;
      }
      router.replace("/accountManage/authorized-agent")
    } else {
      getGoogleData();
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 代理用户授权接口Facebook
const getFacebookOauth = async () => {
  try {
    const facebookCode: any = Cookies.get("facebookCode") || false;
    console.log(facebookCode);
    if (!facebookCode && route.query.code) {
      const param = {
        code: route.query.code,
      };
      const loading = ElLoading.service({
        lock: true,
        text: "授权中，请稍等！",
        background: "rgba(0, 0, 0, 0.7)",
      });
      const res: any = await facebookOauth(param);
      loading.close();
      if (res?.code == 200) {
        ElMessage.success("已授权，由于数据量太多请稍后查看！");
        isOauthSuccess.value = true;
        Cookies.set("facebookCode", route.query.code);
        getFacebookData();
      } else {
        ElMessage.error(res.msg);
        isOauthSuccess.value = false;
      }
      router.replace("/accountManage/authorized-agent")
    } else {
      getFacebookData();
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 代理用户授权接口TikTok
const getTikTokOauth = async () => {
  try {
    const tikTokCode: any = Cookies.get("tikTokCode") || false;
    console.log(tikTokCode);
    if (!tikTokCode && route.query.auth_code) {
      const param = {
        code: route.query.auth_code,
      };
      const loading = ElLoading.service({
        lock: true,
        text: "授权中，请稍等！",
        background: "rgba(0, 0, 0, 0.7)",
      });
      const res: any = await tikTokOauth(param);
      loading.close();
      if (res?.code == 200) {
        ElMessage.success("已授权，由于数据量太多请稍后查看！");
        isOauthSuccess.value = true;
        Cookies.set("tikTokCode", route.query.auth_code);
        getTikTokData();
      } else {
        ElMessage.error(res.msg);
        isOauthSuccess.value = false;
      }
      router.replace("/accountManage/authorized-agent")
    } else {
      getTikTokData();
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 代理用户授权接口Bing
const getBingOauth = async () => {
  try {
    const bingCode: any = Cookies.get("bingCode") || false;
    console.log(bingCode);
    if (!bingCode && route.query.code) {
      const param = {
        code: route.query.code,
      };
      const loading = ElLoading.service({
        lock: true,
        text: "授权中，请稍等！",
        background: "rgba(0, 0, 0, 0.7)",
      });
      const res: any = await getBingAuth(param);
      loading.close();
      if (res?.code == 200) {
        ElMessage.success("已授权，由于数据量太多请稍后查看！");
        isOauthSuccess.value = true;
        Cookies.set("bingCode", route.query.code);
        await getBingData();
      } else {
        ElMessage.error(res.msg);
        isOauthSuccess.value = false;
      }
      router.replace("/accountManage/authorized-agent")
    } else {
      await getBingData();
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 代理用户授权接口LinkedIn
const getLinkedInOauth = async () => {
  try {
    const linkedInCode: any = Cookies.get("linkedInCode") || false;
    console.log(linkedInCode);
    if (!linkedInCode && route.query.code) {
      const param = {
        code: route.query.code,
      };
      const loading = ElLoading.service({
        lock: true,
        text: "授权中，请稍等！",
        background: "rgba(0, 0, 0, 0.7)",
      });
      const res: any = await getLinkedInAuth(param);
      loading.close();
      if (res?.code == 200) {
        ElMessage.success("已授权，由于数据量太多请稍后查看！");
        isOauthSuccess.value = true;
        Cookies.set("linkedInCode", route.query.code);
        await getLinkedInData();
      } else {
        ElMessage.error(res.msg);
        isOauthSuccess.value = false;
      }
      router.replace("/accountManage/authorized-agent")
    } else {
      await getLinkedInData();
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 获取google数据
const getGoogleData = async () => {
  try {
    loading.value = true;
    const param = {
      thirdCustomerNm: searchForm.value?.thirdCustomerNm,
      state: searchForm.value?.state,
      // pageIndex: page.index,
      // pageSize: page.size,
    };
    const res: any = await getGoogleList(param);
    loading.value = false;
    if (res.code == 200) {
      tableData.value = res?.data?.map((item: any) => {
        return {
          ...item,
          oauthState: item.oauthState == 1 ? "已授权" : "未授权",
          customerView: item.customerView?.list?.map((obj: any) => {
            return {
              ...obj,
              mcc:
                obj.mccId && obj.mccName
                  ? `${obj.mccId}(${obj.mccName})`
                  : "--",
              state: obj.state == 1 ? "正常" : "暂停",
            };
          }),
        };
      });
      isOauthSuccess.value =
        res.data?.agencyCustomer && res.data?.oauthTime ? true : false;

      if (window.microApp) {
        window.microApp.addDataListener((data: any) => {
          if (data.type === "openGuide" && !isSHowTour.value) {
            console.log("openGuide");
            //open.value = true;
          }
          return "";
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 获取yandex数据
const getYandexData = async () => {
  try {
    loading.value = true;
    const param = {
      thirdCustomerNm: searchForm.value?.thirdCustomerNm,
      pageIndex: page.index,
      pageSize: page.size,
    };
    const res: any = await getYandexList(param);
    loading.value = false;
    if (res.code == 200) {
      tableData.value = res?.data?.map((item: any) => {
        return {
          ...item,
          oauthState: item.oauthState == 1 ? "已授权" : "未授权",
          customerView: item.customerView?.list?.map((obj: any) => {
            return {
              ...obj,
              mcc:
                obj.mccId && obj.mccName
                  ? `${obj.mccId}(${obj.mccName})`
                  : "--",
              state: obj.state == 1 ? "正常" : "暂停",
            };
          }),
        };
      });
      page.total = res?.data?.customerView?.total;
      if (window.microApp) {
        window.microApp.addDataListener((data: any) => {
          if (data.type === "openGuide" && !isSHowTour.value) {
            console.log("openGuide");
            //open.value = true;
          }
          return "";
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};

// 获取facebook数据
const getFacebookData = async () => {
  try {
    loading.value = true;
    const param = {
      thirdCustomerNm: searchForm.value?.thirdCustomerNm,
      pageIndex: page.index,
      pageSize: page.size,
    };
    const res: any = await facebookTypeList(param);
    loading.value = false;
    if (res?.code == 200) {
      tableData.value = res?.data?.map((item: any) => {
        return {
          ...item,
          oauthState: item.oauthState == 1 ? "已授权" : "未授权",
          customerView: item.customerView?.list?.map((obj: any) => {
            return {
              ...obj,
              mcc:
                obj.mccId && obj.mccName
                  ? `${obj.mccId}(${obj.mccName})`
                  : "--",
              state: obj.state == 1 ? "正常" : "暂停",
            };
          }),
        };
      });
      if (window.microApp) {
        window.microApp.addDataListener((data: any) => {
          if (data.type === "openGuide" && !isSHowTour.value) {
            console.log("openGuide");
            //open.value = true;
          }
          return "";
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 获取tikTok数据
const getTikTokData = async () => {
  try {
    loading.value = true;
    const param = {
      thirdCustomerNm: searchForm.value?.thirdCustomerNm,
      state: searchForm.value?.state,
      pageIndex: page.index,
      pageSize: page.size,
    };
    const res: any = await tikTokTypeList(param);
    loading.value = false;
    if (res?.code == 200) {
      tableData.value = res?.data?.map((item: any) => {
        return {
          ...item,
          oauthState: item.oauthState == 1 ? "已授权" : "未授权",
          customerView: item.customerView?.list?.map((obj: any) => {
            return {
              ...obj,
              mcc:
                obj.mccId && obj.mccName
                  ? `${obj.mccId}(${obj.mccName})`
                  : "--",
              state: obj.state == 1 ? "正常" : "暂停",
            };
          }),
        };
      });
      if (window.microApp) {
        window.microApp.addDataListener((data: any) => {
          if (data.type === "openGuide" && !isSHowTour.value) {
            console.log("openGuide");
            //open.value = true;
          }
          return "";
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 获取bing数据
const getBingData = async () => {
  try {
    loading.value = true;
    const param = {
      thirdCustomerNm: searchForm.value?.thirdCustomerNm,
      pageIndex: page.index,
      pageSize: page.size,
    };
    const res: any = await getBingList(param);
    loading.value = false;
    if (res?.code == 200) {
      tableData.value = res?.data?.map((item: any) => {
        return {
          ...item,
          oauthState: item.oauthState == 1 ? "已授权" : "未授权",
          customerView: item.customerView?.list?.map((obj: any) => {
            return {
              ...obj,
              mcc:
                obj.mccId && obj.mccName
                  ? `${obj.mccId}(${obj.mccName})`
                  : "--",
              state: obj.state == 1 ? "正常" : "暂停",
            };
          }),
        };
      });
      if (window.microApp) {
        window.microApp.addDataListener((data: any) => {
          if (data.type === "openGuide" && !isSHowTour.value) {
            console.log("openGuide");
            open.value = true;
          }
          return "";
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 获取linkedIn数据
const getLinkedInData = async () => {
  try {
    loading.value = true;
    const param = {
      thirdCustomerNm: searchForm.value?.thirdCustomerNm,
      pageIndex: page.index,
      pageSize: page.size,
    };
    const res: any = await getLinkedInList(param);
    loading.value = false;
    if (res?.code == 200) {
      tableData.value = res?.data?.map((item: any) => {
        return {
          ...item,
          oauthState: item.oauthState == 1 ? "已授权" : "未授权",
          customerView: item.customerView?.list?.map((obj: any) => {
            return {
              ...obj,
              mcc:
                obj.mccId && obj.mccName
                  ? `${obj.mccId}(${obj.mccName})`
                  : "--",
              state: obj.state == 1 ? "正常" : "暂停",
            };
          }),
        };
      });
      if (window.microApp) {
        window.microApp.addDataListener((data: any) => {
          if (data.type === "openGuide" && !isSHowTour.value) {
            console.log("openGuide");
            open.value = true;
          }
          return "";
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 新增授权 g和重新授权
const addAuthorization = async () => {
  // 先清除code和token
  if (props.activeTabs == "2") {
    Cookies.remove("access_token");
  } else if (props.activeTabs == "1") {
    Cookies.remove("googleCode");
  } else {
    Cookies.remove("facebookCode");
  }
  try {
    let methodName: any = null;
    switch (props.activeTabs) {
      case "1":
        methodName = await getGoogleAuthorization();
        break;
      case "2":
        methodName = await getYandexAuthorization();
        break;
      case "3":
        methodName = await facebookAuthorization();
        break;
      case "4":
        methodName = await tikTokAuthorization();
        break;
      case "5":
        methodName = await getBingAuthorization();
        break;
      case "6":
        methodName = await getLinkedInAuthorization();
        break;
    }
    const res: any = methodName;
    console.log(res);
    if (res?.code == 200) {
      window.open(res?.data);
    } else {
      ElMessage.error(res.msg);
    }
    // 埋点
    microForceDispatch("mixpanel", {
      key: "marketingClickAddAuthorization",
      otherData: {
        authorization_type:
          props.activeTabs == "2"
            ? "yandex"
            : props.activeTabs == "1"
            ? "google"
            : props.activeTabs == "3"
            ? "facebook"
            : "tiktok",
      },
    });
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 选择账号
const selectionChange = (arr: []) => {
  console.log(arr);
  customerIdList.value = arr?.map((item: any) => item.customerId);
};
//账户解绑
const unlockAccount = async () => {
  try {
    if (!customerIdList.value.length){
      ElMessageBox.alert("请先勾选广告账户！", "提示", {
        confirmButtonText: "知道了",
        type: "warning"
      })
      return
    }
    const res: any = await unBind({ customerIdList: customerIdList.value });
    if (res.code == 200) {
      search();
      ElMessage.success("账号解绑成功");
    } else {
      ElMessage.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
const search = () => {
  switch (props.activeTabs) {
    case "1":
      googleOauth();
      commonControls.value = !GoogleRuleButtonAuth.value;
      break;
    case "2":
      commonControls.value = !YandexRuleButtonAuth.value;
      yandexOauth();
      break;
    case "3":
      commonControls.value = !FacebookRuleButtonAuth.value;
      getFacebookOauth();
      break;
    case "4":
      commonControls.value = !TikTokRuleButtonAuth.value;
      getTikTokOauth();
      break;
    case "5":
      commonControls.value = !BingRuleButtonAuth.value;
      getBingOauth();
      break;
    case "6":
      commonControls.value = !LinkedInRuleButtonAuth.value;
      getLinkedInOauth();
      break;
    default:
      googleOauth();
      commonControls.value = !GoogleRuleButtonAuth.value;
      break;
  }
  // 埋点
  microForceDispatch("mixpanel", {
    key: "marketingClickSearchAuthorization",
    otherData: {
      search_text: searchForm?.value?.thirdCustomerNm || "",
      account_type:
        (generateStatus.filter(
          (item) => item.value === searchForm?.value?.state
        ) || [])[0]?.label || "",
    },
  });
};
watch(
  () => props.activeTabs,
  (newVal) => {
    if (newVal) {
      search();
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
.zq-user-body {
  box-sizing: border-box;
  background: #ffffff;
  height: calc(100vh - 234px);
  padding: 10px 20px 20px;
  overflow-y: auto;

  .zq-advert-search {
    background: #ffffff;
    margin-bottom: 10px;
    padding-left: 20px;
  }

  .zq-el-collapse {
    height: 60px;

    .authorization {
      font-size: 13px;
      color: var(--el-color-primary);
      font-family: PingFangSC-Regular;
      font-weight: 400;
    }
  }
  .el-collapse-item {
    height: 100%;

    :deep(.el-collapse-item__header) {
      height: 100%;
      background: #f8f9fa;
    }
  }

  .zq-top-authorize {
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    line-height: 22px;
    width: 95%;

    .zq-top-authorize_name {
      width: 100px;
      height: 28px;
      background: #f56c6c;
      border-radius: 0 100px 100px 0;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .zq-top-authorize_item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 18%;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;

      i {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #909399;
      }
    }

    .zq-top-authorize_itemText {
      display: flex;
      align-items: center;

      span {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
      }

      .icon {
        margin-right: 5px;
      }

      .authorization {
        font-size: 13px;
        color: #477fff;
        font-family: PingFangSC-Regular;
        font-weight: 400;
      }
    }
  }
}
</style>
