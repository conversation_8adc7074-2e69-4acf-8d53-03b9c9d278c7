<template>
  <h2 class="ads-title">
    <Breadcrumb />
  </h2>
  <el-tabs
    class="demo-tabs"
    v-model="activeTabs"
    @tab-change="changeTabs"
    style="background-color: #ffffff"
  >
    <el-tab-pane name="1" v-if="GoogleView">
      <template #label>
        <span class="custom-tabs-label">
          <img src="../../../assets/img/Google-icon.png" class="google-icon" />
          <span>Google</span>
        </span>
      </template>
      <Item
        :typeName="'Google'"
        :columns="GoogleColumns"
        :activeTabs="activeTabs"
        v-if="GoogleView && activeTabs == '1'"
        ref="subAccountRef"
      />
    </el-tab-pane>
    <el-tab-pane label="Yandex" name="2" v-if="YandexView">
      <template #label>
        <span class="custom-tabs-label">
          <img src="../../../assets/img/Yandex-icon.png" class="yandex-icon" />
          <span>Yandex</span>
        </span>
      </template>
      <Item
        :typeName="'Yandex'"
        :columns="YandexColumns"
        :activeTabs="activeTabs"
        ref="subAccountRef"
        v-if="YandexView && activeTabs == '2'"
      />
    </el-tab-pane>
    <el-tab-pane label="Facebook" name="3" v-if="FacebookView">
      <template #label>
        <span class="custom-tabs-label">
          <img
            src="../../../assets/img/facebook-icon.png"
            class="facebook-icon"
          />
          <span>Facebook</span>
        </span>
      </template>
      <Item
        :typeName="'Facebook'"
        :columns="FacebookColumns"
        :activeTabs="activeTabs"
        v-if="FacebookView && activeTabs == '3'"
        ref="subAccountRef"
      />
    </el-tab-pane>

    <el-tab-pane label="TikTok" name="4" v-if="TikTokView">
      <template #label>
        <span class="custom-tabs-label">
          <img src="../../../assets/img/tikTok-icon.png" class="tikTok-icon" />
          <span>TikTok</span>
        </span>
      </template>
      <Item
        :typeName="'TikTok'"
        v-if="TikTokView && activeTabs == '4'"
        :columns="FacebookColumns"
        :activeTabs="activeTabs"
        ref="subAccountRef"
      />
    </el-tab-pane>

    <el-tab-pane label="Bing" name="5" v-if="BingView">
      <template #label>
        <span class="custom-tabs-label">
          <img src="../../../assets/img/bing-icon.png" class="bing-icon" />
          <span>Bing</span>
        </span>
      </template>
      <Item
          :typeName="'Bing'"
          v-if="BingView && activeTabs == '5'"
          :columns="BingColumns"
          :activeTabs="activeTabs"
          ref="subAccountRef"
      />
    </el-tab-pane>

    <el-tab-pane label="LinkedIn" name="6" v-if="LinkedInView">
      <template #label>
        <span class="custom-tabs-label">
          <img src="../../../assets/img/Linkedin-icon.png" class="linkedIn-icon" />
          <span>LinkedIn</span>
        </span>
      </template>
      <Item
          :typeName="'LinkedIn'"
          v-if="LinkedInView && activeTabs == '6'"
          :columns="LinkedInColumns"
          :activeTabs="activeTabs"
          ref="subAccountRef"
      />
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import { ref, computed, watch } from "vue";
import Item from "./index.vue";
import usePermissionStore from "@/store/modules/permission";
import { ElMessage } from "element-plus";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const subAccountRef = ref<any>(null);
const permissionStore = usePermissionStore();
const GoogleView = computed(
  () => permissionStore?.buttonAuth?.AuthorizedAgent?.indexOf("GoogleView") > -1
);
const YandexView = computed(
  () => permissionStore.buttonAuth?.AuthorizedAgent?.indexOf("YandexView") > -1
);
const FacebookView = computed(
  () =>
    permissionStore?.buttonAuth?.AuthorizedAgent?.indexOf("FacebookView") > -1
);
const TikTokView = computed(
  () => permissionStore.buttonAuth?.AuthorizedAgent?.indexOf("TikTokView") > -1
);
const BingView = computed(
    () => permissionStore.buttonAuth?.AuthorizedAgent?.indexOf("BingADSView") > -1
);
const LinkedInView = computed(
    () => permissionStore.buttonAuth?.AuthorizedAgent?.indexOf("LinkInADSView") > -1
);

const activeTabs = ref(localStorage.getItem("accountTabs") || "1");

watch(
  () => activeTabs.value,
  (newValue) => {
    activeTabs.value = newValue;
  }
);
// Google columes
let GoogleColumns = ref([
  { type: "selection", width: 55 },
  {
    prop: "thirdCustomerName",
    label: "广告账户名称",
    width: 255,
    align: "center",
  },
  { prop: "thirdCustomerId", label: "广告账户ID" },
  { prop: "state", label: "状态" }, //状态 1：正常2:暂停
  { prop: "companyName", label: "公司名称" },
  { prop: "mcc", label: "所属MCC" },
  // { prop: "remainAmount", label: "余额(￥)" },
]);
console.log(permissionStore?.buttonAuth?.AuthorizedAgent, "permissionStore-------------");
// Yandex cloumns
let YandexColumns = ref([
  { type: "selection", width: 55 },
  {
    prop: "thirdCustomerName",
    label: "广告账户名称",
    width: 155,
    align: "center",
  },
  { prop: "thirdCustomerId", label: "账户ID" },
  // { prop: "state", label: "状态", },//状态 1：正常2:暂停
  { prop: "companyName", label: "公司名称" },
  // { prop: "remainAmount", label: "余额($)" },
]);
let FacebookColumns = ref([
  { type: "selection", width: 55 },
  {
    prop: "thirdCustomerName",
    label: "广告账户名称",
    width: 155,
    align: "center",
  },
  { prop: "thirdCustomerId", label: "广告账户ID" },
  { prop: "state", label: "状态" }, //状态 1：正常2:暂停
  { prop: "companyName", label: "公司名称" },
  { prop: "mccName", label: "所属BM" },
]);
let BingColumns = ref([
  { type: "selection", width: 55 },
  {
    prop: "thirdCustomerName",
    label: "广告账户名称",
    width: 155,
    align: "center",
  },
  { prop: "thirdCustomerId", label: "广告账户ID" },
  { prop: "state", label: "状态" }, //状态 1：正常2:暂停
  { prop: "remainAmount", label: "余额(USD)" },
]);
let LinkedInColumns = ref([
  { type: "selection", width: 55 },
  {
    prop: "thirdCustomerName",
    label: "广告账户名称",
    width: 155,
    align: "center",
  },
  { prop: "thirdCustomerId", label: "广告账户ID" },
  { prop: "state", label: "状态" }, //状态 1：正常2:暂停
]);
const changeTabs = (name: string) => {
  console.log(name, GoogleView.value);
  activeTabs.value = name;
  localStorage.setItem("accountTabs", activeTabs.value);

  if (!GoogleView.value && name == "1") {
    return ElMessage.error("您尚未购买Google广告服务，如有需要请联系商务代表!");
  }
  if (!YandexView.value && name == "2") {
    return ElMessage.error("您尚未购买Yandex广告服务，如有需要请联系商务代表!");
  }
  if (!FacebookView.value && name == "3") {
    return ElMessage.error(
      "您尚未购买Facebook广告服务，如有需要请联系商务代表!"
    );
  }
  if (!TikTokView.value && name == "4") {
    return ElMessage.error("您尚未购买TikTok广告服务，如有需要请联系商务代表!");
  }
  if (!BingView.value && name == "5") {
    return ElMessage.error("您尚未购买Bing广告服务，如有需要请联系商务代表!");
  }
  if (!LinkedInView.value && name == "6") {
    return ElMessage.error("您尚未购买LinkedIn广告服务，如有需要请联系商务代表!");
  }
};
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 20px;
  color: #202020;
  margin: 0px 0 20px;
}

.demo-tabs {
  box-sizing: border-box;
  padding: 20px;
  height: calc(100vh - 160px);
}

.custom-tabs-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;

  .google-icon,
  .yandex-icon,
  .tikTok-icon,
  .facebook-icon,
  .bing-icon,
  .linkedIn-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}
</style>
