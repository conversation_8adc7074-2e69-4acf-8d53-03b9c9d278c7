<template>
  <div class="ads-google_data" v-loading="loading">
    <div class="ads-data_card_body">
      <div class="data_card_body_center">
        <div class="ads-data_card">
          <span class="card_title">展示次数</span>
          <h4
            class="card_h4"
            :class="{ card_null: !dataCenterInfo?.account.impressions }"
          >
            {{ dataCenterInfo?.account.impressions || "--" }}<span>次</span>
          </h4>
          <div class="card_line"></div>
        </div>
        <div class="ads-data_card">
          <span class="card_title">点击次数</span>
          <h4 class="card_h4" :class="{ card_null: !dataCenterInfo?.account.clicks }">
            {{ dataCenterInfo?.account.clicks || "--" }}<span>次</span>
          </h4>
          <div class="card_line"></div>
        </div>
        <div class="ads-data_card">
          <span class="card_title">平均每次点击费用</span>
          <h4 class="card_h4" :class="{ card_null: !dataCenterInfo?.account.cpc }">
            {{ dataCenterInfo?.account.cpc || "--"
            }}<span>{{ activeTabs == "1" ? "元/次" : "美元/次" }}</span>
          </h4>
          <div class="card_line"></div>
        </div>
        <div class="ads-data_card">
          <span class="card_title">总消耗金额</span>
          <h4 class="card_h4" :class="{ card_null: !dataCenterInfo?.account.totalCost }">
            {{ dataCenterInfo?.account.totalCost || "--"
            }}<span>{{ activeTabs == "1" ? "元" : "美元" }}</span>
          </h4>
          <div class="card_line"></div>
        </div>
        <div class="ads-data_card">
          <span class="card_title">平均日消耗</span>
          <h4 class="card_h4" :class="{ card_null: !dataCenterInfo?.account.avgDayCost }">
            {{ dataCenterInfo?.account.avgDayCost || "--"
            }}<span>{{ activeTabs == "1" ? "元/日" : "美元/日" }}</span>
          </h4>
        </div>
        <!-- <div class="ads-data_card">
                    <span class="card_title">账户余额</span>
                    <h4 class="card_h4">{{ dataCenterInfo?.account.remainAmount }}<span>元</span></h4>
                </div> -->
      </div>
    </div>
    <div class="ads-data_line_body">
      <div class="ads-data_line_title">
        <h4>营销效果数据趋势<span>（最多可选两条）</span></h4>
        <el-checkbox-group v-model="checkList" @change="changeLineData" :max="2">
          <el-checkbox label="展示次数" value="impressions" />
          <el-checkbox label="点击次数" value="clicks" />
          <el-checkbox label="点击率" value="ctr" />
          <el-checkbox label="平均每次点击费用" value="cpc" />
          <el-checkbox label="总消耗金额" value="cost" />
          <!-- <el-checkbox label="账户余额" value="remainAmount" /> -->
        </el-checkbox-group>
        <span style="flex: 1"></span>
      </div>
      <div ref="chartDataPreview" style="width: 100%; height: 331px"></div>
    </div>
    <div class="ads-data_table">
      <div class="data_table_item" v-if="activeTabs !== '3' && activeTabs !== '4'">
        <div class="data_table_item_header">
          <h5>关键词明细</h5>
          <p></p>
          <div
            class="data_table_item_header_screen"
            @click="
              fullScreen('关键词明细', dataCenterInfo?.keyworkdsList, keWordColumns)
            "
          >
            <el-icon><FullScreen /></el-icon>
            <span>全屏</span>
          </div>
        </div>
        <TableCustom
          :height="400"
          :columns="keWordColumns"
          :tableData="dataCenterInfo?.keyworkdsList"
          style="width: 100%"
          :isSortable="true"
          :adminShow="true"
        >
        </TableCustom>
      </div>
      <div
        class="data_table_item"
        :style="{ width: activeTabs !== '3' && activeTabs !== '4' ? '49%' : '100%' }"
      >
        <div class="data_table_item_header">
          <h5>地理位置明细</h5>
          <p></p>
          <div
            class="data_table_item_header_screen"
            @click="
              fullScreen('地理位置明细', dataCenterInfo?.countryList, addressColumns)
            "
          >
            <el-icon><FullScreen /></el-icon>
            <span>全屏</span>
          </div>
        </div>
        <TableCustom
          :height="400"
          :columns="
            activeTabs == '3'
              ? addressColumnsFacebook
              : activeTabs == '4'
              ? addressColumnsTikTok
              : addressColumns
          "
          :tableData="dataCenterInfo?.countryList"
          style="width: 100%"
          :isSortable="true"
          :adminShow="true"
        >
        </TableCustom>
      </div>
    </div>
    <el-dialog v-model="showFullScreen" :title="dialogTitle" width="70%">
      <TableCustom
        :columns="dialogTableColumns"
        :tableData="paginationTable"
        style="width: 100%"
        :total="dialogTable?.length"
        :currentPage="page.currentPage"
        :pageSize="page.pageSize"
        :changePage="handleCurrentChange"
        :changePageSize="handleCurrentChangePageSize"
        :isShowPagination="true"
        :isSortable="true"
        :adminShow="true"
      >
      </TableCustom>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, defineProps, watch } from "vue";
import TableCustom from "@/components/table-custom.vue";
import { FullScreen } from "@element-plus/icons-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
const props = defineProps({
  activeTabs: {
    type: String,
    default: "1",
  },
  selectTime: {
    type: String,
    default: "seven",
  },
  dataCenterInfo: {
    type: Object,
    default: () => {
      return {
        account: {
          impressions: "--",
          clicks: "--",
          cpc: "--",
          totalCost: "--",
          avgDayCost: "--",
        },
        keyworkdsList: [],
        countryList: [],
        dayList: [],
      };
    },
  },
});
let page = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const showFullScreen = ref(false);
const impressionsList = ref([]); // 展示次数列表
const clicksList = ref([]); // 点击次数列表
const cpcList = ref([]); // 平均每次点击费用
const ctrList = ref([]); // 点击率
const costList = ref([]); // 总消耗金额
// const valueCList = ref([]) // 账户余额 TODO
const timeList = ref([]); // 时间列表
const seriesList = ref([]);
const nameMapping = {
  impressions: "展示次数",
  clicks: "点击次数",
  ctr: "点击率",
  cpc: "平均每次点击费用",
  cost: "总消耗金额",
  // "remainAmount": "账户余额" TODO
};
const paginationTable = ref<any[]>();
const chartDataPreview = ref<any>(null);
const dialogTitle = ref("关键词明细");
const dialogTable = ref<any[]>();
const dialogTableColumns = ref<any[]>();
let keWordColumns = ref<any>([
  { prop: "clicks", label: "点击次数", width: 120 },
  { prop: "impressions", label: "展示次数", width: 120 },
  { prop: "ctr", label: "点击率", width: 120 },
  { prop: "cpc", label: "平均每次点击费用", width: 170 },
  { prop: "cost", label: "总消耗", width: 120 },
]);
let addressColumns = ref([
  { prop: "positionName", label: "国家/地区" },
  { prop: "clicks", label: "点击次数" },
  { prop: "impressions", label: "展示次数" },
  { prop: "ctr", label: "点击率" },
  { prop: "cpc", label: "平均每次点击费用" },
  { prop: "cost", label: "总费用" },
]);
let addressColumnsFacebook = ref([
  { prop: "positionName", label: "国家" },
  { prop: "impressions", label: "展示量" },
  { prop: "clicks", label: "点击量" },
  { prop: "cpm", label: "CPM($)" },
  { prop: "cpc", label: "平均每次点击费用" },
  { prop: "ctr", label: "点击率" },
  { prop: "convCost", label: "CPA($)" },
  { prop: "conversions", label: "成效多种转换" },
  { prop: "cost", label: "总费用" },
]);
let addressColumnsTikTok = ref([
  { prop: "positionName", label: "国家" },
  { prop: "impressions", label: "展示量" },
  { prop: "clicks", label: "点击量" },
  { prop: "cpm", label: "CPM($)" },
  { prop: "cpc", label: "CPC($)" },
  { prop: "ctr", label: "点击率(%)" },
  { prop: "convCost", label: "CPA($)" },
  { prop: "conversions", label: "转化量" },
  { prop: "cost", label: "总消耗" },
]);
const checkList = ref(["clicks", "cost"]);
const loading = ref(false);
watch(
  () => props.activeTabs,
  (newVal) => {
    if (newVal == "1") {
      keWordColumns.value = [
        {
          prop: "keyWords",
          label: "关键词",
        },
      ].concat(keWordColumns.value);
    } else {
      keWordColumns.value = [
        {
          prop: "keyWords",
          label: "Impression criteria",
          width: 155,
          align: "center",
        },
        { prop: "keyWordsZh", label: "展示词" },
      ].concat(keWordColumns.value);
    }
  },
  { immediate: true }
);
watch(
  () => props.dataCenterInfo,
  (newVal) => {
    console.log(newVal);
    if (newVal) {
      impressionsList.value = newVal.dayList?.map((item: any) => item.impressions);
      clicksList.value = newVal.dayList?.map((item: any) => item.clicks);
      cpcList.value = newVal.dayList?.map((item: any) => item.cpc);
      ctrList.value = newVal.dayList?.map((item: any) => item.ctr);
      costList.value = newVal.dayList?.map((item: any) => item.cost);
      timeList.value = newVal.dayList?.map((item: any) =>
        dayjs(item.reportDate).format("YYYY-MM-DD")
      );
      setChartsList(checkList.value);
      console.log(checkList.value);
      setTimeout(() => {
        initBarAndLine();
      }, 100);
    }
  },
  { deep: true }
);
watch(
  () => seriesList.value,
  (val) => {
    seriesList.value = val;
  },
  { deep: true }
);
// 初始化echarts数据
const initBarAndLine = () => {
  // 设置图表的选项和数据
  let myChart = echarts.init(chartDataPreview.value);
  const option = {
    xAxis: {
      type: "category",
      // data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // timeList.value
      data: timeList.value,
    },
    yAxis: {
      type: "value",
    },
    grid: {
      left: "3%",
      right: "3%",
      bottom: "3%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: seriesList.value?.map((item) => item.name),
      align: "right",
      selectedMode: false, // 是否允许点击
    },
    color: ["#1C78B5", "#FFD528", "#FFD898", "#FFD238"],
    series: [...seriesList.value],
  };
  // 使用设置好的选项显示图表
  myChart.setOption(option, { notMerge: true });
};
// 改变勾选显示不同线条
const changeLineData = (val) => {
  setChartsList(val);
  setTimeout(() => {
    initBarAndLine();
  }, 100);
  console.log(seriesList.value);
};
const setChartsList = (val: any[]) => {
  seriesList.value = [];
  val.forEach((item: any) => {
    seriesList.value.push({
      data:
        item == "impressions"
          ? impressionsList.value
          : item == "clicks"
          ? clicksList.value
          : item == "cpc"
          ? cpcList.value
          : item == "cost"
          ? costList.value
          : ctrList.value,
      smooth: true,
      type: "line",
      name: nameMapping[item],
    });
  });
};
const fullScreen = (name: string, tableList: any, columns: any) => {
  showFullScreen.value = true;
  dialogTitle.value = name;
  dialogTable.value = tableList;
  handleCurrentChange(1);
  dialogTableColumns.value = columns;
};
const handleCurrentChange = (val: number) => {
  page.currentPage = val;
  console.log((val - 1) * 10, val * 10 - 1);
  paginationTable.value = dialogTable.value?.slice(
    (val - 1) * page.pageSize,
    val * page.pageSize
  );
  console.log(paginationTable.value);
};
const handleCurrentChangePageSize = (val: number) => {
  // 当前页每页条数
  console.log(val);
  page.pageSize = val;
  handleCurrentChange(1);
  // paginationTable.value = dialogTable.value?.slice((val-1) * page.pageSize, val * page.pageSize )
};
</script>
<style lang="scss" scoped>
.ads-google_data {
  // display: flex;
  // flex-direction: column;
  height: calc(100vh - 140px);
  overflow-y: auto;
  background-color: rgb(242, 243, 245);

  .ads-data_card_body {
    height: 149px;
    width: 100%;
    padding: 24px;
    background-color: #ffffff;

    .data_card_body_center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      background: #f8f8f9;

      .ads-data_card {
        box-sizing: border-box;
        width: 256px;
        height: 112px;
        padding: 20px;
        position: relative;

        .card_line {
          position: absolute;
          top: 25px;
          right: 0;
          width: 1px;
          height: 62px;
          border-right: 1px solid #dcdfe6;
        }

        .card_title {
          font-size: 14px;
          color: #909399;
          font-family: PingFangSC-Regular;
          font-weight: 400;
        }

        .card_h4 {
          font-size: 28px;
          font-weight: 700;
          font-family: Helvetica;
          color: #303133;
          span {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #909399;
            margin-left: 5px;
          }
        }
        .card_null {
          font-weight: 400;
        }

        .card_bottom {
          font-size: 12px;
          color: #333333;
        }
      }
    }
  }

  .ads-data_line_body {
    box-sizing: border-box;
    width: 100%;
    // height: 500px;
    background-color: #ffffff;
    // border-radius: 15px;
    padding: 24px;

    h4 {
      color: #161617;
      font-size: 21px;
      font-family: PingFangSC;
      font-weight: 500;
      line-height: 0;
      text-align: left;
      flex: 1;
    }

    .ads-data_line_title {
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      h4 {
        font-size: 16px;
        font-family: PingFangSC;
        font-weight: bold;
        color: #161617;

        span {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 400;
          color: #909399;
        }
      }
    }
  }

  .ads-data_table {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
    margin-top: 16px;

    .data_table_item {
      width: 49%;
      background-color: #ffffff;
      padding: 24px;
      .data_table_item_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        h5 {
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: 16px;
          color: #161617;
        }
        p {
          flex: 1;
        }
        .data_table_item_header_screen {
          display: flex;
          align-items: center;
          color: #909399;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          cursor: pointer;
          span {
            margin-left: 5px;
          }
        }
      }
    }
  }
}
</style>
