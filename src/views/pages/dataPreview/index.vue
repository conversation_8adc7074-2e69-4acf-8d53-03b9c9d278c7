<template>
  <div class="ads-dataPreview">
    <div class="ads-dataPreview__header">
      <h2>广告数据</h2>
      <div v-if="selectTime == 'customTime'" style="width: 260px; margin-right: 10px">
        <el-date-picker
          style="width: 100%"
          v-model="customTime"
          type="daterange"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          range-separator="至"
          start-placeholder="开始日期"
          :disabled-date="pickerOptionsRang"
          size="default"
          @change="changeCustomTime"
          end-placeholder="结束日期"
        />
      </div>
      <el-radio-group v-model="selectTime" @change="selectTimeChange">
        <el-radio-button value="seven">最近7天</el-radio-button>
        <el-radio-button value="thirty">最近30天</el-radio-button>
        <el-radio-button value="customTime">自定义日期</el-radio-button>
      </el-radio-group>
    </div>
    <el-tabs
      v-model="activeName"
      class="demo-tabs"
      @tab-change="handleChange"
      v-loading="loading"
    >
      <el-tab-pane label="Google" name="1">
        <template #label>
          <span class="custom-tabs-label">
            <img src="../../../assets/img/Google-icon.png" class="google-icon" />
            <span>Google</span>
          </span>
        </template>
        <DataCenterComponents
          v-if="activeName == '1'"
          :activeTabs="activeName"
          :selectTime="selectTime"
          :dataCenterInfo="dataCenterInfo"
        />
      </el-tab-pane>
      <el-tab-pane label="Yandex" name="2">
        <template #label>
          <span class="custom-tabs-label">
            <img src="../../../assets/img/Yandex-icon.png" class="google-icon" />
            <span>Yandex</span>
          </span>
        </template>
        <DataCenterComponents
          v-if="activeName == '2'"
          :activeTabs="activeName"
          :selectTime="selectTime"
          :dataCenterInfo="dataCenterInfo"
        />
      </el-tab-pane>
      <el-tab-pane label="Yandex" name="3">
        <template #label>
          <span class="custom-tabs-label">
            <img src="../../../assets/img/facebook-icon.png" class="facebook-icon" />
            <span>Facebook</span>
          </span>
        </template>
        <DataCenterComponents
          v-if="activeName == '3'"
          :activeTabs="activeName"
          :selectTime="selectTime"
          :dataCenterInfo="dataCenterInfo"
        />
      </el-tab-pane>
      <el-tab-pane label="TikTok" name="4">
        <template #label>
          <span class="custom-tabs-label">
            <img src="../../../assets/img/tikTok-icon.png" class="tikTok-icon" />
            <span>TikTok</span>
          </span>
        </template>
        <DataCenterComponents
          v-if="activeName == '4'"
          :activeTabs="activeName"
          :selectTime="selectTime"
          :dataCenterInfo="dataCenterInfo"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import DataCenterComponents from "./components/dataCenterComponents.vue";
import { getQueryReportDataView } from "@/api/dataPreviewApi";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { microForceDispatch } from "@/utils";
const activeName = ref("1");
const selectTime = ref("seven");
const dataTime = dayjs(new Date().getTime() - ********).format("YYYY-MM-DD"); // 获取当前年份
const dataTimeSeven = dayjs(new Date().getTime() - ******** * 7).format("YYYY-MM-DD"); // 获取当前年份
const reportStartDate = ref(dataTimeSeven);
const reportEndDate = ref(dataTime);
const customTime = ref<any>();
const defaultObject = {
  account: {
    impressions: "",
    clicks: "",
    cpc: "",
    totalCost: "",
    avgDayCost: "",
  },
  keyworkdsList: [],
  countryList: [],
  dayList: [],
};
const dataCenterInfo = ref<any>(null);
const loading = ref(false);
const pickerOptionsRang = (time) => {
  // 禁用当前日期之后的所有日期
  return Date.now() - *********** > time.getTime() || time.getTime() > Date.now(); // 8.64e7 毫秒数代表一天
};
onMounted(() => {
  getQueryReportDataViewData();
});
const getQueryReportDataViewData = async () => {
  const params = {
    // thirdCustomerId: "", // 三方客户ID 暂时无用
    mediumType: activeName.value, // 媒体类型（1-google，2-yandex）
    reportStartDate: reportStartDate.value + " 23:59:59", // 报表开始时间
    reportEndDate: reportEndDate.value + " 23:59:59", // 报表结束时间
  };
  loading.value = true;
  const res: any = await getQueryReportDataView(params);
  loading.value = false;
  console.log(res);
  if (res?.code == 200) {
    dataCenterInfo.value = JSON.stringify(res?.data) !== "{}" ? res.data : defaultObject;
  } else {
    ElMessage.error(res?.msg);
  }
};
const handleChange = (name: string) => {
  console.log(name);
  dataCenterInfo.value = null;
  getQueryReportDataViewData();
  // 埋点
  microForceDispatch("mixpanel", {
    key: "marketingClickCheckDataCenter",
    otherData: {
      data_type: name === "1" ? "google" : "yandex",
    },
  });
};
const changeCustomTime = (val: any) => {
  console.log(val);
  reportStartDate.value = val[0];
  reportEndDate.value = val[1];
  getQueryReportDataViewData();
};
const selectTimeChange = (val: string) => {
  console.log(val);
  switch (val) {
    case "seven": // 7天
      reportStartDate.value = dataTimeSeven;
      reportEndDate.value = dataTime;
      break;
    case "thirty": // 30天
      const thirtyTime = dayjs(new Date().getTime() - ******** * 31).format("YYYY-MM-DD"); // 获取当前年份
      reportStartDate.value = thirtyTime;
      reportEndDate.value = dataTime;
      break;
  }
  getQueryReportDataViewData();
};
</script>
<style lang="scss" scoped>
.ads-dataPreview {
  .ads-dataPreview__header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 64px;

    h2 {
      flex: 1;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 20px;
      color: #202020;
      margin: 20px 0;
    }
  }

  .demo-tabs {
    height: 100%;
    background-color: #ffffff;
    // padding: 24px;
    box-sizing: border-box;
  }
}

.custom-tabs-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;

  .google-icon,
  .facebook-icon,
  .tikTok-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}
.demo-tabs {
  :deep(.el-tabs__header) {
    padding: 24px 24px 0 24px;
  }
}
</style>
