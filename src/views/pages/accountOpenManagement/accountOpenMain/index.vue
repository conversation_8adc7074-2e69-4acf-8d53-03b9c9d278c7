<template>
  <div>
    <div class="main-container">
      <div class="title">
        <Breadcrumb />
      </div>
      <dynamicTable
        ref="dynamicTableRef"
        :columns="columns"
        :initialData="tableData"
        :form-items="formItems"
        :loading="tableLoading"
        @search="getAccountMainList"
      >
        <template #formBtn>
          <el-button
            type="primary"
            @click="addMainBody"
            v-if="accountOpeningSubjectControls"
            >新增主体</el-button
          >
          <el-button
            type="primary"
            @click="fileDownload"
            v-if="accountOpeningSubjectControls"
          >
            excel导出
          </el-button>
        </template>
        <template #custom="{ row }">
          <el-link
            type="primary"
            :underline="false"
            :href="row.businessLicenseUrl"
            target="_blank"
            >营业执照</el-link
          >
        </template>
      </dynamicTable>
    </div>
    <addBodyDialog
      v-model:visible="addBodyShow"
      v-if="addBodyShow"
      :type="DialogType"
      :row-data="currentRow"
      @close="closeDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, h, onMounted, computed } from "vue";
import dynamicTable from "@/components/dynamicTable.vue";
import addBodyDialog from "./component/addBodyDialog.vue";
import { ElInput, ElButton } from "element-plus";
import { getAccountListApi } from "@/api/openAccount/index";
import { downloadFile } from "@/utils/common";
import usePermissionStore from "@/store/modules/permission";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const permissionStore = usePermissionStore();
const accountOpeningSubjectControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountOpenReport?.indexOf(
      "accountOpeningSubjectControls",
    ) > -1,
);
const addBodyShow = ref(false);
let DialogType = ref("add");
let currentRow = ref();
const closeDialog = () => {
  dynamicTableRef.value?.fetchData();
};
const columns = [
  {
    label: "开户主体",
    prop: "customerMainName",
  },
  {
    label: "联系人姓名",
    prop: "contactsName",
  },
  {
    label: "联系人手机号",
    prop: "contactsPhone",
  },
  {
    label: "联系人邮箱 ",
    prop: "contactsEmail",
  },
  {
    label: "营业执照",
    prop: "businessLicenseUrl",
    type: "custom",
  },
  {
    label: "统一社会信用编码",
    prop: "unifiedSocialCreditCode",
  },
  {
    label: "操作",
    prop: (row) => btnElement(row),
    width: 100,
  },
];
const formItems = [
  {
    label: "开户主体",
    prop: "customerMainName",
    component: ElInput,
  },
];

const btnElement = (row: any) => {
  const btnList = [
    {
      label: "编辑",
      type: 1,
      callback: btnAction,
      visible: !accountOpeningSubjectControls.value,
      permission: "openAccountBodyBtn",
      color: "#519C66",
      bgColor: "rgba(50, 147, 111, 0.1)"
    },
  ]?.filter((item) => {
    return !item.visible;
  });
  return h(
    "div",
    {},
    btnList.map((item, index) => {
      return h(
        ElButton,
        {
          class:"table-button",
          // permission: { value: item.permission },
          onClick: () => item.callback(row, item.type),
        },
        () => item.label,
      );
    }),
  );
};
const btnAction = (row: any, type: number) => {
  switch (type) {
    case 1:
      currentRow.value = row;
      DialogType.value = "edit";
      addBodyShow.value = true;
      break;
  }
};
let dynamicTableRef = ref();
let baseParams = ref({
  pageIndex: 1,
  pageSize: 10,
});
// 导出
const fileDownload = () => {
  const isPre = window.location.href.indexOf("pre") > -1;
  const isTest = window.location.href.indexOf("test-") > -1;
  const url = "https://pre-ads.gmarketing.tech";
  const urlTest = "https://test-ads.gmarketing.tech";
  downloadFile({
    url:
      (isPre ? url : isTest ? urlTest : import.meta.env?.VITE_REQUEST_URL) +
      "/ad-trade-web/customer/mainList/viewDownload",
    params: baseParams.value,
    method: "POST",
    headers: {},
    name: "开户主体",
  });
};
const addMainBody = () => {
  DialogType.value = "add";
  addBodyShow.value = true;
};
let tableData = ref([]) as any;
let tableLoading = ref(false);
const getAccountMainList = (value?) => {
  let data = {
    pageIndex: value ? value["currentPage"] : baseParams.value.pageIndex,
    pageSize: value ? value["pageSize"] : baseParams.value.pageSize,
    customerMainName: value ? value["customerMainName"] : "",
  };
  baseParams.value = data;
  tableLoading.value = true;
  getAccountListApi(data)
    .then((res: any) => {
      tableData.value = res.data.list || [];
      dynamicTableRef.value?.setPageTotal(Number(res.data.total));
    })
    .finally(() => {
      tableLoading.value = false;
    });
};
onMounted(() => {
  getAccountMainList();
});
</script>

<style scoped lang="scss">
.main-container {
  padding-bottom: 20px;

  .title {
    color: #202020;
    font-size: 20px;
    font-weight: 500;
    font-family: "PingFang SC";
    margin-bottom: 20px;
  }
}
</style>
