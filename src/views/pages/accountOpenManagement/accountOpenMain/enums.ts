export enum EAddBody {
  //  营业执照Url
  BUSINESS_LICENSE_URL = "businessLicenseUrl",
  //   客户主体名称
  CUSTOMER_MAIN_NAME = "customerMainName",
  //   统一社会信用代码
  UNIFIED_SOCIAL_CREDIT_CODE = "unifiedSocialCreditCode",
  //  公司注册地址
  ADDRESS = "address",
  //   公司注册地址邮编
  POSTAL_CODE = "postalCode",
  //   联系人姓名
  CONTACTS_NAME = "contactsName",
  //   联系人手机号
  CONTACTS_PHONE = "contactsPhone",
  //   联系人邮箱ß
  CONTACTS_EMAIL = "contactsEmail",
  //   法人
  LEGAL_PERSON = "legalPerson",
  //   注册资本
  CAPITAL = "capital",
  // 成立日期
  SETDATE = "setDate",
  // 类型
  TYPE = "type",
  // 类型
  PERIOD = "period",
}
