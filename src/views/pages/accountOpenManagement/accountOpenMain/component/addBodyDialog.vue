<template>
  <div>
    <el-dialog
      v-model="props.visible"
      :title="`${props.type == 'edit' ? '编辑' : '新增'}主体`"
      width="750"
      @closed="closeDialog"
    >
      <div>
        <el-form
          ref="formRef"
          label-position="right"
          label-width="150"
          :model="form"
          style="max-width: 600px"
          :rules="rules"
        >
          <el-form-item label="营业执照" :prop="EAddBody.BUSINESS_LICENSE_URL">
            <div>
              <el-upload
                v-if="!uploadData?.fileUrl"
                style="width: 450px"
                drag
                name="upLoadFile"
                :show-file-list="false"
                :action="uploadUrl"
                :on-success="handleSuccess"
                :before-upload="beforeUploadHandler"
                :headers="{
                  Authorization: token,
                }"
                v-loading="uploadLoading"
                :limit="1"
                accept="jpeg,png,jpg"
                :with-credentials="true"
                :disabled="props.type == 'edit'"
              >
                <div>
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    把文件拖放到这里来上传或 <em>选择设备中的文件</em>
                  </div>
                </div>
              </el-upload>
              <div class="upload-img" v-else>
                <el-image
                  style="width: 100%; height: 100%"
                  :src="uploadData?.fileUrl"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="[uploadData?.fileUrl]"
                  :initial-index="4"
                  fit="cover"
                />
                <span @click="cancelImg" v-if="type !== 'edit'">
                  <el-icon><CircleClose /></el-icon
                ></span>
              </div>
              <div>
                <div class="el-upload__tip">
                  最大可上传5M，接受的格式包括：jpg、jpeg、png、
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="开户主体" :prop="EAddBody.CUSTOMER_MAIN_NAME">
            <el-input
              v-model="form[EAddBody.CUSTOMER_MAIN_NAME]"
              placeholder="请输入开户主体"
              :disabled="props.type == 'edit'"
              maxlength="100"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="统一社会信用代码"
            :prop="EAddBody.UNIFIED_SOCIAL_CREDIT_CODE"
          >
            <el-input
              v-model="form[EAddBody.UNIFIED_SOCIAL_CREDIT_CODE]"
              placeholder="请输入统一社会信用代码"
              :disabled="props.type == 'edit'"
              maxlength="18"
              type="text"
              @input="handleCodeInput"
            />
          </el-form-item>
          <el-form-item label="法人" :prop="EAddBody.LEGAL_PERSON">
            <el-input
              v-model="form[EAddBody.LEGAL_PERSON]"
              placeholder="请输入法人"
              :disabled="props.type == 'edit'"
              maxlength="18"
              type="text"
            />
          </el-form-item>
          <el-form-item label="营业期限">
            <el-date-picker
              v-model="form[EAddBody.PERIOD]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :clearable="false"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="props.type == 'edit'"
            />
          </el-form-item>
          <el-form-item label="类型" :prop="EAddBody.TYPE">
            <el-input
              v-model="form[EAddBody.TYPE]"
              placeholder="请输入公司类型"
              :disabled="props.type == 'edit'"
              maxlength="18"
              type="text"
            />
          </el-form-item>
          <el-form-item label="注册资本" :prop="EAddBody.CAPITAL">
            <el-input
              v-model="form[EAddBody.CAPITAL]"
              placeholder="请输入注册资本"
              :disabled="props.type == 'edit'"
              maxlength="18"
              type="text"
            />
          </el-form-item>
          <el-form-item label="成立日期" :prop="EAddBody.SETDATE">
            <el-input
              v-model="form[EAddBody.SETDATE]"
              placeholder="注册日期"
              :disabled="props.type == 'edit'"
              maxlength="18"
              type="text"
            />
          </el-form-item>
          <el-form-item label="公司注册地址" :prop="EAddBody.ADDRESS">
            <el-input
              v-model="form[EAddBody.ADDRESS]"
              placeholder="请输入公司注册地址"
              :disabled="props.type == 'edit'"
              maxlength="100"
              type="text"
            />
          </el-form-item>
          <el-form-item label="公司注册地址邮编" :prop="EAddBody.POSTAL_CODE">
            <el-input
              v-model="form[EAddBody.POSTAL_CODE]"
              placeholder="请输入公司注册地址邮编"
              :disabled="props.type == 'edit'"
              maxlength="100"
              type="text"
            />
          </el-form-item>
          <el-form-item label="联系人姓名" :prop="EAddBody.CONTACTS_NAME">
            <el-input
              v-model="form[EAddBody.CONTACTS_NAME]"
              placeholder="请输入联系人姓名"
              maxlength="100"
              type="text"
            />
          </el-form-item>
          <el-form-item label="联系人手机号" :prop="EAddBody.CONTACTS_PHONE">
            <el-input
              v-model="form[EAddBody.CONTACTS_PHONE]"
              placeholder="请输入联系人手机号"
              @input="handlePhoneInput"
              maxlength="11"
              type="text"
            />
          </el-form-item>
          <el-form-item label="联系人邮箱" :prop="EAddBody.CONTACTS_EMAIL">
            <el-input
              v-model="form[EAddBody.CONTACTS_EMAIL]"
              placeholder="请输入联系人邮箱"
              maxlength="100"
              type="text"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            :loading="btnLoading"
            @click="submit(formRef)"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, defineProps, onMounted } from "vue";
import { UploadFilled } from "@element-plus/icons-vue";
import {
  addAndUpdateAccountMainApi,
  getBusCodeApi,
  getMainDetailApi,
} from "@/api/openAccount/index";
import { EAddBody } from "../enums";
import { getToken } from "@/utils/auth";
import { ElMessage } from "element-plus";
import { getEnvPrefix } from "@/config";

let uploadUrl = ref(
  `https://${getEnvPrefix()}ads.gmarketing.tech/ad-trade-web/base/file/upload`,
);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "add",
  },
  rowData: {
    type: Object,
    default: () => {},
  },
});
let token = getToken();
// 输入时强制数字
const handlePhoneInput = (value) => {
  form[EAddBody.CONTACTS_PHONE] = value.replace(/[^\d]/g, "");
};
const emits = defineEmits(["update:visible", "close"]);
const formRef = ref();
let btnLoading = ref(false);
const form = reactive<any>({
  [EAddBody.CUSTOMER_MAIN_NAME]: "",
  [EAddBody.UNIFIED_SOCIAL_CREDIT_CODE]: "",
  [EAddBody.POSTAL_CODE]: "",
  [EAddBody.ADDRESS]: "",
  [EAddBody.CONTACTS_NAME]: "",
  [EAddBody.CONTACTS_PHONE]: "",
  [EAddBody.CONTACTS_EMAIL]: "",
  [EAddBody.BUSINESS_LICENSE_URL]: "",
  [EAddBody.TYPE]: "",
  [EAddBody.CAPITAL]: "",
  [EAddBody.LEGAL_PERSON]: "",
  [EAddBody.SETDATE]: "",
  [EAddBody.PERIOD]: "",
});
const handleCodeInput = (value) => {
  form[EAddBody.UNIFIED_SOCIAL_CREDIT_CODE] = value.replace(
    /[^a-zA-Z0-9]/g,
    "",
  );
};
// 邮箱正则（兼容新顶级域名和国际化域名）
const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/;
const rules = reactive({
  [EAddBody.CUSTOMER_MAIN_NAME]: [
    { required: true, message: "请输入开户主体", trigger: "blur" },
  ],
  [EAddBody.UNIFIED_SOCIAL_CREDIT_CODE]: [
    { required: true, message: "请输入统一社会信用代码", trigger: "blur" },
  ],
  [EAddBody.BUSINESS_LICENSE_URL]: [
    { required: true, message: "请上传营业执照", trigger: "change" },
  ],
  [EAddBody.ADDRESS]: [
    { required: true, message: "请输入公司注册地址", trigger: "blur" },
  ],
  [EAddBody.POSTAL_CODE]: [
    { required: true, message: "请输入公司注册地址编码", trigger: "blur" },
  ],
  [EAddBody.CONTACTS_NAME]: [
    { required: true, message: "请输入联系人姓名", trigger: "blur" },
  ],
  [EAddBody.LEGAL_PERSON]: [
    { required: true, message: "请输入法人姓名", trigger: "blur" },
  ],
  [EAddBody.SETDATE]: [
    { required: true, message: "请选择注册日期", trigger: "blur" },
  ],
  [EAddBody.CONTACTS_PHONE]: [
    { required: true, message: "请输入联系人手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/, // 最新号段正则（支持13-19开头）
      message: "请输入正确的手机号",
      trigger: ["blur", "change"],
    },
  ],
  [EAddBody.CONTACTS_EMAIL]: [
    { required: true, message: "请输入联系人邮箱", trigger: "blur" },
    {
      pattern: emailReg,
      message: "请输入有效的邮箱地址",
      trigger: ["blur", "change"],
    },
  ],
});
let uploadData = ref<any>({});
const cancelImg = () => {
  uploadData.value = {};
};
const handleSuccess = (res: any, uploadFile: any, uploadFiles: any) => {
  if (res.code == 200) {
    uploadData.value = res.data;
    form[EAddBody.BUSINESS_LICENSE_URL] = uploadData.value.fileUrl;
    uploadLoading.value = false;
    getBusCode(uploadData.value.fileUrl);
  }
};
let uploadLoading = ref(false);
const beforeUploadHandler = (raw: any) => {
  uploadLoading.value = true;
  let fileSize = raw.size / 1024 / 1024;
  if (fileSize > 5) {
    ElMessage.error("图片大小不能超过5MB");
    uploadLoading.value = false;
    return false;
  }
};
const getBusCode = (value) => {
  getBusCodeApi({ fileBaseUrl: value }).then((res: any) => {
    if (res.code == 200) {
      form[EAddBody.UNIFIED_SOCIAL_CREDIT_CODE] =
        res.data.unifiedSocialCreditCode;
      form[EAddBody.PERIOD] = getTime(res?.data?.period);
      form[EAddBody.SETDATE] = res.data.setDate;
      form[EAddBody.TYPE] = res.data.type;
      form[EAddBody.LEGAL_PERSON] = res.data.legalPerson;
      form[EAddBody.CAPITAL] = res.data.capital;
      form[EAddBody.ADDRESS] = res.data.address;
      form[EAddBody.CUSTOMER_MAIN_NAME] = res.data.customerMainName;
      formRef.value?.clearValidate();
    }
  });
};
const getTime = (str: string) => {
  if (!str || str == "null" || str == "undefined" || str == "") {
    return [];
  }
  let parts = [] as any;
  if (str.includes("至")) {
    parts = str.split("至");
  } else if (str.includes(",")) {
    parts = str.split(",");
  } else if (str.includes("，")) {
    parts = str.split("，");
  }
  // const result = parts
  //   .map((part) => {
  //     const match = part.match(/(\d{4})年(\d{2})月(\d{2})日/);
  //     if (match) {
  //       return `${match[1]}-${match[2]}-${match[3]}`;
  //     }
  //     return null;
  //   })
  //   .filter(Boolean);
  // console.log(result);
  // if (result.length == 1) {
  //   return [result[0], "2125-01-01"];
  // }
  return parts;
};
// 获取开户主体详情
const getMainDetail = () => {
  getMainDetailApi({ customerInfoId: props.rowData.customerInfoId }).then(
    (res: any) => {
      if (res.code == 200) {
        for (let key in form) {
          form[key] = res.data[key];
        }
        form[EAddBody.PERIOD] = getTime(res.data.period);
        uploadData.value.fileUrl = res.data.businessLicenseUrl;
      }
    },
  );
};
// 提交表单
const submit = (ref: any) => {
  let data: any = {
    ...form,
    businessLicenseUrl: uploadData.value.fileBaseUrl,
    period: form[EAddBody.PERIOD].join(","),
  };
  if (props.type == "edit") {
    data.customerInfoId = props.rowData.customerInfoId;
  }
  formRef?.value?.validate((valid) => {
    if (valid) {
      btnLoading.value = true;
      addAndUpdateAccountMainApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            ElMessage.success("提交成功");
            close();
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const closeDialog = () => {
  emits("update:visible", false);
};
const close = () => {
  emits("update:visible", false);
  emits("close");
};
onMounted(() => {
  if (props.type == "edit") {
    getMainDetail();
  }
});
</script>

<style scoped lang="scss">
.upload-img {
  height: 200px;
  width: 450px;
  position: relative;
  span {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    color: #333;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
