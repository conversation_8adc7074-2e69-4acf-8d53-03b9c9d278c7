// 谷歌
export enum GoogleEnums {
  // 开户主体
  MAIN_BODYOF_ACCOUNT_IS_OPENED = "customerInfoId",
  //   币种
  CURRENCY = "currency",
  //   推广链接
  PROMOTIONAL_LINK = "pushUrl",
  //   账户时区
  ACCOUNT_TIMEZONE = "timeZone",
  //   邀请用户
  INVITE_USERS = "invitationEmail",
  // 广告账户名称
  ADVERTISING_ACCOUNT_NAME = "accountName",
  // 开户数量
  OPEN_ACCOUNT_NUM = "applyNo",
}
//tiktok

export enum TiktokEnums {
  //   产品名称
  PRODUCT_ADVANTAGE = "productAdvantage",
  //   币种
  CURRENCY = "currency",
  //   账户时区
  ACCOUNT_TIME_ZONE = "timeZone",
  //   开户主体
  MAIN_BODYOF_ACCOUNT_IS_OPENED = "customerInfoId",
  // 开户主体名称
  MAIN_BODYOF_ACCOUNT_IS_OPENED_NAME = "customerMainName",
  //   行业类型
  INDUSTRY_TYPE = "industryType",
  //   注册地
  REGISTRATION_LOCATION = "registeredAddress",
  // 投放国家
  PROMOTION_COUNTRY = "pushCountry",
  //   账户类型
  ACCOUNT_TYPE = "accountType",
  //   推广链接
  PROMOTIONAL_LINK = "pushUrl",
  // 统一社会信用代码
  UNIFIED_SOCIAL_CREDIT_CODE = "unifiedSocialCreditCode",
  // 开户数量
  ACCOUNT_NUMBER = "applyNo",
  // 广告账户名称
  ADVERTISING_ACCOUNT_NAME = "accountName",
  // 法人姓名
  LEGAL_PERSON_NAME = "legalName",
  // 法人身份证号
  LEGAL_PERSON_ID_CARD = "legalCard",
  // 法人银行卡号
  LEGAL_PERSON_BANK_CARD = "legalBank",
  // 法人手机号
  LEGAL_PERSON_PHONE = "legalPhone",
  // 公司简介
  COMPANY_PROFILE = "companyProfile",
  // 开户数量
  OPEN_ACCOUNT_NUM = "applyNo",
}

// meta
export enum MetaEnums {
  //   开户主体
  MAIN_BODYOF_ACCOUNT_IS_OPENED = "customerInfoId",
  //   行业类型
  INDUSTRY_TYPE = "industryType",
  //   推广链接
  PROMOTIONAL_LINK = "pushUrl",
  //   账户时区
  ACCOUNT_TIME_ZONE = "timeZone",
  //   FB主页
  FB_HOME_PAGE = "fbHomeUrl",
  //   性别
  GENDER = "sex",
  //   生日
  BIRTHDAY = "birthday",
  //   密码
  PASSWORD = "secretKey",
  //   官方网址
  OFFICIAL_WEBSITE = "officialWebsiteUrl",
  // 账户名称
  ACCOUNT_NAME = "accountName",
  // 开户数量
  OPEN_ACCOUNT_NUM = "applyNo",
  // 开户数量
  OEID = "thirdApplyId",
  //   币种
  CURRENCY = "currency",
}

// linkedin开户申请
export enum LinkedinEnums {
  //   开户主体
  MAIN_BODYOF_ACCOUNT_IS_OPENED = "customerInfoId",
  // 公司名称（英文）
  COMPANY_NAME_E = "companyNameEn",
  // 公司地址（英文）
  COMPANY_ADDRESS_E = "companyAddressEn",
  // HQphone总部电话
  HQPHONE = "hqPhone",
  // Official Website 官网链接
  OFFICIAL_WEBSITE = "officialWebsiteUrl",
  // 充值金额
  RECHARGE_AMOUNT = "rechargeAmount",
  // 首次上线时间
  FIRST_ONLINE_TIME = "onlineTimeStr",
  // 推广时间
  PROMOTION_TIME = "pushTime",
  // Linkedin company Page
  LINKEDIN_COMPANY_PAGE = "companyPageUrl",
  // 个人领英界面链接
  PERSONAL_LINKEDIN_PAGE = "oneselfUrl",
  //   行业类型
  INDUSTRY_TYPE = "industryType",
  // 推广链接
  PROMOTIONAL_LINK = "pushUrl",
  // 联系人邮箱
  CONTACT_EMAIL = "contactsEmail",
  // 联系人手机号
  CONTACT_PHONE = "contactsPhone",
  // 联系人姓名
  CONTACT_NAME = "contactsName",
  // 账户时区
  ACCOUNT_TIMEZONE = "timeZone",
  // 开户数量
  OPEN_ACCOUNT_NUM = "applyNo",
}

// bing
export enum BingEnums {
  //   开户主体
  MAIN_BODYOF_ACCOUNT_IS_OPENED = "customerInfoId",
  //   行业类型
  INDUSTRY_TYPE = "industryType",
  // 账户时区
  ACCOUNT_TIME_ZONE = "timeZone",
  // 投放国家
  PROMOTION_COUNTRY = "pushCountry",
  //   推广链接
  PROMOTIONAL_LINK = "pushUrl",
  // 注册地
  REGISTRATION_PLACE = "registeredAddress",
  // 营业执照邮编
  BUSINESS_LICENSE_POSTAL_CODE = "postCode",
  // 电话
  PHONE = "hqPhone",
  // 子行业
  SUB_INDUSTRY = "subIndustry",
  // 开户数量
  OPEN_ACCOUNT_NUM = "applyNo",
}

// Yandex
export enum YandexEnums {
  //   开户主体
  MAIN_BODYOF_ACCOUNT_IS_OPENED = "customerInfoId",
  // 营业时间
  BUSINESS_HOURS = "businessHours",
  // 营业开始时间
  BUSINESS_START_TIME = "businessStartTime",
  // 营业结束时间
  BUSINESS_END_TIME = "businessEndTime",
  // 每日预算
  DAILY_BUDGET = "budgetDay",
  //   推广链接
  PROMOTIONAL_LINK = "pushUrl",
  // 联系人邮箱
  CONTACT_EMAIL = "contactsEmail",
  // 联系人手机号
  CONTACT_PHONE = "contactsPhone",
  // 联系人姓名
  CONTACT_NAME = "contactsName",
  // 公司简介
  COMPANY_PROFILE = "companyProfile",
  // 产品优势
  PRODUCT_ADVANTAGE = "productAdvantage",
  //行业类型
  INDUSTRY_TYPE = "industryType",
  //账户时区
  ACCOUNT_TIME_ZONE = "timeZone",
  // 开户数量
  OPEN_ACCOUNT_NUM = "applyNo",
  //   组织类型
  ORGTYPE = "orgType",
  //   币种
  CURRENCY = "currency",
  //   邀请用户
  INVITE_USERS = "invitationEmail",
  // 统一社会信用代码
  UNIFIED_SOCIAL_CREDIT_CODE = "unifiedSocialCreditCode",
  // 广告账户名称
  ADVERTISING_ACCOUNT_NAME = "accountName",
}

// 批量开户
export enum BatchOpenAccountEnum {
  //   开户主体
  MAIN_BODYOF_ACCOUNT_IS_OPENED = "customerInfoId",
  //   行业类型
  INDUSTRY_TYPE = "industryType",
  //   账户时区
  ACCOUNT_TIME_ZONE = "timeZone",
  //   推广链接
  PROMOTIONAL_LINK = "pushUrl",
  // 联系人邮箱
  CONTACT_EMAIL = "contactsEmail",
  // 联系人手机号
  CONTACT_PHONE = "contactsPhone",
  // 联系人姓名
  CONTACT_NAME = "contactsName",
}
