import {
  getAccountOptionList<PERSON>pi,
  getIndustryTypeListApi,
  getTimeZoneListApi,
} from "@/api/openAccount/index";
import { ref } from "vue";
export const useAccountList = () => {
  let mainList = ref<any[]>([]);
  let timeZoneList = ref<any[]>([]);
  getAccountOptionListApi({ merchantId: "" }).then((res: any) => {
    mainList.value = res?.data?.map((item) => {
      return {
        label: item.customerMainName,
        value: item.customerInfoId,
        ...item,
      };
    });
  });
  getTimeZoneListApi().then((res: any) => {
    timeZoneList.value = res?.data?.map((item) => {
      return {
        label: item,
        value: item,
      };
    });
  });
  const getMainList = () => {
    getAccountOptionListApi({ merchantId: "" }).then((res:any) => {
      mainList.value = res?.data?.map((item) => {
        return {
          label: item.customerMainName,
          value: item.customerInfoId,
          ...item,
        };
      });
    });
  };
  return { mainList, timeZoneList, getMainList };
};
export const useIndustryList = (type?) => {
  let industryTypeList = ref<any[]>([]);
  getIndustryTypeListApi({ mediumType: type ? type : "" }).then((res:any) => {
    industryTypeList.value = res?.data?.map((item) => {
      return {
        label: item.industryName,
        value: item.industryType,
      };
    });
  });
  return { industryTypeList };
};
