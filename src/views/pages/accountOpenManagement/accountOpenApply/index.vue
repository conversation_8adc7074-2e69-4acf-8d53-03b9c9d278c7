<template>
  <div>
    <div class="apply-container">
      <div class="title">
        <Breadcrumb />
      </div>
      <div class="open-type" id="accountOpenApply">
        <div class="type-item" v-for="(item, index) in openItems" :key="index">
          <div class="type-content">
            <div class="item-row1">
              <img :src="item.img" alt="icon" class="iconImg" />
              <div class="type-label">{{ item.title }}</div>
            </div>
            <div class="item-row2">
              <el-tooltip :content="item.content" placement="bottom-end">
                {{ item.content }}
              </el-tooltip>
            </div>
          </div>
          <div class="type-btn">
            <el-button
              type="primary"
              @click="applyAcount(item.id)"
              v-if="accountOpeningApplyControls"
              >申请开户</el-button
            >
          </div>
        </div>
      </div>
      <div class="apply-row2">
        <div class="overview">
          <div class="header">
            <div class="head-title">开户须知</div>
          </div>
          <div class="overview-content">
            <div
              class="content-item"
              v-for="item in openingNotice"
              :key="item.id"
              @click="openNewUrl(item.url)"
            >
              <div class="item-img"><img :src="item.img" alt="" /></div>
              <div class="main">
                <div class="main-title">{{ item.title }}</div>
                <div class="main1-content">
                  {{ item.content }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <googleDialog
      v-if="dialogStatus.googleShow"
      :openType="openTypeNum"
      v-model:visible="dialogStatus.googleShow"
      @close="close"
    />
    <!-- 线上 -->
    <tiktokDialogOnLine
      v-model:visible="dialogStatus.tiktokShow"
      :openType="openTypeNum"
      v-if="dialogStatus.tiktokShow"
      @close="close"
    />
    <!-- 线下 -->
    <tiktokDialogOffline
      v-model:visible="dialogStatus.tiktokOfflineShow"
      :openType="openTypeNum"
      v-if="dialogStatus.tiktokOfflineShow"
      @close="close"
    />
    <!-- meta线下 -->
    <metaDialogOffline
      v-model:visible="dialogStatus.metaOfflineShow"
      :openType="openTypeNum"
      v-if="dialogStatus.metaOfflineShow"
      @close="close"
    />
    <!-- meta线上 -->
    <metaDialogOnLine
      v-model:visible="dialogStatus.metaShow"
      :openType="openTypeNum"
      v-if="dialogStatus.metaShow"
      @close="close"
    />
    <bingDialog
      v-model:visible="dialogStatus.bingShow"
      :openType="openTypeNum"
      v-if="dialogStatus.bingShow"
      @close="close"
    />
    <linkedinDialog
      v-model:visible="dialogStatus.linkedinShow"
      :openType="openTypeNum"
      v-if="dialogStatus.linkedinShow"
      @close="close"
    />
    <!-- yandex线上 -->
    <yangdexDialogOnLine
      v-model:visible="dialogStatus.yangdexShow"
      :openType="openTypeNum"
      v-if="dialogStatus.yangdexShow"
      @close="close"
    />
    <!-- yandex线下 -->
    <yangdexDialogOffline
      v-model:visible="dialogStatus.yangdexOfflineShow"
      v-if="dialogStatus.yangdexOfflineShow"
      :openType="openTypeNum"
      @close="close"
    />

    <openTips ref="openTipsRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import googleDialog from "./component/googleDialog.vue";
import tiktokDialogOffline from "./component/tiktokOffline.vue";
import tiktokDialogOnLine from "./component/tiktokDialog.vue";
import metaDialogOnLine from "./component/metaDialog.vue";
import metaDialogOffline from "./component/metaOffline.vue";
import bingDialog from "./component/bingDialog.vue";
import linkedinDialog from "./component/linkedinDialog.vue";
import yangdexDialogOnLine from "./component/yangdexDialog.vue";
import yangdexDialogOffline from "./component/yandexOffline.vue";
import applyIcon1 from "@/assets/images/applyIcon1.png";
import applyIcon2 from "@/assets/images/applyIcon2.png";
import applyIcon3 from "@/assets/images/applyIcon3.png";
import iconGoogle from "@/assets/images/home/<USER>";
import iconYandex from "@/assets/images/home/<USER>";
import iconMeta from "@/assets/images/home/<USER>";
import iconTikTok from "@/assets/images/home/<USER>";
import iconBing from "@/assets/images/home/<USER>";
import iconLink from "@/assets/images/home/<USER>";
import { openType } from "@/api/openAccount";
import openTips from "@/components/openSuccess/index.vue";
import usePermissionStore from "@/store/modules/permission";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const permissionStore = usePermissionStore();
const accountOpeningApplyControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountOpenApply?.indexOf(
      "accountOpeningApplyControls"
    ) > -1
);
console.log(permissionStore?.buttonAuth);
const route = useRoute();
const openTipsRef = ref();
const openTypeNum = ref(1);
const openItems = [
  {
    id: 1,
    title: "Google Ads",
    content:
      "全球最大的搜索引擎公司，拥有近30亿月活用户，在客户搜索您的产品或服务的那一刻，恰当其时展示广告。",
    img: iconGoogle,
  },
  {
    id: 2,
    title: "Yandex",
    content:
      "俄罗斯第一大搜索引擎,是俄罗斯拥有用户最多的网站,2019年占据俄罗斯42.4%的市场份额。",
    img: iconYandex,
  },

  {
    id: 3,
    title: "Meta",
    content:
      "全球最大的社交平台，亦是全球最佳精准的营销平台之一。依托24亿用户的庞大全球化社群发展你的业务",
    img: iconMeta,
  },
  {
    id: 4,
    title: "Tiktok",
    content:
      "抖音国际版，风靡全球短视频社交平台，海量新潮年轻用户，通过强导流强曝光的短视频内容带来更高转化率。",
    img: iconTikTok,
  },
  {
    id: 5,
    title: "Bing Ads",
    content:
      "全球第二大搜索引擎 ，覆盖全球36个国家和地区，拥有超过6亿高购买力用户，购买力指数比平均联网用户高101%",
    img: iconBing,
  },
  {
    id: 6,
    title: "Linkedln",
    content:
      "全球领先的职业社交网站，在全球200多个国家和地区拥有超过7.56亿会员，拥有高占比企业高层及关键决策人，被评为最可信的社交媒体平台",
    img: iconLink,
  },
];
const openingNotice = [
  {
    id: 1,
    title: "facebook申诉流程",
    content:
      "所有资产包括主页、个人账户、广告、广告账户、BM等互相影响牵连，某个资产有违规都会触发其他资产受限制。广告及主页内容违规是大部分资产受限的源头，而且只要有违规存在就有资产被限制的风险，因此需要严肃管理广告及主页内容。",
    img: applyIcon1,
    url: "https://m32ns8rl95.feishu.cn/docx/JwzldOWYbou7ZRxnL0ucBiMlnsa?from=from_copylink",
  },
  {
    id: 1,
    title: " 2024最新Google广告客户验证流程",
    content:
      "近期，Google对验证流程进行了更新，将身份验证和业务运营验证整合到一个流程里，合并为广告客户验证。需要完成身份验证的广告客户会收到一封电子邮件，并会在其Google广告帐号内看到一则通知。",
    img: applyIcon2,
    url: "https://m32ns8rl95.feishu.cn/docx/Jr5xdNqOKoe45HxpLmFcQjw4nbf",
  },
  {
    id: 1,
    title: "TikTok开户驳回解析指南",
    content:
      "驳回原因：请提供完整营业执照；Incomplete document；Black and white copy营业执照需提供原件的彩色照片或彩色扫描件；营业执照内容必须完整，若拍摄不完整可拒绝（建议执照四个方角露出拍摄）。",
    img: applyIcon3,
    url: "https://m32ns8rl95.feishu.cn/docx/YgoidjkIpotgF5xpapdcxSFJnfe?from=from_copylink",
  },
];
let dialogStatus = reactive({
  googleShow: false,
  tiktokShow: false,
  tiktokOfflineShow: false,
  metaShow: false,
  metaOfflineShow: false,
  bingShow: false,
  linkedinShow: false,
  yangdexShow: false,
  yangdexOfflineShow: false,
});
const router = useRouter();
const batchOpen = () => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: "/ads/openAccount/batchOpenApply",
      name: "ads",
      parentPath: "/ads/openAccount/account-open-apply",
      isChild: true,
    });
  }
};
const openNewUrl = (url) => {
  window.open(url);
};
const close = (type) => {
  openTipsRef?.value?.open(type);
};
// 申请开户
const applyAcount = async (value: any) => {
  const res: any = await openType({ mediumType: value });
  openTypeNum.value = res?.data || 2; // 1-自动，2手动
  switch (Number(value)) {
    case 1:
      // google
      dialogStatus.googleShow = true;
      break;
    case 2:
      // yandex
      if (openTypeNum.value == 1) {
        dialogStatus.yangdexShow = true;
      } else {
        dialogStatus.yangdexOfflineShow = true;
      }
      break;
    case 3:
      // meta
      if (openTypeNum.value == 1) {
        dialogStatus.metaShow = true;
      } else {
        dialogStatus.metaOfflineShow = true;
      }

      break;
    case 4:
      // tiktok
      if (openTypeNum.value == 1) {
        dialogStatus.tiktokShow = true;
      } else {
        dialogStatus.tiktokOfflineShow = true;
      }
      break;
    case 5:
      // bing
      dialogStatus.bingShow = true;
      break;
    case 6:
      // linkedin
      dialogStatus.linkedinShow = true;
      break;
  }
};
onMounted(() => {
  if (route.query?.mediaType) {
    console.log(route.query);
    applyAcount(route.query?.mediaType);
  }
});
</script>

<style scoped lang="scss">
.apply-container {
  // padding: 20px;
  .title {
    color: #202020;
    font-size: 20px;
    font-weight: 500;
    font-family: "PingFang SC";
    margin-bottom: 20px;
  }
  .open-type {
    border-radius: 8px;
    padding: 20px;
    background-color: #fff;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    .type-item {
      padding: 12px 8px;
      padding-right: 14px;
      display: flex;
      background: #ffffff;
      box-shadow: 0 2px 7px 0 #d6dae080;
      .type-content {
        .item-row1 {
          display: flex;
          align-items: center;
          gap: 10px;
          .iconImg {
            width: 18px;
            height: 18px;
          }
          .type-label {
            color: #333333;
            font-size: 14px;
            font-weight: 600;
          }
        }
        .item-row2 {
          width: 100%;
          color: #333333;
          font-size: 12px;
          font-weight: 400;
          margin-top: 16px;
          line-height: 20px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .type-btn {
        margin-left: 20px;
        display: flex;
        align-items: center;
      }
    }
  }
  .apply-row2 {
    margin-top: 20px;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      .head-title {
        color: #333333;
        font-size: 20px;
        font-weight: 600;
      }
      .head-more {
        color: #1f7bf2;
        font-size: 14px;
        font-weight: 400;
        cursor: pointer;
      }
    }
    .overview {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px 20px;
      .overview-content {
        display: flex;
        flex-direction: column;
        gap: 26px;
        margin-top: 14px;
        .content-item {
          display: flex;
          cursor: pointer;

          .item-img {
            width: 120px;
            height: 80px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .main {
            flex: 1;
            margin-left: 8px;
            .main-title {
              margin-bottom: 8px;
              color: #333333;
              font-size: 14px;
              font-weight: 600;
            }
            .main1-content {
              color: #666666;
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
</style>
