<template>
  <div class="batchApply">
    <div class="batch-form">
      <el-form
        ref="commonRef"
        :model="form"
        label-width="120"
        :rules="commonRules"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="开户主体"
              :prop="[BatchOpenAccountEnum.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
            >
              <el-select
                v-model="
                  form[BatchOpenAccountEnum.MAIN_BODYOF_ACCOUNT_IS_OPENED]
                "
                placeholder="请选择开户主体"
                @change="mainChangeHandler"
              >
                <el-option
                  v-for="item in mainList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="推广链接"
              :prop="[BatchOpenAccountEnum.PROMOTIONAL_LINK]"
            >
              <el-input
                v-model="form[BatchOpenAccountEnum.PROMOTIONAL_LINK]"
                placeholder="请输入推广链接"
                maxlength="100"
                type="text"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="账户时区"
              :prop="[BatchOpenAccountEnum.ACCOUNT_TIME_ZONE]"
            >
              <el-select
                v-model="form[BatchOpenAccountEnum.ACCOUNT_TIME_ZONE]"
                placeholder="请选择账户时区"
              >
                <el-option
                  v-for="item in timeZoneList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="select-box">
      <ul>
        <li
          v-for="(item, index) in openItems"
          :key="index"
          :class="{ liActive: selectData.includes(item.id) }"
          @click="selectMedia(item.id)"
        >
          <img :src="item.img" alt="icon" class="iconImg" />
          <span>{{ item.title }}</span>
          <svg-icon
            v-if="selectData.includes(item.id)"
            icon-class="bingo"
            class="bingo"
          />
        </li>
      </ul>
    </div>
    <div class="box-list">
      <div class="box google-box" v-if="selectData.includes(1)">
        <div class="box-title">
          <img :src="iconGoogle" alt="icon" class="iconImg mr10" />
          <span>Google</span>
        </div>
        <div>
          <el-form
            ref="googleRef"
            :model="googleForm"
            label-position="right"
            label-width="200"
            :rules="googleRules"
          >
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="币种" :prop="[GoogleEnums.CURRENCY]">
                  <el-select
                    v-model="googleForm[GoogleEnums.CURRENCY]"
                    placeholder="请选择币种"
                    filterable
                  >
                    <el-option
                      v-for="item in [{ label: 'CNY', value: 'CNY' }]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="开户数量"
                  :prop="[GoogleEnums.OPEN_ACCOUNT_NUM]"
                >
                  <el-input
                    v-model="googleForm[GoogleEnums.OPEN_ACCOUNT_NUM]"
                    placeholder="请填写开户数量"
                    maxlength="5"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div
              v-for="(item, index) in googleForm.addAccountCount"
              :key="index"
              class="add-account-container"
            >
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item
                    :label="
                      googleForm.addAccountCount?.length > 1
                        ? '账户名称' + (index + 1)
                        : '账户名称'
                    "
                    :prop="`addAccountCount.${index}.${GoogleEnums.ADVERTISING_ACCOUNT_NAME}`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入广告账户名称',
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="item[GoogleEnums.ADVERTISING_ACCOUNT_NAME]"
                      placeholder="请填写广告账户名称"
                      maxlength="100"
                      type="text"
                    />
                  </el-form-item>
                </el-col>
                <el-icon class="el-icon--delete" v-if="index > 0">
                  <Delete @click="deleteRow(index)" />
                </el-icon>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item
                    label="推广链接"
                    :prop="`addAccountCount.${index}.${GoogleEnums.PROMOTIONAL_LINK}`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入推广链接',
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="item[GoogleEnums.PROMOTIONAL_LINK]"
                      placeholder="请输入推广链接"
                      maxlength="100"
                      type="text"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item
                    label="邀请用户"
                    :prop="`addAccountCount.${index}.${GoogleEnums.INVITE_USERS}`"
                  >
                    <el-select
                      v-model="item[GoogleEnums.INVITE_USERS]"
                      validate-event
                      multiple
                      filterable
                      :allow-create="true"
                      default-first-option
                      :multiple-limit="5"
                      :reserve-keyword="false"
                      remote
                      placeholder="请输入联系人姓名邀请可填多个邮箱"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div v-if="googleForm.addAccountCount?.length < 5" class="f jcc">
              <el-button type="primary" class="mr10a" @click="addAccountName"
                >新增广告账户</el-button
              >
            </div>
          </el-form>
        </div>
      </div>
      <div class="box yandex-box" v-if="selectData.includes(2)">
        <div class="box-title">
          <img :src="iconYandex" alt="icon" class="iconImg mr10" />
          <span>Yandex</span>
        </div>
        <div>
          <el-form
            ref="yandexRef"
            :model="yandexForm"
            label-position="right"
            label-width="200"
            :rules="yandexRules"
          >
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item label="币种：">
                  <el-text>USD</el-text>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="组织类型：" :prop="[YandexEnums.ORGTYPE]">
                  <el-select
                    v-model="yandexForm[YandexEnums.ORGTYPE]"
                    placeholder="请选择组织类型"
                    filterable
                  >
                    <el-option
                      v-for="item in [
                        { label: 'Foreign legal(外国法人)', value: 1 },
                      ]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="统一社会信用代码"
                  :prop="[YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
                >
                  <el-input
                    v-model="yandexForm[YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
                    placeholder="输入统一社会信用代码"
                    maxlength="8"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="开户数量："
                  :prop="[YandexEnums.OPEN_ACCOUNT_NUM]"
                >
                  <el-input
                    v-model="yandexForm[YandexEnums.OPEN_ACCOUNT_NUM]"
                    placeholder="输入开户数量"
                    maxlength="8"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div
              v-for="(item, index) in yandexForm.yandexAccountList"
              :key="index"
              class="add-account-container"
            >
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item
                    :prop="`yandexAccountList.${index}.${YandexEnums.ADVERTISING_ACCOUNT_NAME}`"
                    :label="
                      yandexForm.yandexAccountList?.length > 1
                        ? '广告账户名称' + (index + 1)
                        : '广告账户名称'
                    "
                    :rules="[
                      {
                        required: true,
                        message: '请输入广告账户名称',
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="item[YandexEnums.ADVERTISING_ACCOUNT_NAME]"
                      placeholder="用于账号登录，仅支持字母数字，必须以字母开头"
                      maxlength="8"
                      type="text"
                    />
                  </el-form-item>
                </el-col>
                <el-icon class="el-icon--delete" v-if="index > 0">
                  <Delete @click="deleteYandexRow(index)" />
                </el-icon>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item
                    label="邮箱"
                    :prop="`yandexAccountList.${index}.${YandexEnums.INVITE_USERS}`"
                  >
                    <el-select
                      v-model="item[YandexEnums.INVITE_USERS]"
                      validate-event
                      multiple
                      filterable
                      :allow-create="true"
                      default-first-option
                      :multiple-limit="5"
                      :reserve-keyword="false"
                      remote
                      placeholder="请输入联系人姓名邀请可填多个邮箱"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div v-if="yandexForm.yandexAccountList?.length < 5" class="f jcc">
              <el-button
                type="primary"
                class="mr10a"
                @click="addYandexAccountName"
                >新增广告账户</el-button
              >
            </div>
          </el-form>
        </div>
      </div>
      <div
        class="box meta-box"
        v-if="selectData.includes(3) && openTypeMeta == 1"
      >
        <div class="box-title">
          <img :src="iconMeta" alt="icon" class="iconImg mr10" />
          <span>Meta</span>
        </div>
        <div>
          <el-form
            ref="metaRef"
            :model="metaForm"
            label-width="200"
            :rules="metaRules"
          >
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item label="币种：">
                  <el-text>USD</el-text>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="开户数量"
                  :prop="[MetaEnums.OPEN_ACCOUNT_NUM]"
                >
                  <el-input
                    v-model="metaForm[MetaEnums.OPEN_ACCOUNT_NUM]"
                    placeholder="请填写开户数量"
                    maxlength="5"
                    type="number"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item label="开户链接：">
                  <div class="flcl aifs">
                    <el-link
                      target="_blank"
                      type="primary"
                      :underline="false"
                      style="font-size: 14px"
                      @click.stop="openLink"
                      >https://www.facebook.com</el-link
                    ><br />
                    <el-text type="info" style="color: #333"
                      >1、点击上方开户链接，提交资料</el-text
                    ><br />
                    <el-text type="info" style="color: #333"
                      >2、提交成功后需复制OE申请编号，填写至下方</el-text
                    >
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item label="OE申请编号" :prop="[MetaEnums.OEID]">
                  <el-input
                    v-model="form[MetaEnums.OEID]"
                    placeholder="请输入OE申请编号"
                    maxlength="100"
                    type="text"
                  />
                  <el-link type="info" style="color: #333"
                    >请正确填写的申请编号，以免影响您的开户进度</el-link
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div
        class="box meta-box"
        v-if="selectData.includes(3) && openTypeMeta == 2"
      >
        <div class="box-title">
          <img :src="iconMeta" alt="icon" class="iconImg mr10" />
          <span>Meta</span>
        </div>
        <div>
          <el-form
            ref="metaRef"
            :model="metaForm"
            label-width="200"
            :rules="metaRules"
          >
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="账户名称" :prop="[MetaEnums.ACCOUNT_NAME]">
                  <el-input
                    v-model="metaForm[MetaEnums.ACCOUNT_NAME]"
                    placeholder="请输入账户名称"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="FB主页" :prop="[MetaEnums.FB_HOME_PAGE]">
                  <el-input
                    v-model="metaForm[MetaEnums.FB_HOME_PAGE]"
                    placeholder="请输入FB主页"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="性别" :prop="[MetaEnums.GENDER]">
                  <el-radio-group v-model="metaForm[MetaEnums.GENDER]">
                    <el-radio value="1">男</el-radio>
                    <el-radio value="2">女</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="生日" :prop="[MetaEnums.BIRTHDAY]">
                  <el-date-picker
                    v-model="metaForm[MetaEnums.BIRTHDAY]"
                    type="date"
                    placeholder="生日"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="密码" :prop="[MetaEnums.PASSWORD]">
                  <el-input
                    v-model="metaForm[MetaEnums.PASSWORD]"
                    placeholder="请输入密码，必须包含至少6个字母或数字"
                    type="text"
                    @input="checkPassword"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="官方网址"
                  :prop="[MetaEnums.OFFICIAL_WEBSITE]"
                >
                  <el-input
                    v-model="metaForm[MetaEnums.OFFICIAL_WEBSITE]"
                    placeholder="请输入公司的官方网址"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div
        class="box tiktok-box"
        v-if="selectData.includes(4) && openTypeTikTok == 1"
      >
        <div class="box-title">
          <img :src="iconTikTok" alt="icon" class="iconImg mr10" />
          <span>Tiktok</span>
        </div>
        <div>
          <el-form
            ref="tiktokRef"
            :model="tiktokForm"
            label-width="200"
            :rules="tiktokRules"
          >
            <el-row :gutter="10">
              <el-col :span="20"
                ><el-form-item
                  label="行业类型"
                  :prop="[TiktokEnums.INDUSTRY_TYPE]"
                >
                  <el-select
                    v-model="tiktokForm[TiktokEnums.INDUSTRY_TYPE]"
                    placeholder="请选择行业类型"
                    filterable
                  >
                    <el-option
                      v-for="item in industryTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="统一社会信用代码"
                  :prop="[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
                >
                  <el-input
                    v-model="form[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
                    placeholder="请输入统一社会信用代码"
                    maxlength="18"
                    type="text"
                    @input="handlerCodeInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="注册地"
                  :prop="[TiktokEnums.REGISTRATION_LOCATION]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.REGISTRATION_LOCATION]"
                    placeholder="请输入注册地"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="账户类型"
                  :prop="[TiktokEnums.ACCOUNT_TYPE]"
                >
                  <el-select
                    v-model="tiktokForm[TiktokEnums.ACCOUNT_TYPE]"
                    placeholder="请选择账户类型"
                    filterable
                  >
                    <el-option
                      v-for="item in [
                        { value: 1, label: '竞价' },
                        { value: 2, label: '品牌' },
                      ]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="开户数量"
                  :prop="[TiktokEnums.ACCOUNT_NUMBER]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.ACCOUNT_NUMBER]"
                    placeholder="请输入开户数量，一次最多支持开通5个"
                    type="number"
                    @input="handlerNumberInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div
              v-for="(item, index) in tiktokForm.accountTikTokList"
              :key="index"
              class="add-account-container"
            >
              <el-row :gutter="10">
                <el-col :span="20">
                  <el-form-item
                    :label="
                      tiktokForm.accountTikTokList?.length > 1
                        ? '广告账户名称' + (index + 1)
                        : '广告账户名称'
                    "
                  >
                    <el-text>{{
                      item[TiktokEnums.PRODUCT_ADVANTAGE] +
                      "-" +
                      form[TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]
                    }}</el-text>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="20">
                  <el-form-item
                    label="产品名称"
                    :prop="`accountTikTokList.${index}.${TiktokEnums.ADVERTISING_ACCOUNT_NAME}`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入广告账户名称',
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="item[TiktokEnums.ADVERTISING_ACCOUNT_NAME]"
                      placeholder="请填写广告账户名称"
                      maxlength="100"
                      type="text"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="20">
                  <el-form-item
                    label="企业简称"
                    :prop="`accountTikTokList.${index}.${TiktokEnums.COMPANY_PROFILE}`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入企业简称，最多20字符',
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="item[TiktokEnums.COMPANY_PROFILE]"
                      placeholder="请输入企业简称，最多20字符"
                      maxlength="20"
                      type="text"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="20">
                  <el-form-item
                    label="推广链接"
                    :prop="`accountTikTokList.${index}.${TiktokEnums.PROMOTIONAL_LINK}`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入推广链接',
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="item[TiktokEnums.PROMOTIONAL_LINK]"
                      placeholder="请输入推广链接"
                      maxlength="100"
                      type="text"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-icon class="el-icon--delete" v-if="index > 0">
                <Delete @click="deleteTikTokRow(index)" />
              </el-icon>
            </div>
            <div
              v-if="tiktokForm.accountTikTokList?.length < 5"
              class="f jcc mb20"
            >
              <el-button
                type="primary"
                class="mr10a"
                @click="addTikTokAccountName"
                >新增广告账户</el-button
              >
            </div>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="法人姓名"
                  :prop="[TiktokEnums.LEGAL_PERSON_NAME]"
                >
                  <el-input
                    v-model="form[TiktokEnums.LEGAL_PERSON_NAME]"
                    placeholder="请输入法人姓名"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="法人身份证号"
                  :prop="[TiktokEnums.LEGAL_PERSON_ID_CARD]"
                >
                  <el-input
                    v-model="form[TiktokEnums.LEGAL_PERSON_ID_CARD]"
                    placeholder="请输入法人身份证号"
                    maxlength="100"
                    type="text"
                    @input="handlerIdCardInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="法人银行卡号"
                  :prop="[TiktokEnums.LEGAL_PERSON_BANK_CARD]"
                >
                  <el-input
                    v-model="form[TiktokEnums.LEGAL_PERSON_BANK_CARD]"
                    placeholder="请输入法人银行卡号"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="法人手机号"
                  :prop="[TiktokEnums.LEGAL_PERSON_PHONE]"
                >
                  <el-input
                    v-model="form[TiktokEnums.LEGAL_PERSON_PHONE]"
                    placeholder="请输入法人手机号"
                    maxlength="11"
                    type="text"
                    @input="handlePhoneInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div
        class="box tiktok-box"
        v-if="selectData.includes(4) && openTypeTikTok == 2"
      >
        <div class="box-title">
          <img :src="iconTikTok" alt="icon" class="iconImg mr10" />
          <span>Tiktok</span>
        </div>
        <div>
          <el-form
            ref="tiktokRef"
            :model="tiktokForm"
            label-width="200"
            :rules="tiktokRules"
          >
            <el-row :gutter="10">
              <el-col :span="10"
                ><el-form-item
                  label="行业类型"
                  :prop="[TiktokEnums.INDUSTRY_TYPE]"
                >
                  <el-select
                    v-model="tiktokForm[TiktokEnums.INDUSTRY_TYPE]"
                    placeholder="请选择行业类型"
                    filterable
                  >
                    <el-option
                      v-for="item in industryTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="统一社会信用代码"
                  :prop="[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
                    placeholder="请输入统一社会信用代码"
                    maxlength="18"
                    type="text"
                    @input="handlerCodeInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="注册地"
                  :prop="[TiktokEnums.REGISTRATION_LOCATION]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.REGISTRATION_LOCATION]"
                    placeholder="请输入注册地"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="账户类型"
                  :prop="[TiktokEnums.ACCOUNT_TYPE]"
                >
                  <el-select
                    v-model="tiktokForm[TiktokEnums.ACCOUNT_TYPE]"
                    placeholder="请选择账户类型"
                    filterable
                  >
                    <el-option
                      v-for="item in [
                        { value: 1, label: '竞价' },
                        { value: 2, label: '品牌' },
                      ]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="广告账户名称"
                  :prop="[TiktokEnums.ADVERTISING_ACCOUNT_NAME]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.ADVERTISING_ACCOUNT_NAME]"
                    placeholder="请输入广告账户名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="法人姓名"
                  :prop="[TiktokEnums.LEGAL_PERSON_NAME]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.LEGAL_PERSON_NAME]"
                    placeholder="请输入法人姓名"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="法人身份证号"
                  :prop="[TiktokEnums.LEGAL_PERSON_ID_CARD]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.LEGAL_PERSON_ID_CARD]"
                    placeholder="请输入法人身份证号"
                    maxlength="100"
                    type="text"
                    @input="handlerIdCardInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="法人银行卡号"
                  :prop="[TiktokEnums.LEGAL_PERSON_BANK_CARD]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.LEGAL_PERSON_BANK_CARD]"
                    placeholder="请输入法人银行卡号"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="法人手机号"
                  :prop="[TiktokEnums.LEGAL_PERSON_PHONE]"
                >
                  <el-input
                    v-model="tiktokForm[TiktokEnums.LEGAL_PERSON_PHONE]"
                    placeholder="请输入法人手机号"
                    maxlength="100"
                    type="text"
                    @input="handleTikPhoneInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div class="box bing-box" v-if="selectData.includes(5)">
        <div class="box-title">
          <img :src="iconBing" alt="icon" class="iconImg mr10" />
          <span>Bing</span>
        </div>
        <div>
          <el-form
            ref="bingRef"
            :model="bingForm"
            label-width="200"
            :rules="bingRules"
          >
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="注册地"
                  :prop="[BingEnums.REGISTRATION_PLACE]"
                >
                  <el-input
                    v-model="bingForm[BingEnums.REGISTRATION_PLACE]"
                    placeholder="请输入注册地"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="营业执照邮编"
                  :prop="[BingEnums.BUSINESS_LICENSE_POSTAL_CODE]"
                >
                  <el-input
                    v-model="bingForm[BingEnums.BUSINESS_LICENSE_POSTAL_CODE]"
                    placeholder="请输入营业执照邮编"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="电话" :prop="[BingEnums.PHONE]">
                  <el-input
                    v-model="bingForm[BingEnums.PHONE]"
                    placeholder="请输入电话"
                    maxlength="11"
                    type="text"
                    @input="handleBingPhoneInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10"
                ><el-form-item
                  label="一级行业类型"
                  :prop="[BingEnums.INDUSTRY_TYPE]"
                >
                  <el-select
                    v-model="bingForm[BingEnums.INDUSTRY_TYPE]"
                    placeholder="请选择一级行业类型"
                  >
                    <el-option
                      v-for="item in industryList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item label="子行业" :prop="[BingEnums.SUB_INDUSTRY]">
                  <el-input
                    v-model="bingForm[BingEnums.SUB_INDUSTRY]"
                    placeholder="请输入子行业"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="投放国家"
                  :prop="[BingEnums.PROMOTION_COUNTRY]"
                >
                  <el-input
                    v-model="bingForm[BingEnums.PROMOTION_COUNTRY]"
                    placeholder="请输入投放国家"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div class="box linkedIn-box" v-if="selectData.includes(6)">
        <div class="box-title">
          <img :src="iconLink" alt="icon" class="iconImg mr10" />
          <span>Linkedin</span>
        </div>
        <div>
          <el-form
            ref="linkedinRef"
            :model="linkedInForm"
            label-position="right"
            label-width="200"
            :rules="linkedInRules"
          >
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="公司名称（英文）"
                  :prop="[LinkedinEnums.COMPANY_NAME_E]"
                >
                  <el-input
                    v-model="linkedInForm[LinkedinEnums.COMPANY_NAME_E]"
                    placeholder="请输入公司名称（英文）"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="公司地址（英文）"
                  :prop="[LinkedinEnums.COMPANY_ADDRESS_E]"
                >
                  <el-input
                    v-model="linkedInForm[LinkedinEnums.COMPANY_ADDRESS_E]"
                    placeholder="请输入公司地址（英文）"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="HQ phone 总部电话"
                  :prop="[LinkedinEnums.HQPHONE]"
                >
                  <el-input
                    v-model="linkedInForm[LinkedinEnums.HQPHONE]"
                    placeholder="请输入HQ phone 总部电话"
                    maxlength="11"
                    type="text"
                    @input="handleLinkedInPhoneInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="Official website 官网地址"
                  :prop="[LinkedinEnums.OFFICIAL_WEBSITE]"
                >
                  <el-input
                    v-model="linkedInForm[LinkedinEnums.OFFICIAL_WEBSITE]"
                    placeholder="请输入Official Website 官方链接"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="充值金额"
                  :prop="[LinkedinEnums.RECHARGE_AMOUNT]"
                >
                  <el-input
                    v-model="linkedInForm[LinkedinEnums.RECHARGE_AMOUNT]"
                    placeholder="请输入充值金额"
                    type="text"
                    maxlength="8"
                    @input="rechargeAmountHandler"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="首次上线时间"
                  :prop="[LinkedinEnums.FIRST_ONLINE_TIME]"
                >
                  <el-date-picker
                    v-model="linkedInForm[LinkedinEnums.FIRST_ONLINE_TIME]"
                    type="date"
                    placeholder="首次上线时间"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="推广时间"
                  :prop="[LinkedinEnums.PROMOTION_TIME]"
                >
                  <el-input
                    v-model="linkedInForm[LinkedinEnums.PROMOTION_TIME]"
                    placeholder="请填写时间段，如3个月或12月"
                    type="number"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="LinkedIN Company Page"
                  :prop="[LinkedinEnums.LINKEDIN_COMPANY_PAGE]"
                >
                  <el-input
                    v-model="linkedInForm[LinkedinEnums.LINKEDIN_COMPANY_PAGE]"
                    placeholder="请输入LinkedIn Company Page（Ad account admit）"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="10">
                <el-form-item
                  label="个人领英界面链接"
                  :prop="[LinkedinEnums.PERSONAL_LINKEDIN_PAGE]"
                >
                  <el-input
                    v-model="linkedInForm[LinkedinEnums.PERSONAL_LINKEDIN_PAGE]"
                    placeholder="申请广告账户管理权限的个人领英界面链接"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>
    <div class="bottom-btn">
      <el-button @click="cancel">取消</el-button>
      <el-button
        type="primary"
        v-if="selectData.length"
        @click="submitHandler"
        :loading="btnLoading"
        >提交</el-button
      >
    </div>
  </div>
  <openTips ref="openTipsRef" />
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAccountList, useIndustryList } from "./useHooks";
import {
  addAndUpdateOpenAccountApi,
  openFacebookLink,
} from "@/api/openAccount";
import {
  TiktokEnums,
  YandexEnums,
  BingEnums,
  MetaEnums,
  LinkedinEnums,
  BatchOpenAccountEnum,
  GoogleEnums,
} from "./enums";
import GoogleForm from "./component/googleForm.vue";
import iconGoogle from "@/assets/images/home/<USER>";
import iconYandex from "@/assets/images/home/<USER>";
import iconMeta from "@/assets/images/home/<USER>";
import iconTikTok from "@/assets/images/home/<USER>";
import iconBing from "@/assets/images/home/<USER>";
import iconLink from "@/assets/images/home/<USER>";
import { industryList, accountListMaping } from "@/utils/mapping";
import openTips from "@/components/openSuccess/index.vue";
import { openType } from "@/api/openAccount";
import {
  googleRules,
  commonRules,
  yandexRules,
  linkedInRules,
  bingRules,
  metaRules,
  tiktokRules,
} from "@/utils/rulesMapping";

// 类型定义
interface AccountItem {
  id: number;
  title: string;
  img: string;
  key: any;
  form: any;
}

interface CustomerExtItem {
  mediumType: number;
  applyNo?: number;
  customerInfoId?: string;
  currency?: string;
  pushUrl?: string;
  timeZone?: string;
  accountName?: string;
  invitationEmail?: string;
  [key: string]: any;
}
const openTipsRef = ref();
// 常量定义
const MAX_ACCOUNT_COUNT = 5;
const { mainList, timeZoneList } = useAccountList();
const { industryTypeList } = useIndustryList();
const router = useRouter();

// 响应式数据
const openUrl = ref("");
const openTypeTikTok = ref(1);
const openTypeMeta = ref(1);
const btnLoading = ref(false);
const selectData = ref<number[]>([1, 2, 3, 4, 5, 6]);

// 表单引用
const commonRef = ref<any>(null);
const googleRef = ref<any>(null);
const yandexRef = ref<any>(null);
const bingRef = ref<any>(null);
const linkedinRef = ref<any>(null);
const tiktokRef = ref<any>(null);
const metaRef = ref<any>(null);

// 表单数据
const form = reactive({
  [BatchOpenAccountEnum.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  [BatchOpenAccountEnum.ACCOUNT_TIME_ZONE]: "",
  [BatchOpenAccountEnum.PROMOTIONAL_LINK]: "",
});

const googleForm = reactive({
  [GoogleEnums.ADVERTISING_ACCOUNT_NAME]: "",
  [GoogleEnums.INVITE_USERS]: "",
  [GoogleEnums.CURRENCY]: "",
  [GoogleEnums.OPEN_ACCOUNT_NUM]: "",
  addAccountCount: [
    {
      [GoogleEnums.ADVERTISING_ACCOUNT_NAME]: "",
      [GoogleEnums.INVITE_USERS]: "",
      [GoogleEnums.PROMOTIONAL_LINK]: "",
    },
  ],
});

const yandexForm = reactive({
  [YandexEnums.ORGTYPE]: "",
  [YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]: "",
  [YandexEnums.OPEN_ACCOUNT_NUM]: "",
  [YandexEnums.CURRENCY]: "USD",
  yandexAccountList: [
    {
      [YandexEnums.INVITE_USERS]: "",
      [YandexEnums.ADVERTISING_ACCOUNT_NAME]: "",
    },
  ],
});

const linkedInForm = reactive({
  [LinkedinEnums.COMPANY_NAME_E]: "",
  [LinkedinEnums.COMPANY_ADDRESS_E]: "",
  [LinkedinEnums.HQPHONE]: "",
  [LinkedinEnums.OFFICIAL_WEBSITE]: "",
  [LinkedinEnums.RECHARGE_AMOUNT]: "",
  [LinkedinEnums.FIRST_ONLINE_TIME]: "",
  [LinkedinEnums.PROMOTION_TIME]: "",
  [LinkedinEnums.LINKEDIN_COMPANY_PAGE]: "",
  [LinkedinEnums.PERSONAL_LINKEDIN_PAGE]: "",
  [LinkedinEnums.OPEN_ACCOUNT_NUM]: 1,
});

const tiktokForm = reactive({
  [TiktokEnums.ACCOUNT_TYPE]: "",
  [TiktokEnums.REGISTRATION_LOCATION]: "",
  [TiktokEnums.LEGAL_PERSON_NAME]: "",
  [TiktokEnums.LEGAL_PERSON_PHONE]: "",
  [TiktokEnums.LEGAL_PERSON_ID_CARD]: "",
  [TiktokEnums.LEGAL_PERSON_BANK_CARD]: "",
  [TiktokEnums.ACCOUNT_NUMBER]: 1,
  [TiktokEnums.ADVERTISING_ACCOUNT_NAME]: "",
  [TiktokEnums.INDUSTRY_TYPE]: "",
  [TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]: "",
  accountTikTokList: [
    {
      [TiktokEnums.PRODUCT_ADVANTAGE]: "",
      [TiktokEnums.PROMOTIONAL_LINK]: "",
      [TiktokEnums.COMPANY_PROFILE]: "",
    },
  ],
});

const bingForm = reactive({
  [BingEnums.PROMOTION_COUNTRY]: "",
  [BingEnums.BUSINESS_LICENSE_POSTAL_CODE]: "",
  [BingEnums.PHONE]: "",
  [BingEnums.SUB_INDUSTRY]: "",
  [BingEnums.REGISTRATION_PLACE]: "",
  [BingEnums.INDUSTRY_TYPE]: "",
  [BingEnums.OPEN_ACCOUNT_NUM]: 1,
});

const metaForm = reactive({
  [MetaEnums.FB_HOME_PAGE]: "",
  [MetaEnums.GENDER]: "",
  [MetaEnums.BIRTHDAY]: "",
  [MetaEnums.PASSWORD]: "",
  [MetaEnums.OFFICIAL_WEBSITE]: "",
  [MetaEnums.ACCOUNT_NAME]: "",
  [MetaEnums.OPEN_ACCOUNT_NUM]: 1,
  [MetaEnums.OEID]: "",
});

// 账户列表
const openItems: AccountItem[] = [
  {
    id: 1,
    title: "Google",
    img: iconGoogle,
    key: googleRef,
    form: googleForm,
  },
  {
    id: 2,
    title: "Yandex",
    img: iconYandex,
    key: yandexRef,
    form: yandexForm,
  },
  {
    id: 3,
    title: "Meta",
    img: iconMeta,
    key: metaRef,
    form: metaForm,
  },
  {
    id: 4,
    title: "Tiktok",
    img: iconTikTok,
    key: tiktokRef,
    form: tiktokForm,
  },
  {
    id: 5,
    title: "Bing Ads",
    img: iconBing,
    key: bingRef,
    form: bingForm,
  },
  {
    id: 6,
    title: "Linkedin",
    img: iconLink,
    key: linkedinRef,
    form: linkedInForm,
  },
];

// 监听器
watch(
  [
    () => yandexForm[YandexEnums.BUSINESS_START_TIME],
    () => yandexForm[YandexEnums.BUSINESS_END_TIME],
  ],
  (newValue) => {
    yandexForm[YandexEnums.BUSINESS_HOURS] = newValue.some((item) => !item)
      ? ""
      : "1";
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  getOpenType(3);
  getOpenType(4);
});

// 工具函数
const getOpenType = async (mediumType: number) => {
  try {
    const res: any = await openType({ mediumType });
    if (res?.code === 200) {
      switch (mediumType) {
        case 3:
          openTypeMeta.value = res?.data;
          break;
        case 4:
          openTypeTikTok.value = res?.data;
          break;
      }
    }
  } catch (err) {
    console.error("获取开户类型失败:", err);
  }
};

// 表单处理函数
const addAccountName = () => {
  if (googleForm.addAccountCount?.length >= MAX_ACCOUNT_COUNT) return;
  googleForm.addAccountCount.push({
    [GoogleEnums.ADVERTISING_ACCOUNT_NAME]: "",
    [GoogleEnums.INVITE_USERS]: "",
    [GoogleEnums.PROMOTIONAL_LINK]: "",
  });
};

const addYandexAccountName = () => {
  if (yandexForm.yandexAccountList?.length >= MAX_ACCOUNT_COUNT) return;
  yandexForm.yandexAccountList.push({
    [YandexEnums.ADVERTISING_ACCOUNT_NAME]: "",
    [YandexEnums.INVITE_USERS]: "",
  });
};

const addTikTokAccountName = () => {
  if (tiktokForm.accountTikTokList?.length >= MAX_ACCOUNT_COUNT) return;
  tiktokForm.accountTikTokList.push({
    [TiktokEnums.PRODUCT_ADVANTAGE]: "",
    [TiktokEnums.PROMOTIONAL_LINK]: "",
    [TiktokEnums.COMPANY_PROFILE]: "",
  });
};

// 删除行函数
const deleteRow = (index: number) => {
  googleForm.addAccountCount?.splice(index, 1);
};

const deleteYandexRow = (index: number) => {
  yandexForm.yandexAccountList?.splice(index, 1);
};

const deleteTikTokRow = (index: number) => {
  tiktokForm.accountTikTokList?.splice(index, 1);
};

// 输入处理函数
const handlePhoneInput = (value: string) => {
  form[YandexEnums.CONTACT_PHONE] = value.replace(/[^\d]/g, "");
};

const handleTikPhoneInput = (value: string) => {
  tiktokForm[TiktokEnums.LEGAL_PERSON_PHONE] = value.replace(/[^\d]/g, "");
};

const handleLinkedInPhoneInput = (value: string) => {
  linkedInForm[LinkedinEnums.HQPHONE] = value.replace(/[^\d]/g, "");
};

const handleBingPhoneInput = (value: string) => {
  bingForm[BingEnums.PHONE] = value.replace(/[^\d]/g, "");
};

const handlerNumberInput = (value: string) => {
  let filtered = value.replace(/[^1-5]/g, "");
  tiktokForm[TiktokEnums.ACCOUNT_NUMBER] = Math.min(
    Number(filtered),
    MAX_ACCOUNT_COUNT
  );
};

const handlerCodeInput = (value: string) => {
  tiktokForm[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE] = value.replace(
    /[^a-zA-Z0-9]/g,
    ""
  );
};

const handlerIdCardInput = (value: string) => {
  tiktokForm[TiktokEnums.LEGAL_PERSON_ID_CARD] = value
    .toUpperCase()
    .replace(/[^0-9X]/g, "");
};

const checkPassword = (value: string) => {
  metaForm[MetaEnums.PASSWORD] = value.replace(/[\u4e00-\u9fa5]/g, "");
};

const rechargeAmountHandler = (value: string) => {
  form[LinkedinEnums.RECHARGE_AMOUNT] = value.replace(/[^\d]/g, "");
};

// 选择处理函数
const selectMedia = (id: number) => {
  const index = selectData.value.indexOf(id);
  if (index > -1) {
    selectData.value.splice(index, 1);
  } else {
    selectData.value.push(id);
  }
};

// 数据处理函数
const handlerParams = () => {
  try {
    const customerMainName = form[
      BatchOpenAccountEnum.MAIN_BODYOF_ACCOUNT_IS_OPENED
    ]
      ? mainList.value.find(
          (item) =>
            item.value ===
            form[BatchOpenAccountEnum.MAIN_BODYOF_ACCOUNT_IS_OPENED]
        )?.label
      : "";

    const customerExtList: CustomerExtItem[] = [];

    selectData.value.forEach((item) => {
      const obj = openItems.find((oItem) => oItem.id === item);
      if (!obj) return;

      const accountMapping = accountListMaping[obj.id];
      if (accountMapping && obj.form[accountMapping]) {
        obj.form[accountMapping].forEach((ele: any) => {
          customerExtList.push({
            mediumType: obj.id,
            ...obj.form,
            applyNo: obj.form.applyNo,
            customerInfoId: form.customerInfoId,
            currency: obj.form.currency,
            pushUrl: ele?.pushUrl || form?.pushUrl,
            timeZone: form.timeZone,
            accountName: ele.accountName || obj.form.accountName,
            invitationEmail: ele.invitationEmail?.join(",") || "",
          });
        });
      } else {
        //  特殊处理meta
        if (obj.id === 3 && obj.form.openType === 1) {
          for (let i = 0; i < obj.form.applyNo; i++) {
            customerExtList.push({
              mediumType: obj.id,
              ...obj.form,
              customerInfoId: form.customerInfoId,
              currency: obj.form.currency,
              pushUrl: form?.pushUrl,
              timeZone: form.timeZone,
            });
          }
        } else {
          customerExtList.push({
            mediumType: obj.id,
            ...obj.form,
            customerInfoId: form.customerInfoId,
            currency: obj.form.currency,
            pushUrl: form?.pushUrl,
            timeZone: form.timeZone,
          });
        }
      }
    });

    const data = {
      ...form,
      openType: openTypeMeta.value,
      applyNo:
        customerExtList
          .map((item) => Number(item.applyNo))
          .reduce((acc, currentValue) => acc + currentValue, 0) || 1,
      customerExtList,
      customerMainName,
    };

    // 清理不需要的数据
    delete data.customerExtList[0]?.addAccountCount;
    delete data.customerExtList[1]?.yandexAccountList;
    delete data.customerExtList[3]?.accountTikTokList;

    return data;
  } catch (error) {
    console.error("处理参数失败:", error);
    return null;
  }
};

// 主要业务函数
const openLink = async () => {
  try {
    const res: any = await openFacebookLink();
    if (res.code === 200) {
      openUrl.value = res.data;
      window.open(res.data);
    }
  } catch (error) {
    console.error("打开链接失败:", error);
  }
};

const mainChangeHandler = (value: string) => {
  const item = mainList.value.find((item) => item.value === value);
  if (!item) return;

  bingForm[BingEnums.REGISTRATION_PLACE] = item.address;
  bingForm[BingEnums.PHONE] = item.contactsPhone;
  tiktokForm[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE] =
    item[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE];
  tiktokForm[TiktokEnums.REGISTRATION_LOCATION] = item?.address;
  tiktokForm[TiktokEnums.LEGAL_PERSON_NAME] = item?.legalName;
  yandexForm[YandexEnums.CONTACT_EMAIL] = item[YandexEnums.CONTACT_EMAIL];
  yandexForm[YandexEnums.CONTACT_NAME] = item[YandexEnums.CONTACT_NAME];
  yandexForm[YandexEnums.CONTACT_PHONE] = item[YandexEnums.CONTACT_PHONE];

  yandexRef.value?.clearValidate();
  tiktokRef.value?.clearValidate();
  bingRef.value?.clearValidate();
};

const submitHandler = async () => {
  const promiseArr = [commonRef.value?.validate()];
  selectData.value.forEach((item) => {
    const obj = openItems.find((oItem) => oItem.id === item);
    if (obj?.key?.value) {
      promiseArr.push(obj.key.value.validate());
    }
  });

  try {
    const valid = await Promise.all(promiseArr);
    if (valid.every((item) => item)) {
      const data = handlerParams();
      if (!data) return;

      btnLoading.value = true;
      try {
        const res: any = await addAndUpdateOpenAccountApi(data);
        if (res.code == 200) {
          openTipsRef?.value.open();
        }
      } finally {
        btnLoading.value = false;
      }
    }
  } catch (err) {
    console.error("提交失败:", err);
  }
};

const cancel = () => {
  router.go(-1);
};
</script>

<style scoped lang="scss">
.batchApply {
  padding: 20px;
  .batch-form {
    background-color: #fff;
    padding: 24px 34px 6px 34px;
    margin-bottom: 24px;
    border-radius: 8px;
  }
  .select-box {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    ul {
      display: flex;
      gap: 12px;
      li {
        cursor: pointer;
        padding: 6px 10px;
        border-radius: 8px;
        border: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        position: relative;
        gap: 8px;
        color: #333333;
        font-size: 14px;
        font-weight: 500;
        .bingo {
          position: absolute;
          color: #fff;
          bottom: -1px;
          right: 0;
        }
      }
      .liActive {
        border: 1px solid #1f7bf2;
        background: #ffffff;
      }
    }
    .logo {
      width: 24px;
      height: 24px;
      .svg-icon {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }
  .box-list {
    margin-top: 22px;
    display: flex;
    flex-direction: column;
    gap: 22px;
    .box {
      border-radius: 8px;
      background: #ffffff;
      padding: 20px;
      .box-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        span {
          color: #333333;
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
}
.bottom-btn {
  margin-top: 20px;
  background-color: #fff;

  padding: 20px;
  text-align: end;
}
.iconImg {
  width: 18px;
  height: 18px;
}
.add-account-container {
  .el-icon--delete:hover {
    color: #ff4d4f;
    cursor: pointer;
  }
}
</style>
