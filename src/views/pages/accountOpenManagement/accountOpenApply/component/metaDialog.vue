<template>
  <div>
    <el-drawer
      v-model="props.visible"
      title="Meta开户申请"
      size="600"
      :before-close="closeDialog"
    >
      <template #header>
        <h3>
          Meta开户{{ props.type === "view" ? "详情" : "申请" }}
          <span v-if="props.type === 'view'" class="form-status">
            {{ showStatusSrt }}
          </span>
        </h3>
      </template>
      <div>
        <h4 class="form-header" v-if="props.type === 'view'">申请信息</h4>
        <el-form
          ref="formRef"
          style="max-width: 600px"
          :model="form"
          :rules="rules"
          label-position="left"
          :inline="false"
          label-width="120"
        >
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户主体"
                :prop="[MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
              >
                <el-select
                  v-model="form[MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
                  placeholder="请选择开户主体"
                  filterable
                  :disabled="type === 'view'"
                >
                  <el-option
                    v-for="item in mainList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button
                type="primary"
                @click="addBodyHandler"
                :disabled="type === 'view'"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="币种：">
                <el-text>USD</el-text>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户数量"
                :prop="[MetaEnums.OPEN_ACCOUNT_NUM]"
              >
                <el-input
                  v-model="form[MetaEnums.OPEN_ACCOUNT_NUM]"
                  placeholder="输入开户数量，一次最多支持开通5个"
                  :disabled="props.type === 'view'"
                  maxlength="5"
                  type="number"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="开户链接：">
                <div class="f fd-c aiss">
                  <el-link
                    target="_blank"
                    type="primary"
                    :underline="false"
                    style="font-size: 14px"
                    @click.stop="openLink"
                    >https://www.facebook.com</el-link
                  >
                  <el-link type="info" :underline="false"
                    >1、点击上方开户链接，提交资料</el-link
                  >
                  <el-link type="info" :underline="false"
                    >2、提交成功后需复制OE申请编号，填写至下方</el-link
                  >
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="OE申请编号" :prop="[MetaEnums.OEID]">
                <el-input
                  v-model="form[MetaEnums.OEID]"
                  placeholder="请输入OE申请编号"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
                <el-link type="info" :underline="false"
                  >请正确填写申请编号，以免影响您的开户进度</el-link
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="infoMain" v-if="props.type === 'view'">
        <h4 class="form-header">账户信息</h4>
        <div>
          <div class="infoContent">
            <div class="infoLable">粉丝页：</div>
            <div class="infoValue">{{ fbHomeUrl }}</div>
          </div>
          <div class="infoContent">
            <div class="infoLable">可推广网址：</div>
            <div class="infoValue">{{ companyPageUrl }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            v-if="type !== 'view'"
            :loading="btnLoading"
            @click="submitHandler"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-drawer>
    <addBodyDialog
      v-model:visible="addBodyShow"
      v-if="addBodyShow"
      @close="closeBodyHandler"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  withDefaults,
  onMounted,
  computed,
} from "vue";
import addBodyDialog from "../../accountOpenMain/component/addBodyDialog.vue";
import { MetaEnums } from "../enums";
import { useAccountList } from "../useHooks";
import {
  addAndUpdateOpenAccountApi,
  getOpenReportDetailApi,
  openFacebookLink,
} from "@/api/openAccount";
import { auditStateMapping } from "@/utils/mapping";
import { ElMessage } from "element-plus";

const fbHomeUrl = ref("");
const companyPageUrl = ref("");
const openUrl = ref("");
const { mainList, timeZoneList, getMainList } = useAccountList();
const props = withDefaults(
  defineProps<{
    visible: boolean;
    type?: string;
    rowData?: any;
    openType: number;
  }>(),
  { type: "add" },
);
const addBodyShow = ref(false);
const closeBodyHandler = () => {
  getMainList();
};
const emit = defineEmits(["update:visible", "close"]);
const form = reactive({
  [MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  [MetaEnums.OEID]: "",
  [MetaEnums.OPEN_ACCOUNT_NUM]: "",
  [MetaEnums.CURRENCY]: "USD",
  openType: "",
});
const checkPassword = (value) => {
  form[MetaEnums.PASSWORD] = value.replace(/[\u4e00-\u9fa5]/g, "");
};
const rules = reactive({
  [MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [MetaEnums.OEID]: [
    { required: true, message: "请输入OE申请编号", trigger: "change" },
  ],
  [MetaEnums.OPEN_ACCOUNT_NUM]: [
    { required: true, message: "请输入开户数量", trigger: "change" },
  ],
});
let customerOpenExtId = ref("");
// 处理参数
const handlerParams = () => {
  let customerMainName = mainList.value.find(
    (item) => item.value == form[MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED],
  )["label"];
  let data: any = {
    ...form,
    openType: props.openType,
    customerMainName,
    customerExtList: [
      {
        mediumType: 3,
        openType: props.openType,
        [MetaEnums.OPEN_ACCOUNT_NUM]: form[MetaEnums.OPEN_ACCOUNT_NUM],
        pushUrl: openUrl.value,
        [MetaEnums.CURRENCY]: "USD",
        customerOpenExtId: customerOpenExtId.value,
      },
    ],
  };
  delete data[MetaEnums.OFFICIAL_WEBSITE];
  delete data[MetaEnums.GENDER];
  delete data[MetaEnums.BIRTHDAY];
  delete data[MetaEnums.PASSWORD];
  delete data[MetaEnums.FB_HOME_PAGE];
  delete data[MetaEnums.ACCOUNT_NAME];
  if (props.type === "edit") {
    data.applyId = props.rowData?.applyId;
  }
  if (data.applyNo != 1) {
    let listNum = Number(data.applyNo);
    for (let i = 0; i < listNum; i++) {
      if (i != 0) {
        data.customerExtList.push(data.customerExtList[0]);
      }
    }
  }
  return data;
};
let formRef = ref();
// 提交
const btnLoading = ref(false);
const submitHandler = () => {
  formRef.value?.validate().then((valid) => {
    if (valid) {
      let data = handlerParams();
      btnLoading.value = true;
      addAndUpdateOpenAccountApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            // ElMessage.success("提交成功");
            closeDialog();
            emit("close", { type: 3, applyId: res.data.applyIdList[0] });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const closeDialog = () => {
  emit("update:visible", false);
};
const addBodyHandler = () => {
  addBodyShow.value = true;
};
const openLink = async () => {
  try {
    const res: any = await openFacebookLink();
    if (res.code == 200) {
      openUrl.value = res.data;
      window.open(res.data);
    }
  } catch (error) {
    console.log(error);
  }
};
const getReportDeatil = () => {
  getOpenReportDetailApi({
    applyId: props.rowData?.applyId,
  }).then((res: any) => {
    if (res && res.code && res.code == 200) {
      form[MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED] = res.data.customerInfoId;
      form[MetaEnums.PROMOTIONAL_LINK] = res.data.pushUrl;
      form[MetaEnums.ACCOUNT_TIME_ZONE] = res.data.timeZone;
      form[MetaEnums.OFFICIAL_WEBSITE] =
        res.data?.customerOpenExt?.officialWebsiteUrl;
      form[MetaEnums.PASSWORD] = res.data?.customerOpenExt?.secretKey;
      form[MetaEnums.FB_HOME_PAGE] = res.data?.customerOpenExt?.fbHomeUrl;
      form[MetaEnums.BIRTHDAY] = res.data?.customerOpenExt?.birthday;
      form[MetaEnums.GENDER] = res.data?.customerOpenExt?.sex;
      form[MetaEnums.ACCOUNT_NAME] = res.data?.customerOpenExt?.accountName;
      form[MetaEnums.OEID] = res.data?.thirdApplyId;
      form[MetaEnums.OPEN_ACCOUNT_NUM] = res.data?.applyNo;
      customerOpenExtId.value = res.data?.customerOpenExt?.customerOpenExtId;
      form.openType = res.data?.openType;
      fbHomeUrl.value = res.data?.customerOpenExt?.fbHomeUrl;
      companyPageUrl.value = res.data?.customerOpenExt?.companyPageUrl;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const showStatusSrt = computed(() => {
  let str = "";
  if (props.rowData?.auditState == 3) {
    props.rowData?.state == 1
      ? (str = "开户中")
      : props.rowData?.state == 2
      ? (str = "开户成功")
      : props.rowData?.state == 3
      ? (str = "开户失败")
      : props.rowData?.state == 4
      ? (str = "部分开户成功")
      : (str = "");
  } else {
    props.rowData?.auditState == 1
      ? (str = "待审核")
      : props.rowData?.auditState == 2
      ? (str = "审核中")
      : props.rowData?.auditState == 3
      ? (str = "审核通过")
      : props.rowData?.auditState == 4
      ? (str = "审核驳回")
      : (str = "");
  }
  return str;
});
onMounted(() => {
  if (props.type == "view" || props.type == "edit") {
    getReportDeatil();
  }
});
</script>

<style scoped lang="scss">
:deep(.el-dialog__headerbtn) {
  right: 24px !important;
}
:deep(.el-dialog__header) {
  padding-left: 24px;
  border-bottom: 1px solid #dcdee0;
  margin-bottom: 16px;
}
:deep(.el-form-item) {
  flex-direction: column;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
.infoMain {
  .infoContent {
    display: flex;
    margin-bottom: 10px;
    padding: 0 24px;
    font-size: 14px;
    color: #333;
    .infoLable {
      width: 98px;
      color: #606266;
      text-align: right;
    }
    .infoValue {
      color: #333;
      font-size: 14px;
      margin-left: 10px;
    }
  }
}
</style>
