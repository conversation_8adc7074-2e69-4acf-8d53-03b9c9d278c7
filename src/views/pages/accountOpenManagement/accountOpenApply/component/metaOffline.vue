<template>
  <div>
    <el-drawer
      v-model="props.visible"
      title="Meta开户申请"
      size="600"
      :before-close="closeDialog"
    >
      <template #header>
        <h3>
          Meta开户{{ props.type === "view" ? "详情" : "申请" }}
          <span v-if="props.type === 'view'" class="form-status">
            {{ showStatusSrt }}
          </span>
        </h3>
      </template>
      <div>
        <h4 class="form-header" v-if="props.type === 'view'">申请信息</h4>
        <el-form
          ref="formRef"
          style="max-width: 600px"
          :model="form"
          :rules="rules"
          label-position="left"
          :inline="false"
          label-width="120"
        >
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户主体"
                :prop="[MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
              >
                <el-select
                  v-model="form[MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
                  placeholder="请选择开户主体"
                  filterable
                  :disabled="type === 'view'"
                >
                  <el-option
                    v-for="item in mainList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" class="pt-31">
              <el-button
                type="primary"
                @click="addBodyHandler"
                :disabled="type === 'view'"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="账户时区"
                :prop="[MetaEnums.ACCOUNT_TIME_ZONE]"
              >
                <el-select
                  v-model="form[MetaEnums.ACCOUNT_TIME_ZONE]"
                  placeholder="请选择账户时区"
                  filterable
                  :disabled="type === 'view'"
                >
                  <el-option
                    v-for="item in timeZoneList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="账户名称" :prop="[MetaEnums.ACCOUNT_NAME]">
                <el-input
                  v-model="form[MetaEnums.ACCOUNT_NAME]"
                  placeholder="请输入账户名称"
                  :disabled="type === 'view'"
                  type="text"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="推广链接"
                :prop="[MetaEnums.PROMOTIONAL_LINK]"
              >
                <el-input
                  v-model="form[MetaEnums.PROMOTIONAL_LINK]"
                  placeholder="请输入推广链接"
                  :disabled="type === 'view'"
                  type="text"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20"
              ><el-form-item label="FB主页" :prop="[MetaEnums.FB_HOME_PAGE]">
                <el-input
                  v-model="form[MetaEnums.FB_HOME_PAGE]"
                  placeholder="请输入FB主页"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="性别" :prop="[MetaEnums.GENDER]">
                <el-radio-group
                  v-model="form[MetaEnums.GENDER]"
                  :disabled="type === 'view'"
                >
                  <el-radio :value="1">男</el-radio>
                  <el-radio :value="2">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="生日" :prop="[MetaEnums.BIRTHDAY]">
                <el-date-picker
                  v-model="form[MetaEnums.BIRTHDAY]"
                  type="date"
                  placeholder="生日"
                  value-format="YYYY-MM-DD"
                  :disabled="type === 'view'"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="密码" :prop="[MetaEnums.PASSWORD]">
                <el-input
                  v-model="form[MetaEnums.PASSWORD]"
                  placeholder="请输入密码，必须包含至少6个字母或数字"
                  :disabled="type === 'view'"
                  type="text"
                  @input="checkPassword"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="官方网址"
                :prop="[MetaEnums.OFFICIAL_WEBSITE]"
              >
                <el-input
                  v-model="form[MetaEnums.OFFICIAL_WEBSITE]"
                  placeholder="请输入公司的官方网址"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            v-if="type !== 'view'"
            :loading="btnLoading"
            @click="submitHandler"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-drawer>
    <addBodyDialog
      v-model:visible="addBodyShow"
      v-if="addBodyShow"
      @close="closeBodyHandler"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  withDefaults,
  onMounted,
  computed,
} from "vue";
import addBodyDialog from "../../accountOpenMain/component/addBodyDialog.vue";
import { MetaEnums } from "../enums";
import { useAccountList } from "../useHooks";
import openTips from "@/components/openSuccess/index.vue";
import { auditStateMapping } from "@/utils/mapping";
import {
  addAndUpdateOpenAccountApi,
  getOpenReportDetailApi,
} from "@/api/openAccount";
import { ElMessage } from "element-plus";
const { mainList, timeZoneList, getMainList } = useAccountList();
const props = withDefaults(
  defineProps<{
    visible: boolean;
    type?: string;
    rowData?: any;
    openType: number;
  }>(),
  { type: "add" },
);
const addBodyShow = ref(false);
const closeBodyHandler = () => {
  getMainList();
};
const emit = defineEmits(["update:visible", "close"]);
const form = reactive({
  [MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  [MetaEnums.GENDER]: "",
  [MetaEnums.PASSWORD]: "",
  [MetaEnums.FB_HOME_PAGE]: "",
  [MetaEnums.BIRTHDAY]: "",
  [MetaEnums.ACCOUNT_TIME_ZONE]: "",
  [MetaEnums.PROMOTIONAL_LINK]: "",
  [MetaEnums.OFFICIAL_WEBSITE]: "",
  [MetaEnums.ACCOUNT_NAME]: "",
  openType: "",
}) as any;
const checkPassword = (value) => {
  form[MetaEnums.PASSWORD] = value.replace(/[\u4e00-\u9fa5]/g, "");
};
const rules = reactive({
  [MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [MetaEnums.GENDER]: [
    { required: true, message: "请选择性别", trigger: "change" },
  ],
  [MetaEnums.FB_HOME_PAGE]: [
    { required: true, message: "请输入FB主页", trigger: "blur" },
  ],
  [MetaEnums.BIRTHDAY]: [
    { required: true, message: "请选择生日", trigger: "change" },
  ],
  [MetaEnums.PASSWORD]: [
    {
      required: true,
      message: "请输入密码",
      trigger: "blur",
    },
    {
      min: 6,
      message: "密码至少为6位",
      trigger: ["blur", "change"],
    },
  ],
  [MetaEnums.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择账户时区", trigger: "change" },
  ],
  [MetaEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "blur" },
  ],
  [MetaEnums.OFFICIAL_WEBSITE]: [
    { required: true, message: "请输入公司的官方网址", trigger: "blur" },
  ],
  [MetaEnums.ACCOUNT_NAME]: [
    { required: true, message: "请输入账户名称", trigger: "blur" },
  ],
});
let customerOpenExtId = ref("");
// 处理参数
const handlerParams = () => {
  let customerMainName = mainList.value.find(
    (item) => item.value == form[MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED],
  )["label"];
  let data: any = {
    ...form,
    openType: props.openType,
    applyNo: 1, // 默认值
    customerExtList: [
      {
        mediumType: 3,
        openType: props.openType,
        [MetaEnums.OPEN_ACCOUNT_NUM]: 1,
        [MetaEnums.OFFICIAL_WEBSITE]: form[MetaEnums.OFFICIAL_WEBSITE],
        [MetaEnums.GENDER]: form[MetaEnums.GENDER],
        [MetaEnums.BIRTHDAY]: form[MetaEnums.BIRTHDAY],
        [MetaEnums.PASSWORD]: form[MetaEnums.PASSWORD],
        [MetaEnums.FB_HOME_PAGE]: form[MetaEnums.FB_HOME_PAGE],
        [MetaEnums.ACCOUNT_NAME]: form[MetaEnums.ACCOUNT_NAME],
        [MetaEnums.ACCOUNT_TIME_ZONE]: form[MetaEnums.ACCOUNT_TIME_ZONE],
        [MetaEnums.PROMOTIONAL_LINK]: form[MetaEnums.PROMOTIONAL_LINK],
        customerOpenExtId: customerOpenExtId.value,
      },
    ],
    customerMainName,
  };
  delete data[MetaEnums.PROMOTIONAL_LINK];
  delete data[MetaEnums.ACCOUNT_TIME_ZONE];
  delete data[MetaEnums.OFFICIAL_WEBSITE];
  delete data[MetaEnums.GENDER];
  delete data[MetaEnums.BIRTHDAY];
  delete data[MetaEnums.PASSWORD];
  delete data[MetaEnums.FB_HOME_PAGE];
  delete data[MetaEnums.ACCOUNT_NAME];
  if (props.type === "edit") {
    data.applyId = props.rowData?.applyId;
  }
  return data;
};
let formRef = ref();
// 提交
const btnLoading = ref(false);
const submitHandler = () => {
  formRef.value?.validate().then((valid) => {
    if (valid) {
      let data = handlerParams();
      btnLoading.value = true;
      addAndUpdateOpenAccountApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            // ElMessage.success("提交成功");
            closeDialog();
            emit("close", { type: 3, applyId: res.data.applyIdList[0] });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const closeDialog = () => {
  emit("update:visible", false);
};
const addBodyHandler = () => {
  addBodyShow.value = true;
};
const getReportDeatil = () => {
  getOpenReportDetailApi({
    applyId: props.rowData?.applyId,
  }).then((res: any) => {
    if (res && res.code && res.code == 200) {
      form[MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED] = res.data.customerInfoId;
      form[MetaEnums.PROMOTIONAL_LINK] =
        res.data?.customerOpenExtList[0]?.pushUrl;
      form[MetaEnums.ACCOUNT_TIME_ZONE] = res.data?.customerOpenExt?.timeZone;
      form[MetaEnums.OFFICIAL_WEBSITE] =
        res.data?.customerOpenExt?.officialWebsiteUrl;
      form[MetaEnums.PASSWORD] = res.data?.customerOpenExt?.secretKey;
      form[MetaEnums.FB_HOME_PAGE] = res.data?.customerOpenExt?.fbHomeUrl;
      form[MetaEnums.BIRTHDAY] = res.data?.customerOpenExt?.birthday;
      form[MetaEnums.GENDER] = res.data?.customerOpenExt?.sex;
      form[MetaEnums.ACCOUNT_NAME] = res.data?.customerOpenExt?.accountName;
      customerOpenExtId.value = res.data?.customerOpenExt?.customerOpenExtId;
      form.openType = res.data?.openType;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const showStatusSrt = computed(() => {
  let str = "";
  if (props.rowData?.auditState == 3) {
    props.rowData?.state == 1
      ? (str = "开户中")
      : props.rowData?.state == 2
      ? (str = "开户成功")
      : props.rowData?.state == 3
      ? (str = "开户失败")
      : props.rowData?.state == 4
      ? (str = "部分开户成功")
      : (str = "");
  } else {
    props.rowData?.auditState == 1
      ? (str = "待审核")
      : props.rowData?.auditState == 2
      ? (str = "审核中")
      : props.rowData?.auditState == 3
      ? (str = "审核通过")
      : props.rowData?.auditState == 4
      ? (str = "审核驳回")
      : (str = "");
  }
  return str;
});
onMounted(() => {
  if (props.type == "view" || props.type == "edit") {
    getReportDeatil();
  }
});
</script>

<style scoped lang="scss">
:deep(.el-dialog__headerbtn) {
  right: 24px !important;
}
:deep(.el-dialog__header) {
  padding-left: 24px;
  border-bottom: 1px solid #dcdee0;
  margin-bottom: 16px;
}
:deep(.el-form-item) {
  flex-direction: column;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
