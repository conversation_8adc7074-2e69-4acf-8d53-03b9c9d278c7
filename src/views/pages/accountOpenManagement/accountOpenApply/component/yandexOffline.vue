<template>
  <div>
    <el-drawer
      v-model="props.visible"
      title="Yandex开户申请"
      size="600"
      :before-close="closeDialog"
    >
      <template #header>
        <h3>
          Yandex开户{{ props.type === "view" ? "详情" : "申请" }}
          <span v-if="props.type === 'view'" class="form-status">
            {{ showStatusSrt }}
          </span>
        </h3>
      </template>
      <div>
        <h4 class="form-header" v-if="props.type === 'view'">申请信息</h4>
        <el-form
          ref="formRef"
          style="max-width: 600px"
          :model="form"
          :rules="rules"
          label-width="150"
          label-position="left"
          :inline="false"
        >
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户主体"
                :prop="[YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
              >
                <el-select
                  v-model="form[YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
                  placeholder="请选择开户主体"
                  filterable
                  :disabled="type === 'view'"
                  @change="mainChagneHandler"
                >
                  <el-option
                    v-for="item in mainList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" class="pt-31">
              <el-button
                type="primary"
                @click="addBodyHandler"
                :disabled="type === 'view'"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="账户时区"
                :prop="[YandexEnums.ACCOUNT_TIME_ZONE]"
              >
                <el-select
                  v-model="form[YandexEnums.ACCOUNT_TIME_ZONE]"
                  placeholder="请选择账户时区"
                  filterable
                  :disabled="type === 'view'"
                >
                  <el-option
                    v-for="item in timeZoneList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20"
              ><el-form-item
                label="营业时间"
                :prop="[YandexEnums.BUSINESS_HOURS]"
              >
                <div style="display: flex; gap: 15px">
                  <el-time-picker
                    v-model="form[YandexEnums.BUSINESS_START_TIME]"
                    placeholder="选择开始时间"
                    value-format="HH:mm:ss"
                    style="width: 150px"
                    :disabled="type === 'view'"
                  />
                  <span>~</span>
                  <el-time-picker
                    v-model="form[YandexEnums.BUSINESS_END_TIME]"
                    placeholder="选择结束时间"
                    value-format="HH:mm:ss"
                    style="width: 150px"
                    :disabled="type === 'view'"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="每日预算" :prop="[YandexEnums.DAILY_BUDGET]">
                <el-input
                  v-model="form[YandexEnums.DAILY_BUDGET]"
                  placeholder="请输入每日预算，单位为美元，建议50元/天"
                  :disabled="type === 'view'"
                  maxlength="8"
                  type="text"
                  @input="DailyBugetHandler"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="推广链接"
                :prop="[YandexEnums.PROMOTIONAL_LINK]"
              >
                <el-input
                  v-model="form[YandexEnums.PROMOTIONAL_LINK]"
                  placeholder="请输入推广链接"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="联系人姓名"
                :prop="[YandexEnums.CONTACT_NAME]"
              >
                <el-input
                  v-model="form[YandexEnums.CONTACT_NAME]"
                  placeholder="请输入联系人姓名"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="联系人手机号"
                :prop="[YandexEnums.CONTACT_PHONE]"
              >
                <el-input
                  v-model="form[YandexEnums.CONTACT_PHONE]"
                  placeholder="请输入联系人手机号"
                  @input="handlePhoneInput"
                  :disabled="type === 'view'"
                  maxlength="11"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="联系人邮箱"
                :prop="[YandexEnums.CONTACT_EMAIL]"
              >
                <el-input
                  v-model="form[YandexEnums.CONTACT_EMAIL]"
                  placeholder="请输入联系人邮箱"
                  :disabled="type === 'view'"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="公司简介"
                :prop="[YandexEnums.COMPANY_PROFILE]"
              >
                <el-input
                  v-model="form[YandexEnums.COMPANY_PROFILE]"
                  type="textarea"
                  placeholder="请输入公司简介"
                  :disabled="type === 'view'"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="产品优势"
                :prop="[YandexEnums.PRODUCT_ADVANTAGE]"
              >
                <el-input
                  v-model="form[YandexEnums.PRODUCT_ADVANTAGE]"
                  type="textarea"
                  placeholder="请输入产品优势"
                  :disabled="type === 'view'"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户数量"
                :prop="[YandexEnums.OPEN_ACCOUNT_NUM]"
              >
                <el-input
                  v-model="form[YandexEnums.OPEN_ACCOUNT_NUM]"
                  placeholder="输入开户数量，一次最多支持开通5个"
                  :disabled="props.type === 'view'"
                  maxlength="5"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            v-if="type !== 'view'"
            :loading="btnLoading"
            @click="submitHandler"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-drawer>
    <addBodyDialog
      v-model:visible="addBodyShow"
      v-if="addBodyShow"
      @close="closeBodyHandler"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  withDefaults,
  watch,
  onMounted,
  computed,
} from "vue";
import addBodyDialog from "../../accountOpenMain/component/addBodyDialog.vue";
import { auditStateMapping } from "@/utils/mapping";
import { YandexEnums } from "../enums";
import { useAccountList } from "../useHooks";
import {
  addAndUpdateOpenAccountApi,
  getOpenReportDetailApi,
} from "@/api/openAccount";
import { validateEmail } from "@/utils";
import { ElMessage } from "element-plus";
const { mainList, timeZoneList, getMainList } = useAccountList();
const openTipsRef = ref();
const props = withDefaults(
  defineProps<{
    visible: boolean;
    type?: string;
    rowData?: any;
    openType: number;
  }>(),
  { type: "add" },
);
const closeBodyHandler = () => {
  getMainList();
};
const addBodyShow = ref(false);
const emit = defineEmits(["update:visible", "close"]);
const form = reactive({
  [YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  [YandexEnums.DAILY_BUDGET]: "",
  [YandexEnums.CONTACT_PHONE]: "",
  [YandexEnums.BUSINESS_HOURS]: "",
  [YandexEnums.BUSINESS_START_TIME]: "",
  [YandexEnums.BUSINESS_END_TIME]: "",
  [YandexEnums.CONTACT_NAME]: "",
  [YandexEnums.CONTACT_EMAIL]: "",
  [YandexEnums.COMPANY_PROFILE]: "",
  [YandexEnums.PROMOTIONAL_LINK]: "",
  [YandexEnums.PRODUCT_ADVANTAGE]: "",
  [YandexEnums.OPEN_ACCOUNT_NUM]: "",
  openType: "",
});
watch(
  [
    () => form[YandexEnums.BUSINESS_START_TIME],
    () => form[YandexEnums.BUSINESS_END_TIME],
  ],
  (newValue) => {
    if (!newValue.some((item) => !item)) {
      form[YandexEnums.BUSINESS_HOURS] = "1";
    } else {
      form[YandexEnums.BUSINESS_HOURS] = "";
    }
  },
  { deep: true },
);
const DailyBugetHandler = (value) => {
  form[YandexEnums.DAILY_BUDGET] = value.replace(/[^\d]/g, "");
};
// 输入时强制数字
const handlePhoneInput = (value) => {
  form[YandexEnums.CONTACT_PHONE] = value.replace(/[^\d]/g, "");
};
// 邮箱正则（兼容新顶级域名和国际化域名）
const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/;
const rules = reactive({
  [YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [YandexEnums.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择账户时区", trigger: "change" },
  ],
  [YandexEnums.DAILY_BUDGET]: [
    { required: true, message: "请输入每日预算", trigger: "blur" },
  ],
  [YandexEnums.BUSINESS_HOURS]: [
    { required: true, message: "请选择营业时间", trigger: "change" },
  ],
  [YandexEnums.CONTACT_NAME]: [
    { required: true, message: "请输入联系人姓名", trigger: "blur" },
  ],
  [YandexEnums.CONTACT_PHONE]: [
    { required: true, message: "请输入联系人手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/, // 最新号段正则（支持13-19开头）
      message: "请输入正确的手机号",
      trigger: ["blur", "change"],
    },
  ],
  [YandexEnums.CONTACT_EMAIL]: [
    { required: true, message: "请输入联系人邮箱", trigger: "blur" },
    {
      pattern: emailReg,
      message: "请输入有效的邮箱地址",
      trigger: ["blur", "change"],
    },
  ],
  [YandexEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "blur" },
  ],
  [YandexEnums.OPEN_ACCOUNT_NUM]: [
    { required: true, message: "请输入开户数量", trigger: "blur" },
  ],
});

const closeDialog = () => {
  emit("update:visible", false);
};
const customerOpenExtId = ref("");
// 参数整理
const handlerParams = () => {
  let customerMainName = mainList.value.find(
    (item) => item.value == form[YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED],
  )["label"];
  let data: any = {
    ...form,
    customerExtList: [
      {
        mediumType: 2,
        [YandexEnums.BUSINESS_END_TIME]: form[YandexEnums.BUSINESS_END_TIME],
        [YandexEnums.BUSINESS_START_TIME]:
          form[YandexEnums.BUSINESS_START_TIME],
        [YandexEnums.DAILY_BUDGET]: form[YandexEnums.DAILY_BUDGET],
        [YandexEnums.COMPANY_PROFILE]: form[YandexEnums.COMPANY_PROFILE],
        [YandexEnums.PRODUCT_ADVANTAGE]: form[YandexEnums.PRODUCT_ADVANTAGE],
        [YandexEnums.CONTACT_EMAIL]: form[YandexEnums.CONTACT_EMAIL],
        [YandexEnums.CONTACT_NAME]: form[YandexEnums.CONTACT_NAME],
        [YandexEnums.CONTACT_PHONE]: form[YandexEnums.CONTACT_PHONE],
        customerOpenExtId: customerOpenExtId.value,
      },
    ],
    customerMainName,
  };
  delete data.businessStartTime;
  delete data.businessEndTime;
  delete data.budgetDay;
  delete data.companyProfile;
  delete data.productAdvantage;
  delete data.businessHours;
  delete data[YandexEnums.CONTACT_EMAIL];
  delete data[YandexEnums.CONTACT_NAME];
  delete data[YandexEnums.CONTACT_PHONE];
  if (props.type == "edit") {
    data.applyId = props.rowData?.applyId;
  }
  return data;
};
let formRef = ref();
const btnLoading = ref(false);
const submitHandler = () => {
  formRef.value.validate().then((valid) => {
    if (valid) {
      let data = handlerParams();
      if (form[YandexEnums.OPEN_ACCOUNT_NUM] != data?.customerExtList?.length) {
        return ElMessage.error("开户数量和当前账户数量不符");
      }
      data?.customerExtList?.forEach((item) => {
        const invitationEmailList = item?.invitationEmail?.split(",") || [];
        if (
          invitationEmailList?.filter((item) => !validateEmail(item))?.length &&
          item?.invitationEmail
        ) {
          ElMessage.error("邀请用户邮箱格式不正确，请重新填写");
          throw new Error("邀请用户邮箱格式不正确，请重新填写");
        }
      });
      btnLoading.value = true;
      addAndUpdateOpenAccountApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            // ElMessage.success("提交成功");
            closeDialog();
            emit("close", { type: 2, applyId: res.data.applyIdList[0] });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const getReportDeatil = () => {
  getOpenReportDetailApi({
    applyId: props.rowData?.applyId,
  }).then((res: any) => {
    if (res && res.code && res.code == 200) {
      form[YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED] = res.data.customerInfoId;
      form[YandexEnums.PROMOTIONAL_LINK] = res.data.pushUrl;
      form[YandexEnums.ACCOUNT_TIME_ZONE] = res.data.timeZone;
      form[YandexEnums.BUSINESS_START_TIME] =
        res.data?.customerOpenExt?.businessStartTime;
      form[YandexEnums.BUSINESS_END_TIME] =
        res.data?.customerOpenExt?.businessEndTime;
      form[YandexEnums.DAILY_BUDGET] = res.data?.customerOpenExt?.budgetDay;
      form[YandexEnums.COMPANY_PROFILE] =
        res.data?.customerOpenExt?.companyProfile;
      form[YandexEnums.PRODUCT_ADVANTAGE] =
        res.data?.customerOpenExt?.productAdvantage;
      form[YandexEnums.CONTACT_NAME] = res.data?.customerOpenExt?.contactsName;
      form[YandexEnums.CONTACT_PHONE] =
        res.data?.customerOpenExt?.contactsPhone;
      form[YandexEnums.CONTACT_EMAIL] =
        res.data?.customerOpenExt?.contactsEmail;
      customerOpenExtId.value = res.data?.customerOpenExt?.customerOpenExtId;
      form.openType = res.data?.openType;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const mainChagneHandler = (value) => {
  let item = mainList.value.find((item) => item.value == value);
  form[YandexEnums.CONTACT_EMAIL] = item[YandexEnums.CONTACT_EMAIL];
  form[YandexEnums.CONTACT_NAME] = item[YandexEnums.CONTACT_NAME];
  form[YandexEnums.CONTACT_PHONE] = item[YandexEnums.CONTACT_PHONE];
  formRef.value?.clearValidate();
};
const showStatusSrt = computed(() => {
  let str = "";
  if (props.rowData?.auditState == 3) {
    props.rowData?.state == 1
      ? (str = "开户中")
      : props.rowData?.state == 2
      ? (str = "开户成功")
      : props.rowData?.state == 3
      ? (str = "开户失败")
      : props.rowData?.state == 4
      ? (str = "部分开户成功")
      : (str = "");
  } else {
    props.rowData?.auditState == 1
      ? (str = "待审核")
      : props.rowData?.auditState == 2
      ? (str = "审核中")
      : props.rowData?.auditState == 3
      ? (str = "审核通过")
      : props.rowData?.auditState == 4
      ? (str = "审核驳回")
      : (str = "");
  }
  return str;
});
onMounted(() => {
  if (props.type == "view" || props.type == "edit") {
    getReportDeatil();
  }
});
const addBodyHandler = () => {
  addBodyShow.value = true;
};
</script>

<style scoped lang="scss">
:deep(.el-dialog__headerbtn) {
  right: 24px !important;
}
:deep(.el-dialog__header) {
  padding-left: 24px;
  border-bottom: 1px solid #dcdee0;
  margin-bottom: 16px;
}
:deep(.el-form-item) {
  flex-direction: column;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
