<template>
  <div>
    <el-drawer
      v-model="props.visible"
      title="Yandex开户申请"
      size="600"
      :before-close="closeDialog"
    >
      <template #header>
        <h3>
          Yandex开户{{ props.type === "view" ? "详情" : "申请" }}
          <span v-if="props.type === 'view'" class="form-status">
            {{ showStatusSrt }}
          </span>
        </h3>
      </template>
      <div>
        <h4 class="form-header" v-if="props.type === 'view'">申请信息</h4>
        <el-form
          ref="formRef"
          style="max-width: 600px"
          :model="form"
          :rules="rules"
          label-width="150"
          label-position="left"
          :inline="false"
        >
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户主体"
                :prop="[YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
              >
                <el-select
                  v-model="form[YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
                  placeholder="请选择开户主体"
                  filterable
                  :disabled="type === 'view'"
                  @change="mainChagneHandler"
                >
                  <el-option
                    v-for="item in mainList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" class="pt-31">
              <el-button
                type="primary"
                @click="addBodyHandler"
                :disabled="type === 'view'"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="币种：">
                <el-text>USD</el-text>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="组织类型：" :prop="[YandexEnums.ORGTYPE]">
                <el-select
                  v-model="form[YandexEnums.ORGTYPE]"
                  placeholder="请选择组织类型"
                  filterable
                  :disabled="props.type === 'view'"
                >
                  <el-option
                    v-for="item in [
                      { label: 'Foreign legal(外国法人)', value: 1 },
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="统一社会信用代码"
                :prop="[YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
              >
                <el-input
                  v-model="form[YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
                  placeholder="输入统一社会信用代码"
                  :disabled="type === 'view'"
                  maxlength="8"
                  type="text"
                  @input="DailyBugetHandler"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户数量："
                :prop="[YandexEnums.OPEN_ACCOUNT_NUM]"
              >
                <el-input
                  v-model="form[YandexEnums.OPEN_ACCOUNT_NUM]"
                  placeholder="输入开户数量"
                  :disabled="type === 'view'"
                  maxlength="8"
                  type="text"
                  @input="DailyBugetHandler"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div
            v-for="(item, index) in form.yandexAccountList"
            :key="index"
            class="add-account-container"
          >
<!--            <el-row :gutter="10">-->
<!--              <el-col :span="20">-->
<!--                <el-form-item-->
<!--                  :prop="`yandexAccountList.${index}.${YandexEnums.ADVERTISING_ACCOUNT_NAME}`"-->
<!--                  :label="-->
<!--                    form.yandexAccountList?.length > 1-->
<!--                      ? '广告账户名称' + (index + 1)-->
<!--                      : '广告账户名称'-->
<!--                  "-->
<!--                  :rules="[-->
<!--                    {-->
<!--                      required: true,-->
<!--                      message: '请输入广告账户名称',-->
<!--                      trigger: 'blur',-->
<!--                    },-->
<!--                    {-->
<!--                      pattern: /^[a-zA-Z][a-zA-Z0-9]{0,28}$/,-->
<!--                      message:-->
<!--                        '必须以字母开头，以字母或数字结尾，不超过30个字符',-->
<!--                      trigger: 'change',-->
<!--                    },-->
<!--                  ]"-->
<!--                >-->
<!--                  <el-input-->
<!--                    v-model="item[YandexEnums.ADVERTISING_ACCOUNT_NAME]"-->
<!--                    placeholder="用于账号登录，仅支持字母数字，必须以字母开头"-->
<!--                    :disabled="type === 'view'"-->
<!--                    type="text"-->
<!--                    @input="DailyBugetHandler"-->
<!--                  />-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--            </el-row>-->
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="邮箱"
                  :prop="`yandexAccountList.${index}.${YandexEnums.INVITE_USERS}`"
                  :rules="[
                    {
                      required: true,
                      message: '请输入邮箱',
                      trigger: 'blur',
                    },
                    {
                      validator: validateEmailRule,
                      trigger: 'change',
                    },
                  ]"
                >
                  <el-select
                    v-model="item[YandexEnums.INVITE_USERS]"
                    validate-event
                    multiple
                    filterable
                    :allow-create="true"
                    default-first-option
                    :multiple-limit="1"
                    :reserve-keyword="false"
                    remote
                    placeholder="请输入邮箱"
                    :disabled="props.type === 'view'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-icon
              class="el-icon--delete"
              v-if="index > 0 && props.type !== 'view'"
            >
              <Delete @click="deleteRow(index)" />
            </el-icon>
          </div>
          <div
            v-if="form.yandexAccountList?.length < 5 && props.type !== 'view'"
            class="f jcc"
          >
            <el-button type="primary" class="mr10a" @click="addAccountName"
              >新增广告账户</el-button
            >
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            v-if="type !== 'view'"
            :loading="btnLoading"
            @click="submitHandler"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-drawer>
    <addBodyDialog
      v-model:visible="addBodyShow"
      v-if="addBodyShow"
      @close="closeBodyHandler"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  withDefaults,
  watch,
  onMounted,
  computed,
} from "vue";
import addBodyDialog from "../../accountOpenMain/component/addBodyDialog.vue";
import { Delete } from "@element-plus/icons-vue";
import { YandexEnums } from "../enums";
import { useAccountList } from "../useHooks";
import { ElMessage } from "element-plus";
import {
  addAndUpdateOpenAccountApi,
  getOpenReportDetailApi,
} from "@/api/openAccount";
import { auditStateMapping } from "@/utils/mapping";
import { validateEmail } from "@/utils";
const { mainList, timeZoneList, getMainList } = useAccountList();
const props = withDefaults(
  defineProps<{
    visible: boolean;
    type?: string;
    rowData?: any;
    openType: number;
  }>(),
  { type: "add" },
);
const closeBodyHandler = () => {
  getMainList();
};
const addBodyShow = ref(false);
const emit = defineEmits(["update:visible", "close"]);
const form = reactive({
  [YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  [YandexEnums.OPEN_ACCOUNT_NUM]: "",
  [YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]: "",
  [YandexEnums.ORGTYPE]: "",
  yandexAccountList: [
    {
      [YandexEnums.INVITE_USERS]: "",
      // 2025/06/26 Yandex 线上开户 删除广告账户名称字段
      // [YandexEnums.ADVERTISING_ACCOUNT_NAME]: "",
    },
  ],
  openType: "",
});
watch(
  [
    () => form[YandexEnums.BUSINESS_START_TIME],
    () => form[YandexEnums.BUSINESS_END_TIME],
  ],
  (newValue) => {
    if (!newValue.some((item) => !item)) {
      form[YandexEnums.BUSINESS_HOURS] = "1";
    } else {
      form[YandexEnums.BUSINESS_HOURS] = "";
    }
  },
  { deep: true },
);
const DailyBugetHandler = (value) => {
  form[YandexEnums.DAILY_BUDGET] = value.replace(/[^\d]/g, "");
};
const deleteRow = (index) => {
  form.yandexAccountList?.splice(index, 1);
};
const addAccountName = () => {
  if (form.yandexAccountList?.length == 5) return;
  form.yandexAccountList.push({
    // 2025/06/26 Yandex 线上开户 删除广告账户名称字段
    // [YandexEnums.ADVERTISING_ACCOUNT_NAME]: "",
    [YandexEnums.INVITE_USERS]: ""
  });
};
// 输入时强制数字
// const handlePhoneInput = (value) => {
//   form[YandexEnums.CONTACT_PHONE] = value.replace(/[^\d]/g, "");
// };
// 邮箱正则（兼容新顶级域名和国际化域名）
const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/;

// 自定义邮箱校验
const validateEmailRule = (rule, value, callback) => {
  console.log(rule, value, callback, "rule, value, callback---")
  if (!value) {
    callback(new Error("请输入邮箱!"));
  }

  // 多个邮箱校验
  if (value && Array.isArray(value)) {
    if (value.some((item) => !emailReg.test(item))) {
      callback(new Error("邮箱格式不正确!"));
    } else {
      callback();
    }
  } else {
    if (!emailReg.test(value)) {
      callback(new Error("邮箱格式不正确!"));
    } else {
      callback();
    }
  }
}

const rules = reactive({
  [YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [YandexEnums.ORGTYPE]: [
    { required: true, message: "请选择组织类型", trigger: "change" },
  ],
  [YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]: [
    { required: true, message: "请输入统一社会信用代码", trigger: "change" },
  ],
  [YandexEnums.OPEN_ACCOUNT_NUM]: [
    { required: true, message: "请输入开户数量", trigger: "change" },
  ],
});

const closeDialog = () => {
  emit("update:visible", false);
};
const customerOpenExtId = ref("");
// 参数整理
const handlerParams = () => {
  let customerMainName = mainList.value.find(
    (item) => item.value == form[YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED],
  )["label"];
  let data: any = {
    ...form,
    openType: props.openType,
    customerExtList: form.yandexAccountList.map((item: any) => {
      return {
        mediumType: 2,
        openType: props.openType,
        [YandexEnums.OPEN_ACCOUNT_NUM]: form[YandexEnums.OPEN_ACCOUNT_NUM],
        // 2025/06/26 Yandex 线上开户 删除广告账户名称字段
        // [YandexEnums.ADVERTISING_ACCOUNT_NAME]:
        //   item[YandexEnums.ADVERTISING_ACCOUNT_NAME],
        [YandexEnums.INVITE_USERS]: item[YandexEnums.INVITE_USERS]?.length
          ? item[YandexEnums.INVITE_USERS]?.join(",")
          : "",
        [YandexEnums.ORGTYPE]: form[YandexEnums.ORGTYPE],
        [YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]:
          form[YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE],
        [YandexEnums.CURRENCY]: "USD",
        customerOpenExtId: customerOpenExtId.value,
      };
    }),
    customerMainName,
  };
  delete data.orgType;
  delete data.unifiedSocialCreditCode;
  delete data.yandexAccountList;
  delete data.businessStartTime;
  delete data.businessEndTime;
  delete data.budgetDay;
  delete data.companyProfile;
  delete data.productAdvantage;
  delete data.businessHours;
  delete data[YandexEnums.CONTACT_EMAIL];
  delete data[YandexEnums.CONTACT_NAME];
  delete data[YandexEnums.CONTACT_PHONE];

  if (props.type == "edit") {
    data.applyId = props.rowData?.applyId;
  }
  return data;
};
let formRef = ref();
const btnLoading = ref(false);
const submitHandler = () => {
  formRef.value.validate().then((valid) => {
    if (valid) {
      let data = handlerParams();
      if (form[YandexEnums.OPEN_ACCOUNT_NUM] != data?.customerExtList?.length) {
        return ElMessage.error("开户数量和当前账户数量不符");
      }
      data?.customerExtList?.forEach((item) => {
        const invitationEmailList = item?.invitationEmail?.split(",") || [];
        if (
          invitationEmailList?.filter((item) => !validateEmail(item))?.length &&
          item?.invitationEmail
        ) {
          ElMessage.error("邀请用户邮箱格式不正确，请重新填写");
          throw new Error("邀请用户邮箱格式不正确，请重新填写");
        }
      });
      btnLoading.value = true;
      addAndUpdateOpenAccountApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            // ElMessage.success("提交成功");
            closeDialog();
            emit("close", { type: 2, applyId: res.data.applyIdList[0] });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const getReportDeatil = () => {
  getOpenReportDetailApi({
    applyId: props.rowData?.applyId,
  }).then((res: any) => {
    if (res && res.code && res.code == 200) {
      form[YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED] = res.data.customerInfoId;
      form[YandexEnums.PROMOTIONAL_LINK] = res.data.pushUrl;
      form[YandexEnums.ACCOUNT_TIME_ZONE] =
        res.data.customerOpenExtList[0].timeZone;
      form[YandexEnums.ORGTYPE] = res.data.customerOpenExtList[0].orgType;
      form[YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE] =
        res.data.customerOpenExtList[0].unifiedSocialCreditCode;
      form[YandexEnums.OPEN_ACCOUNT_NUM] = res.data.applyNo;
      form.yandexAccountList = res?.data?.customerOpenExtList?.map((item) => {
        return {
          accountName: item.accountName,
          invitationEmail: item.invitationEmail?.split(","),
        };
      });
      customerOpenExtId.value = res.data?.customerOpenExt?.customerOpenExtId;
      form.openType = res.data?.openType;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const mainChagneHandler = (value) => {
  let item = mainList.value.find((item) => item.value == value);
  form[YandexEnums.CONTACT_EMAIL] = item[YandexEnums.CONTACT_EMAIL];
  form[YandexEnums.CONTACT_NAME] = item[YandexEnums.CONTACT_NAME];
  form[YandexEnums.CONTACT_PHONE] = item[YandexEnums.CONTACT_PHONE];
  form[YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE] =
    item[YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE];
  formRef.value?.clearValidate();
};
const showStatusSrt = computed(() => {
  let str = "";
  if (props.rowData?.auditState == 3) {
    props.rowData?.state == 1
      ? (str = "开户中")
      : props.rowData?.state == 2
      ? (str = "开户成功")
      : props.rowData?.state == 3
      ? (str = "开户失败")
      : props.rowData?.state == 4
      ? (str = "部分开户成功")
      : (str = "");
  } else {
    props.rowData?.auditState == 1
      ? (str = "待审核")
      : props.rowData?.auditState == 2
      ? (str = "审核中")
      : props.rowData?.auditState == 3
      ? (str = "审核通过")
      : props.rowData?.auditState == 4
      ? (str = "审核驳回")
      : (str = "");
  }
  return str;
});
onMounted(() => {
  if (props.type == "view" || props.type == "edit") {
    getReportDeatil();
  }
});
const addBodyHandler = () => {
  addBodyShow.value = true;
};
</script>

<style scoped lang="scss">
:deep(.el-dialog__headerbtn) {
  right: 24px !important;
}
:deep(.el-dialog__header) {
  padding-left: 24px;
  border-bottom: 1px solid #dcdee0;
  margin-bottom: 16px;
}
.add-account-container {
  position: relative;
  .el-icon--delete {
    position: absolute;
    right: 30px;
    top: 0%;
  }
  .el-icon--delete:hover {
    color: #ff4d4f;
    cursor: pointer;
  }
}
:deep(.el-form-item) {
  flex-direction: column;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
