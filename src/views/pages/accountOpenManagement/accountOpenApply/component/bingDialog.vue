<template>
  <div>
    <el-drawer
      v-model="props.visible"
      title="Bing开户申请"
      size="600"
      :before-close="closeDialog"
    >
      <template #header>
        <h3>
          Bing开户{{ props.type === "view" ? "详情" : "申请" }}
          <span v-if="props.type === 'view'" class="form-status">
            {{ showStatusSrt }}
          </span>
        </h3>
      </template>
      <div>
        <h4 class="form-header" v-if="props.type === 'view'">申请信息</h4>
        <el-form
          ref="formRef"
          style="max-width: 600px"
          :model="form"
          :rules="rules"
          label-position="left"
          :inline="false"
          label-width="150"
        >
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户主体"
                :prop="[BingEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
              >
                <el-select
                  v-model="form[BingEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
                  placeholder="请选择开户主体"
                  :disabled="props.type === 'view'"
                  @change="mainChangeHandler"
                >
                  <el-option
                    v-for="item in mainList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="2" class="pt-31">
              <el-button
                type="primary"
                :disabled="props.type === 'view'"
                @click="addBodyHandler"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="注册地"
                :prop="[BingEnums.REGISTRATION_PLACE]"
              >
                <el-input
                  v-model="form[BingEnums.REGISTRATION_PLACE]"
                  placeholder="请输入注册地"
                  :disabled="props.type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="营业执照邮编"
                :prop="[BingEnums.BUSINESS_LICENSE_POSTAL_CODE]"
              >
                <el-input
                  v-model="form[BingEnums.BUSINESS_LICENSE_POSTAL_CODE]"
                  placeholder="请输入营业执照邮编"
                  :disabled="props.type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="电话" :prop="[BingEnums.PHONE]">
                <el-input
                  v-model="form[BingEnums.PHONE]"
                  placeholder="请输入电话"
                  :disabled="props.type === 'view'"
                  maxlength="11"
                  type="text"
                  @input="handlePhoneInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="账户时区"
                :prop="[BingEnums.ACCOUNT_TIME_ZONE]"
              >
                <el-select
                  v-model="form[BingEnums.ACCOUNT_TIME_ZONE]"
                  placeholder="请选择账户时区"
                  filterable
                  :disabled="props.type === 'view'"
                >
                  <el-option
                    v-for="item in timeZoneList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20"
              ><el-form-item
                label="一级行业类型"
                :prop="[BingEnums.INDUSTRY_TYPE]"
              >
                <el-select
                  v-model="form[BingEnums.INDUSTRY_TYPE]"
                  placeholder="请选择行业类型"
                  :disabled="props.type === 'view'"
                >
                  <el-option
                    v-for="item in industryList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="子行业" :prop="[BingEnums.SUB_INDUSTRY]">
                <el-input
                  v-model="form[BingEnums.SUB_INDUSTRY]"
                  placeholder="请输入子行业"
                  :disabled="props.type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="投放国家"
                :prop="[BingEnums.PROMOTION_COUNTRY]"
              >
                <el-input
                  v-model="form[BingEnums.PROMOTION_COUNTRY]"
                  placeholder="请选择投放国家"
                  :disabled="props.type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="推广链接"
                :prop="[BingEnums.PROMOTIONAL_LINK]"
              >
                <el-input
                  v-model="form[BingEnums.PROMOTIONAL_LINK]"
                  placeholder="请输入推广链接"
                  :disabled="props.type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            @click="submitHandler"
            :loading="btnLoading"
            v-if="type !== 'view'"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-drawer>
    <addBodyDialog
      v-if="addBodyShow"
      v-model:visible="addBodyShow"
      @close="closeBodyHandler"
    />
    <openTips ref="openTipsRef" />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  withDefaults,
  onMounted,
  computed,
} from "vue";
import addBodyDialog from "../../accountOpenMain/component/addBodyDialog.vue";
import { BingEnums } from "../enums";
import { useAccountList } from "../useHooks";
import { auditStateMapping } from "@/utils/mapping";
import {
  addAndUpdateOpenAccountApi,
  getOpenReportDetailApi,
} from "@/api/openAccount";
import openTips from "@/components/openSuccess/index.vue";
import { ElMessage } from "element-plus";
const { mainList, timeZoneList, getMainList } = useAccountList();
const openTipsRef = ref();
const closeBodyHandler = () => {
  getMainList();
};
const props = withDefaults(
  defineProps<{
    visible: boolean;
    type?: string;
    rowData?: any;
    openType?: number;
  }>(),
  { type: "add" },
);
const handlePhoneInput = (value) => {
  form[BingEnums.PHONE] = value.replace(/[^\d]/g, "");
};
const mainChangeHandler = (value) => {
  let item = mainList.value.find((item) => item.value == value);
  form[BingEnums.REGISTRATION_PLACE] = item.address;
  form[BingEnums.PHONE] = item.contactsPhone;
  formRef.value?.clearValidate();
};
const industryList = [
  {
    label: "EC",
    value: 1001,
  },
  {
    label: "Game",
    value: 1002,
  },
  {
    label: "App",
    value: 1003,
  },
  {
    label: "Fin",
    value: 1004,
  },
  {
    label: "BRANDING",
    value: 1005,
  },
];
const addBodyShow = ref(false);
const emit = defineEmits(["update:visible", "close"]);
const form = reactive({
  [BingEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  [BingEnums.INDUSTRY_TYPE]: "",
  [BingEnums.PROMOTION_COUNTRY]: "",
  [BingEnums.ACCOUNT_TIME_ZONE]: "",
  [BingEnums.PROMOTIONAL_LINK]: "",
  [BingEnums.BUSINESS_LICENSE_POSTAL_CODE]: "",
  [BingEnums.PHONE]: "",
  [BingEnums.SUB_INDUSTRY]: "",
  [BingEnums.REGISTRATION_PLACE]: "",
  openType: "",
});
const rules = reactive({
  [BingEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [BingEnums.INDUSTRY_TYPE]: [
    { required: true, message: "请选择一级行业类型", trigger: "change" },
  ],
  [BingEnums.PROMOTION_COUNTRY]: [
    { required: true, message: "请输入投放国家", trigger: "blur" },
  ],
  [BingEnums.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择账户时区", trigger: "change" },
  ],
  [BingEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "blur" },
  ],
  [BingEnums.PHONE]: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
  ],
  [BingEnums.SUB_INDUSTRY]: [
    { required: true, message: "请输入子行业", trigger: "blur" },
  ],
  [BingEnums.BUSINESS_LICENSE_POSTAL_CODE]: [
    { required: true, message: "请输入营业执照邮编", trigger: "blur" },
  ],
  [BingEnums.REGISTRATION_PLACE]: [
    { required: true, message: "请输入营业执照注册地", trigger: "blur" },
  ],
});
let customerOpenExtId = ref("");
const handlerParams = () => {
  let customerMainName = mainList.value.find(
    (item) => item.value == form[BingEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED],
  )["label"];
  let data: any = {
    ...form,
    openType: props.openType,
    applyNo: 1, // 默认值
    customerExtList: [
      {
        mediumType: 5,
        openType: props.openType,
        applyNo: 1, // 默认值
        [BingEnums.PROMOTION_COUNTRY]: form[BingEnums.PROMOTION_COUNTRY],
        [BingEnums.REGISTRATION_PLACE]: form[BingEnums.REGISTRATION_PLACE],
        [BingEnums.SUB_INDUSTRY]: form[BingEnums.SUB_INDUSTRY],
        [BingEnums.BUSINESS_LICENSE_POSTAL_CODE]:
          form[BingEnums.BUSINESS_LICENSE_POSTAL_CODE],
        [BingEnums.PHONE]: form[BingEnums.PHONE],
        [BingEnums.INDUSTRY_TYPE]: form[BingEnums.INDUSTRY_TYPE],
        [BingEnums.ACCOUNT_TIME_ZONE]: form[BingEnums.ACCOUNT_TIME_ZONE],
        [BingEnums.PROMOTIONAL_LINK]: form[BingEnums.PROMOTIONAL_LINK],
        customerOpenExtId: customerOpenExtId.value,
      },
    ],
    customerMainName,
  };
  delete data[BingEnums.PROMOTIONAL_LINK];
  delete data[BingEnums.ACCOUNT_TIME_ZONE];
  delete data[BingEnums.PROMOTION_COUNTRY];
  delete data[BingEnums.REGISTRATION_PLACE];
  delete data[BingEnums.SUB_INDUSTRY];
  delete data[BingEnums.BUSINESS_LICENSE_POSTAL_CODE];
  delete data[BingEnums.PHONE];
  delete data[BingEnums.INDUSTRY_TYPE];
  if (props.type == "edit") {
    data.applyId = props.rowData?.applyId;
  }
  return data;
};
const formRef = ref();
const btnLoading = ref(false);
const submitHandler = () => {
  formRef.value?.validate().then((valid) => {
    if (valid) {
      let data = handlerParams();
      btnLoading.value = true;
      addAndUpdateOpenAccountApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            // ElMessage.success("提交成功");
            closeDialog();
            emit("close", { type: 5, applyId: res.data.applyIdList[0] });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const addBodyHandler = () => {
  addBodyShow.value = true;
};
const closeDialog = () => {
  emit("update:visible", false);
};
const getReportDeatil = () => {
  getOpenReportDetailApi({
    applyId: props.rowData?.applyId,
  }).then((res: any) => {
    if (res && res.code && res.code == 200) {
      form[BingEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED] = res.data.customerInfoId;
      form[BingEnums.PROMOTIONAL_LINK] =
        res.data.customerOpenExtList[0]?.pushUrl;
      form[BingEnums.ACCOUNT_TIME_ZONE] =
        res.data.customerOpenExtList[0]?.timeZone;
      form[BingEnums.PROMOTION_COUNTRY] =
        res.data?.customerOpenExt?.pushCountry;
      form[BingEnums.REGISTRATION_PLACE] =
        res.data?.customerOpenExt?.registeredAddress;
      form[BingEnums.SUB_INDUSTRY] = res.data?.customerOpenExt?.subIndustry;
      form[BingEnums.BUSINESS_LICENSE_POSTAL_CODE] =
        res.data?.customerOpenExt?.postCode;
      form[BingEnums.PHONE] = res.data?.customerOpenExt?.hqPhone;
      form[BingEnums.INDUSTRY_TYPE] = res.data?.customerOpenExt?.industryType;
      customerOpenExtId.value = res.data?.customerOpenExt?.customerOpenExtId;
      form.openType = res.data?.openType;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const showStatusSrt = computed(() => {
  let str = "";
  if (props.rowData?.auditState == 3) {
    props.rowData?.state == 1
      ? (str = "开户中")
      : props.rowData?.state == 2
      ? (str = "开户成功")
      : props.rowData?.state == 3
      ? (str = "开户失败")
      : props.rowData?.state == 4
      ? (str = "部分开户成功")
      : (str = "");
  } else {
    props.rowData?.auditState == 1
      ? (str = "待审核")
      : props.rowData?.auditState == 2
      ? (str = "审核中")
      : props.rowData?.auditState == 3
      ? (str = "审核通过")
      : props.rowData?.auditState == 4
      ? (str = "审核驳回")
      : (str = "");
  }
  return str;
});
onMounted(() => {
  if (props.type == "view" || props.type == "edit") {
    getReportDeatil();
  }
});
</script>

<style scoped lang="scss">
:deep(.el-dialog__headerbtn) {
  right: 24px !important;
}
:deep(.el-dialog__header) {
  padding-left: 24px;
  border-bottom: 1px solid #dcdee0;
  margin-bottom: 16px;
}
:deep(.el-form-item) {
  flex-direction: column;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
