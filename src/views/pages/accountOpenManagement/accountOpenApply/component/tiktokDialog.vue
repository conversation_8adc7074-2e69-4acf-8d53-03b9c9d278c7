<template>
  <div>
    <el-drawer
      v-model="props.visible"
      title="TikTok开户申请"
      size="600"
      :before-close="closeDialog"
    >
      <template #header>
        <h3>
          TikTok开户{{ props.type === "view" ? "详情" : "申请" }}
          <span v-if="props.type === 'view'" class="form-status">
            {{ showStatusSrt }}
          </span>
        </h3>
      </template>
      <div>
        <h4 class="form-header" v-if="props.type === 'view'">申请信息</h4>
        <el-form
          ref="formRef"
          style="max-width: 600px"
          :model="form"
          :rules="rules"
          label-width="150"
          label-position="left"
          :inline="false"
        >
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户主体"
                :prop="[TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
              >
                <el-select
                  v-model="form[TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
                  placeholder="请选择开户主体公司"
                  filterable
                  :disabled="type === 'view'"
                  @change="mainChangeHandler"
                >
                  <el-option
                    v-for="item in mainList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" class="pt-31">
              <el-button
                type="primary"
                @click="addBodyHandler"
                :disabled="type === 'view'"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20"
              ><el-form-item
                label="行业类型"
                :prop="[TiktokEnums.INDUSTRY_TYPE]"
              >
                <el-select
                  v-model="form[TiktokEnums.INDUSTRY_TYPE]"
                  placeholder="请选择行业类型"
                  filterable
                  :disabled="type === 'view'"
                >
                  <el-option
                    v-for="item in industryTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="账户时区"
                :prop="[TiktokEnums.ACCOUNT_TIME_ZONE]"
              >
                <el-select
                  v-model="form[TiktokEnums.ACCOUNT_TIME_ZONE]"
                  placeholder="请选择账户时区"
                  filterable
                  :disabled="type === 'view'"
                >
                  <el-option
                    v-for="item in timeZoneList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="币种"> USD </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="统一社会信用代码"
                :prop="[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
              >
                <el-input
                  v-model="form[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]"
                  placeholder="请输入统一社会信用代码"
                  :disabled="type === 'view'"
                  maxlength="18"
                  type="text"
                  @input="handlerCodeInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="注册地"
                :prop="[TiktokEnums.REGISTRATION_LOCATION]"
              >
                <el-input
                  v-model="form[TiktokEnums.REGISTRATION_LOCATION]"
                  placeholder="请输入注册地"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="广告账户类型"
                :prop="[TiktokEnums.ACCOUNT_TYPE]"
              >
                <el-select
                  v-model="form[TiktokEnums.ACCOUNT_TYPE]"
                  placeholder="请选择广告账户类型"
                  filterable
                  :disabled="type === 'view'"
                >
                  <el-option
                    v-for="item in [
                      { value: 1, label: '竞价' },
                      { value: 2, label: '品牌' },
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户数量"
                :prop="[TiktokEnums.ACCOUNT_NUMBER]"
              >
                <el-input
                  v-model="form[TiktokEnums.ACCOUNT_NUMBER]"
                  placeholder="请输入开户数量，一次最多支持开通5个"
                  :disabled="type === 'view'"
                  type="number"
                  @input="handlerNumberInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="投放国家"
                :prop="[TiktokEnums.PROMOTION_COUNTRY]"
              >
                <el-input
                  v-model="form[TiktokEnums.PROMOTION_COUNTRY]"
                  placeholder="请输入投放国家，可填写多个，如中国；美国"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div
            v-for="(item, index) in form.accountList"
            :key="index"
            class="add-account-container"
          >
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  :label="
                    form.accountList?.length > 1
                      ? '广告账户' + (index + 1)
                      : '广告账户'
                  "
                >
                  <el-text v-if="props.type === 'view'">
                    {{
                      item[TiktokEnums.PRODUCT_ADVANTAGE] +
                      "-" +
                      form[TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED_NAME]
                    }}
                  </el-text>
                  <el-text v-else>
                    账户名称根据产品名称+企业名称自动生成
                  </el-text>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="产品名称"
                  :prop="`accountList.${index}.${TiktokEnums.PRODUCT_ADVANTAGE}`"
                  :rules="[
                    {
                      required: true,
                      message: '请输入投放的核心出海产品名称，最多20字符',
                      trigger: ['blur', 'change'],
                    },
                    {
                      min: 1,
                      max: 20,
                      message: '请输入投放的核心出海产品名称，最多20字符',
                      trigger: ['blur', 'change'],
                    },
                  ]"
                >
                  <el-input
                    v-model="item[TiktokEnums.PRODUCT_ADVANTAGE]"
                    placeholder="请输入投放的核心出海产品名称，最多20字符"
                    :disabled="props.type === 'view'"
                    maxlength="20"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="企业简称"
                  :prop="`accountList.${index}.${TiktokEnums.COMPANY_PROFILE}`"
                  :rules="[
                    {
                      required: true,
                      message: '请输入企业简称，最多20字符',
                      trigger: ['blur', 'change'],
                    },
                    {
                      min: 1,
                      max: 20,
                      message: '请输入企业简称，最多20字符',
                      trigger: ['blur', 'change'],
                    },
                  ]"
                >
                  <el-input
                    v-model="item[TiktokEnums.COMPANY_PROFILE]"
                    placeholder="请输入企业简称，最多20字符"
                    :disabled="type === 'view'"
                    maxlength="20"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="推广链接"
                  :prop="`accountList.${index}.${TiktokEnums.PROMOTIONAL_LINK}`"
                  :rules="[
                    {
                      required: true,
                      message: '请输入推广链接，多个时以英文中;分隔',
                      trigger: ['blur', 'change'],
                    },
                  ]"
                >
                  <el-input
                    v-model="item[TiktokEnums.PROMOTIONAL_LINK]"
                    placeholder="请输入推广链接，多个时以英文中;分隔"
                    :disabled="type === 'view'"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-icon
              class="el-icon--delete"
              v-if="index > 0 && type !== 'view'"
            >
              <Delete @click="deleteRow(index)" />
            </el-icon>
          </div>
          <div
            v-if="form.accountList?.length < 5 && type !== 'view'"
            class="f jcc"
          >
            <el-button type="primary" class="mr10a" @click="addAccountName"
              >新增广告账户</el-button
            >
          </div>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="法人姓名"
                :prop="[TiktokEnums.LEGAL_PERSON_NAME]"
                :rules="[
                  {
                    required: true,
                    message: '请输入法人姓名',
                    trigger: ['blur', 'change'],
                  },
                ]"
              >
                <el-input
                  v-model="form[TiktokEnums.LEGAL_PERSON_NAME]"
                  placeholder="请输入法人姓名"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="法人身份证号"
                :prop="[TiktokEnums.LEGAL_PERSON_ID_CARD]"
                :rules="[
                  {
                    required: true,
                    message: '请输入法人身份证号',
                    trigger: ['blur', 'change'],
                  },
                ]"
              >
                <el-input
                  v-model="form[TiktokEnums.LEGAL_PERSON_ID_CARD]"
                  placeholder="请输入法人身份证号"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                  @input="handlerIdCardInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="法人银行卡号"
                :prop="[TiktokEnums.LEGAL_PERSON_BANK_CARD]"
                :rules="[
                  {
                    required: true,
                    message: '请输入法人银行卡号',
                    trigger: ['blur', 'change'],
                  },
                ]"
              >
                <el-input
                  v-model="form[TiktokEnums.LEGAL_PERSON_BANK_CARD]"
                  placeholder="请输入法人银行卡号"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="法人手机号"
                :prop="[TiktokEnums.LEGAL_PERSON_PHONE]"
                :rules="[
                  {
                    required: true,
                    message: '请输入法人手机号',
                    trigger: ['blur', 'change'],
                  },
                ]"
              >
                <el-input
                  v-model="form[TiktokEnums.LEGAL_PERSON_PHONE]"
                  placeholder="请输入法人手机号"
                  :disabled="type === 'view'"
                  maxlength="11"
                  type="text"
                  @input="handlePhoneInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            @click="submitHandler"
            :loading="btnLoading"
            v-if="type !== 'view'"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-drawer>
    <addBodyDialog
      v-model:visible="addBodyShow"
      v-if="addBodyShow"
      @close="closeBodyHandler"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  withDefaults,
  onMounted,
  computed,
} from "vue";
import addBodyDialog from "../../accountOpenMain/component/addBodyDialog.vue";
import { Delete } from "@element-plus/icons-vue";
import { TiktokEnums } from "../enums";
import { useAccountList, useIndustryList } from "../useHooks";
import {
  addAndUpdateOpenAccountApi,
  getOpenReportDetailApi,
} from "@/api/openAccount";
import { ElMessage } from "element-plus";
import { cityList } from "@/utils/mapping";
const { mainList, timeZoneList, getMainList } = useAccountList();
const { industryTypeList } = useIndustryList(4);
const props = withDefaults(
  defineProps<{
    visible: boolean;
    type?: string;
    rowData?: any;
    openType: number;
  }>(),
  { type: "add" },
);
const addAccountItem = {
  [TiktokEnums.PRODUCT_ADVANTAGE]: "",
  [TiktokEnums.PROMOTIONAL_LINK]: "",
  [TiktokEnums.COMPANY_PROFILE]: "",
};
// 输入时强制数字
const handlePhoneInput = (value) => {
  form[TiktokEnums.LEGAL_PERSON_PHONE] = value.replace(/[^\d]/g, "");
};
const handlerNumberInput = (value) => {
  let filtered = value.replace(/[^1-5]/g, "");
  // 若输入超过5，自动修正为最大值5
  if (filtered > 5) filtered = 5;
  form[TiktokEnums.ACCOUNT_NUMBER] = filtered;
};
// 身份证校验规则
const validateIdCard = (rule, value, callback) => {
  if (value) {
    // 基本格式验证
    const reg =
      /(^[1-9]\d{5}(18|19|([23]\d))\d{2}(0[1-9]|1[0-2])([0-2][1-9]|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}(0[1-9]|1[0-2])([0-2][1-9]|10|20|30|31)\d{2}[0-9Xx]$)/;

    if (!reg.test(value)) {
      return callback(new Error("身份证格式错误"));
    }

    // 地区码校验
    const cityCode = value.substring(0, 2);

    if (!cityList[cityCode]) {
      return callback(new Error("地区码无效"));
    }

    // 18位身份证校验位验证
    if (value.length === 18) {
      const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const parity = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
      let sum = 0;

      for (let i = 0; i < 17; i++) {
        sum += value[i] * factor[i];
      }

      const lastChar = value[17].toUpperCase();
      if (lastChar !== parity[sum % 11]) {
        return callback(new Error("校验码错误"));
      }
    }
    callback();
  } else {
    callback();
  }
};
const handlerCodeInput = (value) => {
  form[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE] = value.replace(
    /[^a-zA-Z0-9]/g,
    "",
  );
};
const handlerIdCardInput = (value) => {
  form[TiktokEnums.LEGAL_PERSON_ID_CARD] = value
    .toUpperCase()
    .replace(/[^0-9X]/g, "");
};
const emit = defineEmits(["update:visible", "close"]);
let addBodyShow = ref(false);
const closeBodyHandler = () => {
  getMainList();
};
const form = reactive({
  [TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED_NAME]: "",
  [TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  [TiktokEnums.REGISTRATION_LOCATION]: "",
  [TiktokEnums.ACCOUNT_TYPE]: "",
  [TiktokEnums.INDUSTRY_TYPE]: "",
  [TiktokEnums.LEGAL_PERSON_PHONE]: "",
  [TiktokEnums.ACCOUNT_TIME_ZONE]: "",
  [TiktokEnums.LEGAL_PERSON_NAME]: "",
  [TiktokEnums.PROMOTIONAL_LINK]: "",
  [TiktokEnums.LEGAL_PERSON_ID_CARD]: "",
  [TiktokEnums.LEGAL_PERSON_BANK_CARD]: "",
  [TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]: "",
  [TiktokEnums.ACCOUNT_NUMBER]: "",
  [TiktokEnums.ADVERTISING_ACCOUNT_NAME]: [],
  [TiktokEnums.COMPANY_PROFILE]: "",
  accountList: [addAccountItem],
  openType: "",
});
const rules = reactive({
  [TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [TiktokEnums.REGISTRATION_LOCATION]: [
    { required: true, message: "请输入注册地", trigger: "blur" },
  ],
  [TiktokEnums.INDUSTRY_TYPE]: [
    { required: true, message: "请选择行业类型", trigger: "change" },
  ],
  [TiktokEnums.ACCOUNT_TYPE]: [
    { required: true, message: "请输入账户类型", trigger: "change" },
  ],
  [TiktokEnums.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择账户时区", trigger: "change" },
  ],
  [TiktokEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "blur" },
  ],
  [TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]: [
    { required: true, message: "请输入统一社会信用代码", trigger: "blur" },
  ],
  [TiktokEnums.ACCOUNT_NUMBER]: [
    { required: true, message: "请输入账户数量", trigger: "blur" },
  ],
  [TiktokEnums.ADVERTISING_ACCOUNT_NAME]: [
    { required: true, message: "请输入广告账户名称", trigger: "change" },
  ],
  [TiktokEnums.LEGAL_PERSON_ID_CARD]: [
    { validator: validateIdCard, trigger: "blur" },
  ],
  [TiktokEnums.COMPANY_PROFILE]: [
    {
      required: true,
      message: "请输入企业简称，最多20字符",
      trigger: "change",
    },
  ],
  [TiktokEnums.LEGAL_PERSON_PHONE]: [
    {
      pattern: /^1[3-9]\d{9}$/, // 最新号段正则（支持13-19开头）
      message: "请输入正确的手机号",
      trigger: ["blur", "change"],
    },
  ],
  [TiktokEnums.PROMOTION_COUNTRY]: [
    { required: true, message: "请输入投放国家", trigger: "blur" },
  ],
});
const formRef = ref();
const addAccountName = () => {
  if (form.accountList?.length == 5) return;
  form.accountList.push({
    [TiktokEnums.PRODUCT_ADVANTAGE]: "",
    [TiktokEnums.PROMOTIONAL_LINK]: "",
    [TiktokEnums.COMPANY_PROFILE]: "",
  });
};
const deleteRow = (index) => {
  form.accountList?.splice(index, 1);
};
const customerOpenExtId = ref("");
const handlerParams = () => {
  let customerMainName = mainList.value.find(
    (item) => item.value == form[TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED],
  )["label"];
  let data: any = {
    ...form,
    openType: props.openType,
    applyNo: form.applyNo,
    customerExtList: form.accountList?.map((item: any) => {
      return {
        mediumType: 4,
        openType: props.openType,
        [TiktokEnums.ACCOUNT_NUMBER]: form[TiktokEnums.ACCOUNT_NUMBER],
        [TiktokEnums.ADVERTISING_ACCOUNT_NAME]: `${
          item[TiktokEnums.PRODUCT_ADVANTAGE]
        }-${customerMainName}`, // 账户
        [TiktokEnums.CURRENCY]: form[TiktokEnums.CURRENCY], //币种
        [TiktokEnums.PROMOTIONAL_LINK]: item[TiktokEnums.PROMOTIONAL_LINK], // 推广链接
        [TiktokEnums.COMPANY_PROFILE]: item[TiktokEnums.COMPANY_PROFILE], // 推广链接
        [TiktokEnums.ACCOUNT_TIME_ZONE]: form[TiktokEnums.ACCOUNT_TIME_ZONE], //  时区
        [TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]:
          form[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE],
        [TiktokEnums.REGISTRATION_LOCATION]:
          form[TiktokEnums.REGISTRATION_LOCATION],
        [TiktokEnums.INDUSTRY_TYPE]: form[TiktokEnums.INDUSTRY_TYPE],
        [TiktokEnums.ACCOUNT_TYPE]: form[TiktokEnums.ACCOUNT_TYPE],
        [TiktokEnums.PRODUCT_ADVANTAGE]: item[TiktokEnums.PRODUCT_ADVANTAGE],
        [TiktokEnums.PROMOTION_COUNTRY]: form[TiktokEnums.PROMOTION_COUNTRY],
        customerOpenExtId: customerOpenExtId.value,
        [TiktokEnums.LEGAL_PERSON_NAME]: form[TiktokEnums.LEGAL_PERSON_NAME],
        [TiktokEnums.LEGAL_PERSON_ID_CARD]:
          form[TiktokEnums.LEGAL_PERSON_ID_CARD],
        [TiktokEnums.LEGAL_PERSON_BANK_CARD]:
          form[TiktokEnums.LEGAL_PERSON_BANK_CARD],
        [TiktokEnums.LEGAL_PERSON_PHONE]: form[TiktokEnums.LEGAL_PERSON_PHONE],
      };
    }),
    customerMainName,
  };
  delete data[TiktokEnums.PROMOTIONAL_LINK];
  delete data[TiktokEnums.ACCOUNT_TIME_ZONE];
  delete data["accountList"];
  delete data[TiktokEnums.ACCOUNT_TYPE];
  delete data[TiktokEnums.REGISTRATION_LOCATION];
  delete data[TiktokEnums.ADVERTISING_ACCOUNT_NAME];
  delete data[TiktokEnums.INDUSTRY_TYPE];
  delete data[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE];
  delete data[TiktokEnums.LEGAL_PERSON_NAME];
  delete data[TiktokEnums.LEGAL_PERSON_PHONE];
  delete data[TiktokEnums.LEGAL_PERSON_ID_CARD];
  delete data[TiktokEnums.LEGAL_PERSON_BANK_CARD];
  if (props.type == "edit") {
    data.applyId = props.rowData?.applyId;
  }
  return data;
};
const btnLoading = ref(false);
const submitHandler = () => {
  formRef.value?.validate().then((valid) => {
    console.log(valid);
    if (valid) {
      let data: any = handlerParams();
      if (form[TiktokEnums.OPEN_ACCOUNT_NUM] != data?.customerExtList?.length) {
        return ElMessage.error("开户数量和当前账户数量不符");
      }
      btnLoading.value = true;
      addAndUpdateOpenAccountApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            // ElMessage.success("提交成功");
            closeDialog();
            emit("close", { type: 4, applyId: res.data.applyIdList[0] });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const closeDialog = () => {
  emit("update:visible", false);
};

const addBodyHandler = () => {
  addBodyShow.value = true;
};
const getReportDeatil = () => {
  getOpenReportDetailApi({
    applyId: props.rowData?.applyId,
  }).then((res: any) => {
    if (res && res.code == 200) {
      form[TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED_NAME] =
        res.data.customerMainName;
      form[TiktokEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED] = res.data.customerInfoId;
      form[TiktokEnums.ACCOUNT_TIME_ZONE] =
        res.data.customerOpenExtList[0]?.timeZone;
      form[TiktokEnums.REGISTRATION_LOCATION] =
        res.data?.customerOpenExt?.registeredAddress;
      form[TiktokEnums.ACCOUNT_TYPE] =
        res.data?.customerOpenExtList[0]?.accountType;
      form[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE] =
        res.data?.customerOpenExt?.unifiedSocialCreditCode;
      form[TiktokEnums.INDUSTRY_TYPE] =
        res.data?.customerOpenExtList[0]?.industryType;
      form[TiktokEnums.LEGAL_PERSON_NAME] =
        res.data?.customerOpenExtList[0]?.legalName;
      form[TiktokEnums.LEGAL_PERSON_PHONE] =
        res.data?.customerOpenExt?.legalPhone;
      form[TiktokEnums.LEGAL_PERSON_ID_CARD] =
        res.data?.customerOpenExt?.legalCard;
      form[TiktokEnums.LEGAL_PERSON_BANK_CARD] =
        res.data?.customerOpenExt?.legalBank;
      form[TiktokEnums.ACCOUNT_NUMBER] = res.data?.applyNo;
      form.accountList = res?.data?.customerOpenExtList?.map((item) => {
        return {
          accountName: item.accountName,
          productAdvantage: item.productAdvantage,
          companyProfile: item.companyProfile,
          pushUrl: item.pushUrl,
        };
      });
      customerOpenExtId.value = res.data?.customerOpenExt?.customerOpenExtId;
      form.openType = res.data?.openType;
      form[TiktokEnums.PROMOTION_COUNTRY] =
        res.data?.customerOpenExt[TiktokEnums.PROMOTION_COUNTRY];
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const showStatusSrt = computed(() => {
  let str = "";
  if (props.rowData?.auditState == 3) {
    props.rowData?.state == 1
      ? (str = "开户中")
      : props.rowData?.state == 2
      ? (str = "开户成功")
      : props.rowData?.state == 3
      ? (str = "开户失败")
      : props.rowData?.state == 4
      ? (str = "部分开户成功")
      : (str = "");
  } else {
    props.rowData?.auditState == 1
      ? (str = "待审核")
      : props.rowData?.auditState == 2
      ? (str = "审核中")
      : props.rowData?.auditState == 3
      ? (str = "审核通过")
      : props.rowData?.auditState == 4
      ? (str = "审核驳回")
      : (str = "");
  }
  return str;
});
onMounted(() => {
  if (props.type == "view" || props.type == "edit") {
    getReportDeatil();
  }
});
const mainChangeHandler = (value) => {
  let item = mainList.value.find((item) => item.value == value);
  form[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE] =
    item[TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE];
  form[TiktokEnums.REGISTRATION_LOCATION] = item?.address;
  formRef.value?.clearValidate();
};
</script>

<style scoped lang="scss">
:deep(.el-dialog__headerbtn) {
  right: 24px !important;
}
:deep(.el-dialog__header) {
  padding-left: 24px;
  border-bottom: 1px solid #dcdee0;
  margin-bottom: 16px;
}
.add-account-container {
  position: relative;
  .el-icon--delete {
    position: absolute;
    right: 30px;
    top: 0%;
  }
  .el-icon--delete:hover {
    color: #ff4d4f;
    cursor: pointer;
  }
}
:deep(.el-form-item) {
  flex-direction: column;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
