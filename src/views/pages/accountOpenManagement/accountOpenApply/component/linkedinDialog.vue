<template>
  <div>
    <el-drawer
      v-model="props.visible"
      title="LinkedIn开户申请"
      size="650"
      :before-close="closeDialog"
    >
      <template #header>
        <h3>
          LinkedIn开户{{ props.type === "view" ? "详情" : "申请" }}
          <span v-if="props.type === 'view'" class="form-status">
            {{ showStatusSrt }}
          </span>
        </h3>
      </template>
      <div>
        <h4 class="form-header" v-if="props.type === 'view'">申请信息</h4>
        <el-form
          ref="formRef"
          style="max-width: 600px"
          :model="form"
          :rules="rules"
          label-position="left"
          :inline="false"
          label-width="200"
        >
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户主体"
                :prop="[LinkedinEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
              >
                <el-select
                  v-model="form[LinkedinEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
                  placeholder="请选择开户主体"
                  filterable
                  :disabled="type === 'view'"
                >
                  <el-option
                    v-for="item in mainList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" class="pt-31">
              <el-button
                type="primary"
                @click="addBodyHandler"
                :disabled="type === 'view'"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="账户时区"
                :prop="[LinkedinEnums.ACCOUNT_TIMEZONE]"
              >
                <el-select
                  v-model="form[LinkedinEnums.ACCOUNT_TIMEZONE]"
                  placeholder="请选择账户时区"
                  :disabled="type === 'view'"
                  filterable
                >
                  <el-option
                    v-for="item in timeZoneList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="推广链接"
                :prop="[LinkedinEnums.PROMOTIONAL_LINK]"
              >
                <el-input
                  v-model="form[LinkedinEnums.PROMOTIONAL_LINK]"
                  :disabled="type === 'view'"
                  placeholder="请输入推广链接"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="公司名称（英文）"
                :prop="[LinkedinEnums.COMPANY_NAME_E]"
              >
                <el-input
                  v-model="form[LinkedinEnums.COMPANY_NAME_E]"
                  placeholder="请输入公司名称（英文）"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="公司地址（英文）"
                :prop="[LinkedinEnums.COMPANY_ADDRESS_E]"
              >
                <el-input
                  v-model="form[LinkedinEnums.COMPANY_ADDRESS_E]"
                  placeholder="请输入公司地址（英文）"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="HQ phone 总部电话"
                :prop="[LinkedinEnums.HQPHONE]"
              >
                <el-input
                  v-model="form[LinkedinEnums.HQPHONE]"
                  placeholder="请输入HQ phone 总部电话"
                  :disabled="type === 'view'"
                  @input="handlePhoneInput"
                  maxlength="11"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20"
              ><el-form-item
                label="Official Website 官方链接"
                :prop="[LinkedinEnums.OFFICIAL_WEBSITE]"
              >
                <el-input
                  v-model="form[LinkedinEnums.OFFICIAL_WEBSITE]"
                  placeholder="请输入Official Website 官方链接"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="充值金额"
                :prop="[LinkedinEnums.RECHARGE_AMOUNT]"
              >
                <el-input
                  v-model="form[LinkedinEnums.RECHARGE_AMOUNT]"
                  placeholder="请输入充值金额"
                  :disabled="type === 'view'"
                  maxlength="8"
                  type="text"
                  @input="rechargeAmountHandler"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="首次上线时间"
                :prop="[LinkedinEnums.FIRST_ONLINE_TIME]"
              >
                <el-date-picker
                  v-model="form[LinkedinEnums.FIRST_ONLINE_TIME]"
                  type="date"
                  placeholder="首次上线时间"
                  value-format="YYYY-MM-DD"
                  :disabled="type === 'view'"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="推广时间"
                :prop="[LinkedinEnums.PROMOTION_TIME]"
              >
                <el-input
                  v-model="form[LinkedinEnums.PROMOTION_TIME]"
                  placeholder="请填写时间段，如3个月或12月"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="number"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="LinkedIn Company Page"
                :prop="[LinkedinEnums.LINKEDIN_COMPANY_PAGE]"
              >
                <el-input
                  v-model="form[LinkedinEnums.LINKEDIN_COMPANY_PAGE]"
                  placeholder="请输入LinkedIn Company Page（Ad account admit）"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="个人领英界面链接"
                :prop="[LinkedinEnums.PERSONAL_LINKEDIN_PAGE]"
              >
                <el-input
                  v-model="form[LinkedinEnums.PERSONAL_LINKEDIN_PAGE]"
                  placeholder="申请广告账户管理权限的个人领英界面链接"
                  :disabled="type === 'view'"
                  maxlength="100"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            @click="submitHandler"
            :loading="btnLoading"
            v-if="type !== 'view'"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-drawer>
    <addBodyDialog
      v-model:visible="addBodyShow"
      v-if="addBodyShow"
      @close="closeBodyHandler"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  withDefaults,
  onMounted,
  computed,
} from "vue";
import addBodyDialog from "../../accountOpenMain/component/addBodyDialog.vue";
import { auditStateMapping } from "@/utils/mapping";
import { LinkedinEnums } from "../enums";
import { useAccountList } from "../useHooks";
import { ElMessage } from "element-plus";
import {
  addAndUpdateOpenAccountApi,
  getOpenReportDetailApi,
} from "@/api/openAccount";
const { mainList, timeZoneList, getMainList } = useAccountList();
const props = withDefaults(
  defineProps<{
    visible: boolean;
    type?: string;
    rowData?: any;
    openType?: number;
  }>(),
  { type: "add" },
);
const addBodyShow = ref(false);
const closeBodyHandler = () => {
  getMainList();
};
const rechargeAmountHandler = (value) => {
  form[LinkedinEnums.RECHARGE_AMOUNT] = value.replace(/[^\d]/g, "");
};
const handlePhoneInput = (value) => {
  form[LinkedinEnums.HQPHONE] = value.replace(/[^\d]/g, "");
};
const emit = defineEmits(["update:visible", "close"]);
const form = reactive({
  [LinkedinEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  [LinkedinEnums.RECHARGE_AMOUNT]: "",
  [LinkedinEnums.PROMOTION_TIME]: "",
  [LinkedinEnums.OFFICIAL_WEBSITE]: "",
  [LinkedinEnums.FIRST_ONLINE_TIME]: "",
  [LinkedinEnums.COMPANY_NAME_E]: "",
  [LinkedinEnums.COMPANY_ADDRESS_E]: "",
  [LinkedinEnums.HQPHONE]: "",
  [LinkedinEnums.LINKEDIN_COMPANY_PAGE]: "",
  [LinkedinEnums.PERSONAL_LINKEDIN_PAGE]: "",
  [LinkedinEnums.ACCOUNT_TIMEZONE]: "",
  [LinkedinEnums.PROMOTIONAL_LINK]: "",
  openType: "",
});
const rules = reactive({
  [LinkedinEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [LinkedinEnums.INDUSTRY_TYPE]: [
    { required: true, message: "请选择行业类型", trigger: "change" },
  ],
  [LinkedinEnums.ACCOUNT_TIMEZONE]: [
    { required: true, message: "请选择账户时区", trigger: "change" },
  ],
  [LinkedinEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "blur" },
  ],
  [LinkedinEnums.RECHARGE_AMOUNT]: [
    { required: true, message: "请输入充值金额", trigger: "blur" },
  ],
  [LinkedinEnums.OFFICIAL_WEBSITE]: [
    { required: true, message: "请输入官方链接", trigger: "blur" },
  ],
  [LinkedinEnums.FIRST_ONLINE_TIME]: [
    { required: true, message: "请选择首次上线时间", trigger: "change" },
  ],
  [LinkedinEnums.PROMOTION_TIME]: [
    {
      required: true,
      message: "请输入推广时间",
      trigger: "blur",
    },
  ],
  [LinkedinEnums.COMPANY_NAME_E]: [
    { required: true, message: "请输入公司名称", trigger: "blur" },
  ],
  [LinkedinEnums.COMPANY_ADDRESS_E]: [
    { required: true, message: "请输入公司地址", trigger: "blur" },
  ],
  [LinkedinEnums.HQPHONE]: [
    { required: true, message: "请输入HQ Phone 总部电话", trigger: "blur" },
  ],
  [LinkedinEnums.LINKEDIN_COMPANY_PAGE]: [
    { required: true, message: "请输入LinkedIN COmpany Page", trigger: "blur" },
  ],
  [LinkedinEnums.PERSONAL_LINKEDIN_PAGE]: [
    { required: true, message: "请输入个人领英界面链接", trigger: "blur" },
  ],
});
let customerOpenExtId = ref("");
const handlerParams = () => {
  let customerMainName = mainList.value.find(
    (item) => item.value == form[LinkedinEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED],
  )["label"];
  let data: any = {
    ...form,
    openType: props.openType,
    applyNo: 1, // 默认值
    customerExtList: [
      {
        mediumType: 6,
        openType: props.openType,
        applyNo: 1, // 默认值
        [LinkedinEnums.COMPANY_NAME_E]: form[LinkedinEnums.COMPANY_NAME_E],
        [LinkedinEnums.COMPANY_ADDRESS_E]:
          form[LinkedinEnums.COMPANY_ADDRESS_E],
        [LinkedinEnums.HQPHONE]: form[LinkedinEnums.HQPHONE],
        [LinkedinEnums.OFFICIAL_WEBSITE]: form[LinkedinEnums.OFFICIAL_WEBSITE],
        [LinkedinEnums.RECHARGE_AMOUNT]: form[LinkedinEnums.RECHARGE_AMOUNT],
        [LinkedinEnums.FIRST_ONLINE_TIME]:
          form[LinkedinEnums.FIRST_ONLINE_TIME],

        [LinkedinEnums.PROMOTION_TIME]: form[LinkedinEnums.PROMOTION_TIME],
        [LinkedinEnums.PERSONAL_LINKEDIN_PAGE]:
          form[LinkedinEnums.PERSONAL_LINKEDIN_PAGE],
        [LinkedinEnums.LINKEDIN_COMPANY_PAGE]:
          form[LinkedinEnums.LINKEDIN_COMPANY_PAGE],
        [LinkedinEnums.ACCOUNT_TIMEZONE]: form[LinkedinEnums.ACCOUNT_TIMEZONE],
        [LinkedinEnums.PROMOTIONAL_LINK]: form[LinkedinEnums.PROMOTIONAL_LINK],
        customerOpenExtId: customerOpenExtId.value,
      },
    ],
    customerMainName,
  };
  delete data[LinkedinEnums.ACCOUNT_TIMEZONE];
  delete data[LinkedinEnums.PROMOTIONAL_LINK];
  delete data[LinkedinEnums.COMPANY_NAME_E];
  delete data[LinkedinEnums.COMPANY_ADDRESS_E];
  delete data[LinkedinEnums.HQPHONE];
  delete data[LinkedinEnums.OFFICIAL_WEBSITE];
  delete data[LinkedinEnums.RECHARGE_AMOUNT];
  delete data[LinkedinEnums.FIRST_ONLINE_TIME];
  delete data[LinkedinEnums.PROMOTION_TIME];
  delete data[LinkedinEnums.PERSONAL_LINKEDIN_PAGE];
  delete data[LinkedinEnums.LINKEDIN_COMPANY_PAGE];
  if (props.type == "edit") {
    data.applyId = props.rowData?.applyId;
  }
  return data;
};

let formRef = ref();
const btnLoading = ref(false);
const submitHandler = () => {
  formRef.value?.validate().then((valid) => {
    if (valid) {
      let data = handlerParams();
      btnLoading.value = true;
      addAndUpdateOpenAccountApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            // ElMessage.success("提交成功");
            closeDialog();
            emit("close", { type: 6, applyId: res.data.applyIdList[0] });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const closeDialog = () => {
  emit("update:visible", false);
};
const addBodyHandler = () => {
  addBodyShow.value = true;
};
const getReportDeatil = () => {
  getOpenReportDetailApi({
    applyId: props.rowData?.applyId,
  }).then((res: any) => {
    if (res && res.code && res.code == 200) {
      form[LinkedinEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED] =
        res.data.customerInfoId;
      form[LinkedinEnums.PROMOTIONAL_LINK] =
        res.data.customerOpenExtList[0]?.pushUrl;
      form[LinkedinEnums.ACCOUNT_TIMEZONE] =
        res.data.customerOpenExtList[0]?.timeZone;
      form[LinkedinEnums.COMPANY_NAME_E] =
        res.data.customerOpenExt?.companyNameEn;
      form[LinkedinEnums.COMPANY_ADDRESS_E] =
        res.data.customerOpenExt?.companyAddressEn;
      form[LinkedinEnums.HQPHONE] = res.data.customerOpenExt?.hqPhone;
      form[LinkedinEnums.OFFICIAL_WEBSITE] =
        res.data.customerOpenExt?.officialWebsiteUrl;
      form[LinkedinEnums.RECHARGE_AMOUNT] =
        res.data.customerOpenExt?.rechargeAmount;
      form[LinkedinEnums.FIRST_ONLINE_TIME] =
        res.data.customerOpenExt?.onlineTime;
      form[LinkedinEnums.PROMOTION_TIME] = res.data.customerOpenExt?.pushTime;
      form[LinkedinEnums.PERSONAL_LINKEDIN_PAGE] =
        res.data.customerOpenExt?.oneselfUrl;
      form[LinkedinEnums.LINKEDIN_COMPANY_PAGE] =
        res.data?.customerOpenExt?.companyPageUrl;
      customerOpenExtId.value = res.data?.customerOpenExt?.customerOpenExtId;
      form.openType = res.data?.openType;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const showStatusSrt = computed(() => {
  let str = "";
  if (props.rowData?.auditState == 3) {
    props.rowData?.state == 1
      ? (str = "开户中")
      : props.rowData?.state == 2
      ? (str = "开户成功")
      : props.rowData?.state == 3
      ? (str = "开户失败")
      : props.rowData?.state == 4
      ? (str = "部分开户成功")
      : (str = "");
  } else {
    props.rowData?.auditState == 1
      ? (str = "待审核")
      : props.rowData?.auditState == 2
      ? (str = "审核中")
      : props.rowData?.auditState == 3
      ? (str = "审核通过")
      : props.rowData?.auditState == 4
      ? (str = "审核驳回")
      : (str = "");
  }
  return str;
});
onMounted(() => {
  if (props.type == "view" || props.type == "edit") {
    getReportDeatil();
  }
});
</script>

<style scoped lang="scss">
:deep(.el-dialog__headerbtn) {
  right: 24px !important;
}
:deep(.el-dialog__header) {
  padding-left: 24px;
  border-bottom: 1px solid #dcdee0;
  margin-bottom: 16px;
}
:deep(.el-form-item) {
  flex-direction: column;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
