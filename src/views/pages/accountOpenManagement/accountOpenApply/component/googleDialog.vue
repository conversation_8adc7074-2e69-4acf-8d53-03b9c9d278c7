<template>
  <div>
    <el-drawer
      v-model="props.visible"
      title="Google开户申请"
      size="40%"
      :before-close="closeDialog"
    >
      <template #header>
        <h3>
          Google开户{{ props.type === "view" ? "详情" : "申请" }}
          <span v-if="props.type === 'view'" class="form-status">
            {{ showStatusSrt }}
          </span>
        </h3>
      </template>
      <div>
        <h4 class="form-header" v-if="props.type === 'view'">申请信息</h4>
        <el-form
          ref="formRef"
          style="max-width: 600px"
          :model="form"
          :rules="rules"
          label-position="left"
          :inline="false"
          label-width="120"
        >
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户主体"
                :prop="[GoogleEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
              >
                <el-select
                  v-model="form[GoogleEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]"
                  placeholder="请选择开户主体"
                  filterable
                  :disabled="props.type === 'view'"
                >
                  <el-option
                    v-for="item in mainList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" class="pt-31">
              <el-button
                type="primary"
                @click="addBodyHandler"
                :disabled="type === 'view'"
                >新增</el-button
              >
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="开户数量"
                :prop="[GoogleEnums.OPEN_ACCOUNT_NUM]"
              >
                <el-input
                  v-model="form[GoogleEnums.OPEN_ACCOUNT_NUM]"
                  placeholder="输入开户数量，一次最多支持开通5个"
                  :disabled="props.type === 'view'"
                  maxlength="5"
                  type="text"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item label="币种" :prop="[GoogleEnums.CURRENCY]">
                <el-select
                  v-model="form[GoogleEnums.CURRENCY]"
                  placeholder="请选择币种"
                  filterable
                  :disabled="props.type === 'view'"
                >
                  <el-option
                    v-for="item in [{ label: 'CNY', value: 'CNY' }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="账户时区"
                :prop="[GoogleEnums.ACCOUNT_TIMEZONE]"
              >
                <el-select
                  v-model="form[GoogleEnums.ACCOUNT_TIMEZONE]"
                  placeholder="请选择账户时区"
                  :disabled="props.type === 'view'"
                >
                  <el-option
                    v-for="item in timeZoneList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div
            v-for="(item, index) in form.addAccountCount"
            :key="index"
            class="add-account-container"
          >
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  :label="
                    form.addAccountCount?.length > 1
                      ? '账户名称' + (index + 1)
                      : '账户名称'
                  "
                  :prop="`addAccountCount.${index}.${GoogleEnums.ADVERTISING_ACCOUNT_NAME}`"
                  :rules="[
                    {
                      required: true,
                      message: '请输入广告账户名称',
                      trigger: ['blur', 'change'],
                    },
                  ]"
                >
                  <el-input
                    v-model="item[GoogleEnums.ADVERTISING_ACCOUNT_NAME]"
                    placeholder="请填写广告账户名称"
                    :disabled="props.type === 'view'"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  label="推广链接"
                  :prop="`addAccountCount.${index}.${GoogleEnums.PROMOTIONAL_LINK}`"
                  :rules="[
                    {
                      required: true,
                      message: '请输入推广链接',
                      trigger: ['blur', 'change'],
                    },
                  ]"
                >
                  <el-input
                    v-model="item[GoogleEnums.PROMOTIONAL_LINK]"
                    placeholder="请输入推广链接"
                    :disabled="props.type === 'view'"
                    maxlength="100"
                    type="text"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="20">
                <el-form-item
                  :label="
                    form.addAccountCount?.length > 1
                      ? '邀请用户' + (index + 1)
                      : '邀请用户'
                  "
                >
                  <el-select
                    v-model="item[GoogleEnums.INVITE_USERS]"
                    validate-event
                    multiple
                    filterable
                    :allow-create="true"
                    default-first-option
                    :multiple-limit="5"
                    :reserve-keyword="false"
                    remote
                    placeholder="请输入邀请用户google邮箱"
                    :disabled="props.type === 'view'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-icon
              class="el-icon--delete"
              v-if="index > 0 && props.type !== 'view'"
            >
              <Delete @click="deleteRow(index)" />
            </el-icon>
          </div>

          <div
            v-if="form.addAccountCount?.length < 5 && props.type !== 'view'"
            class="f jcc"
          >
            <el-button type="primary" class="mr10a" @click="addAccountName"
              >新增广告账户</el-button
            >
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            @click="submitHandler"
            :loading="btnLoading"
            v-if="type !== 'view'"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-drawer>
    <addBodyDialog
      v-model:visible="addBodyShow"
      v-if="addBodyShow"
      @close="closeBodyHandler"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  defineEmits,
  defineProps,
  withDefaults,
  onMounted,
  computed,
} from "vue";
import addBodyDialog from "../../accountOpenMain/component/addBodyDialog.vue";
import { GoogleEnums } from "../enums";
import { auditStateMapping } from "@/utils/mapping";
import {
  addAndUpdateOpenAccountApi,
  getOpenReportDetailApi,
} from "@/api/openAccount/index";

import { useAccountList } from "../useHooks";
import { Delete } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { validateEmail } from "@/utils";
const { mainList, timeZoneList, getMainList } = useAccountList();
const addBodyShow = ref(false);
const closeBodyHandler = () => {
  getMainList();
};
const props = withDefaults(
  defineProps<{
    visible: boolean;
    type?: string;
    rowData?: any;
    openType: number;
  }>(),
  { type: "add" },
);
const emit = defineEmits(["update:visible", "close"]);
const form = reactive({
  [GoogleEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: "",
  // [GoogleEnums.PROMOTIONAL_LINK]: "",
  [GoogleEnums.INVITE_USERS]: [],
  [GoogleEnums.ACCOUNT_TIMEZONE]: "",
  [GoogleEnums.OPEN_ACCOUNT_NUM]: "",
  addAccountCount: [
    {
      [GoogleEnums.ADVERTISING_ACCOUNT_NAME]: "",
      [GoogleEnums.INVITE_USERS]: "",
      [GoogleEnums.PROMOTIONAL_LINK]: "",
    },
  ],
  openType: "",
});

const rules = reactive({
  [GoogleEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [GoogleEnums.ADVERTISING_ACCOUNT_NAME]: [
    { required: true, message: "请输入广告账户名称", trigger: "blur" },
  ],
  [GoogleEnums.CURRENCY]: [
    { required: true, message: "请选择币种", trigger: "change" },
  ],
  [GoogleEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "blur" },
  ],
  [GoogleEnums.ACCOUNT_TIMEZONE]: [
    { required: true, message: "请选择账户时区", trigger: "change" },
  ],
  [GoogleEnums.OPEN_ACCOUNT_NUM]: [
    { required: true, message: "请输入开户数量", trigger: "blur" },
  ],
});
const addAccountName = () => {
  if (form.addAccountCount?.length == 5) return;
  form.addAccountCount.push({
    [GoogleEnums.ADVERTISING_ACCOUNT_NAME]: "",
    [GoogleEnums.INVITE_USERS]: "",
    [GoogleEnums.PROMOTIONAL_LINK]: "",
  });
};
const deleteRow = (index) => {
  form.addAccountCount?.splice(index, 1);
};
const addInviteUser = () => {};
// 邀请用户下啦
const addBodyHandler = () => {
  addBodyShow.value = true;
};
const closeDialog = () => {
  emit("update:visible", false);
};
let customerOpenExtId = ref("");
const handlerParams = () => {
  let customerMainName = mainList.value.find(
    (item) => item.value == form[GoogleEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED],
  )["label"];
  let data: any = {
    ...form,
    openType: props.openType,
    customerExtList: form.addAccountCount?.map((item: any) => {
      return {
        mediumType: 1,
        openType: props.openType,
        [GoogleEnums.OPEN_ACCOUNT_NUM]: form[GoogleEnums.OPEN_ACCOUNT_NUM],
        [GoogleEnums.ADVERTISING_ACCOUNT_NAME]:
          item[GoogleEnums.ADVERTISING_ACCOUNT_NAME], // 账户
        [GoogleEnums.INVITE_USERS]: item[GoogleEnums.INVITE_USERS].length
          ? item[GoogleEnums.INVITE_USERS]?.join(",")
          : "", // 邀请用户
        [GoogleEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]:
          form[GoogleEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED], // 主体
        [GoogleEnums.CURRENCY]: form[GoogleEnums.CURRENCY], //币种
        [GoogleEnums.PROMOTIONAL_LINK]: item[GoogleEnums.PROMOTIONAL_LINK], // 推广链接
        [GoogleEnums.ACCOUNT_TIMEZONE]: form[GoogleEnums.ACCOUNT_TIMEZONE], //  时区
        customerOpenExtId: customerOpenExtId.value,
      };
    }),
    customerMainName,
  };
  if (props.type == "edit") {
    data.applyId = props.rowData?.applyId;
  }
  delete data[GoogleEnums.INVITE_USERS];
  delete data[GoogleEnums.ADVERTISING_ACCOUNT_NAME];
  delete data[GoogleEnums.CURRENCY];
  delete data.addAccountCount;

  return data;
};
const formRef = ref();
const btnLoading = ref(false);
// 提交
const submitHandler = () => {
  formRef.value.validate().then((valid) => {
    if (valid) {
      let data = handlerParams();
      console.log(data);
      if (!data) {
        return;
      }
      if (form[GoogleEnums.OPEN_ACCOUNT_NUM] != data?.customerExtList?.length) {
        return ElMessage.error("开户数量和当前账户数量不符");
      }
      data?.customerExtList?.forEach((item) => {
        const invitationEmailList = item?.invitationEmail?.split(",") || [];
        if (
          invitationEmailList?.filter((item) => !validateEmail(item))?.length &&
          item?.invitationEmail
        ) {
          ElMessage.error("邀请用户邮箱格式不正确，请重新填写");
          throw new Error("邀请用户邮箱格式不正确，请重新填写");
        }
      });

      btnLoading.value = true;
      addAndUpdateOpenAccountApi(data)
        .then((res: any) => {
          if (res.code == 200) {
            // ElMessage.success("提交成功");
            closeDialog();
            emit("close", { type: 1, applyId: res.data.applyIdList[0] });
          } else {
            ElMessage.error(res.msg);
          }
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
const getReportDeatil = () => {
  getOpenReportDetailApi({
    applyId: props.rowData?.applyId,
  }).then((res: any) => {
    if (res && res.code && res.code == 200) {
      console.log(res);
      form[GoogleEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED] = res.data.customerInfoId;
      form[GoogleEnums.CURRENCY] = res?.data?.customerOpenExtList[0]?.currency;
      form[GoogleEnums.ADVERTISING_ACCOUNT_NAME] =
        res.data.customerOpenExt?.accountName;
      form[GoogleEnums.PROMOTIONAL_LINK] = res.data.pushUrl;
      form[GoogleEnums.ACCOUNT_TIMEZONE] =
        res?.data?.customerOpenExtList[0]?.timeZone;
      form[GoogleEnums.OPEN_ACCOUNT_NUM] = res.data.applyNo;
      form["addAccountCount"] = res?.data?.customerOpenExtList?.map((item) => {
        return {
          accountName: item.accountName,
          pushUrl: item.pushUrl,
          timeZone: item.timeZone,
          invitationEmail: item.invitationEmail?.split(","),
        };
      });
      customerOpenExtId.value = res.data?.customerOpenExt?.customerOpenExtId;
      form.openType = res.data?.openType;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const showStatusSrt = computed(() => {
  let str = "";
  if (props.rowData?.auditState == 3) {
    props.rowData?.state == 1
      ? (str = "开户中")
      : props.rowData?.state == 2
      ? (str = "开户成功")
      : props.rowData?.state == 3
      ? (str = "开户失败")
      : props.rowData?.state == 4
      ? (str = "部分开户成功")
      : (str = "");
  } else {
    props.rowData?.auditState == 1
      ? (str = "待审核")
      : props.rowData?.auditState == 2
      ? (str = "审核中")
      : props.rowData?.auditState == 3
      ? (str = "审核通过")
      : props.rowData?.auditState == 4
      ? (str = "审核驳回")
      : (str = "");
  }
  return str;
});
onMounted(() => {
  if (props.type == "view" || props.type == "edit") {
    getReportDeatil();
  }
});
</script>

<style scoped lang="scss">
:deep(.el-dialog__headerbtn) {
  right: 24px !important;
}
:deep(.el-dialog__header) {
  padding-left: 24px;
  border-bottom: 1px solid #dcdee0;
  margin-bottom: 16px;
}
.add-account-container {
  position: relative;
  .el-icon--delete {
    position: absolute;
    right: 30px;
    top: 0%;
  }
  .el-icon--delete:hover {
    color: #ff4d4f;
    cursor: pointer;
  }
}
:deep(.el-form-item) {
  flex-direction: column;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
