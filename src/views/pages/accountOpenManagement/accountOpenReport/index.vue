<template>
  <div>
    <div class="openReport-container">
      <div class="title">
        <Breadcrumb />
      </div>
      <div>
        <!-- 媒体渠道tab组件 -->
        <mianTabs ref="mediaTabsRef" :show-new-media="false"  :noProsime="true" @tab-click="switchTab" />
        <dynamicTable
          ref="dynamicTableRef"
          label-width="95px"
          :columns="columns"
          :form-items="formItems"
          :initialData="tableData"
          @search="getOpenReportList"
          :loading="tableLoading"
          row-key="id"
        >
          <template #tableTop>
            <el-button
              type="primary"
              @click="downloadFileHandler"
              v-if="accountOpeningLogControls"
            >
              excel导出</el-button
            >
          </template>
          <template #custom="{ row }">
            <!-- 审核状态 -->
            <div
              class="tooltip-div f aic"
              v-if="row.prop == EOpenAccountTable.AUDIT_STATUS"
            >
              {{
                row[EOpenAccountTable.AUDIT_STATUS] == 1
                  ? "待审核"
                  : row[EOpenAccountTable.AUDIT_STATUS] == 2
                  ? "审核中"
                  : row[EOpenAccountTable.AUDIT_STATUS] == 3
                  ? "审核通过"
                  : row[EOpenAccountTable.AUDIT_STATUS] == 4
                  ? "审核驳回"
                  : ""
              }}
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="row.errorMsg"
                placement="bottom"
                :disabled="!row.errorMsg || row.errorMsg == ''"
                v-if="row[EOpenAccountTable.AUDIT_STATUS] == 4"
              >
                <el-icon class="select_icon">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </div>
            <!-- 开户状态 -->
            <div
              class="tooltip-div f aic"
              v-if="
                row.prop == EOpenAccountTable.OPEN_ACCOUNT_STATUS &&
                row[EOpenAccountTable.AUDIT_STATUS] == 3
              "
            >
              {{
                row[EOpenAccountTable.OPEN_ACCOUNT_STATUS] == 1
                  ? "开户中"
                  : row[EOpenAccountTable.OPEN_ACCOUNT_STATUS] == 2
                  ? "开户成功"
                  : row[EOpenAccountTable.OPEN_ACCOUNT_STATUS] == 3
                  ? "开户失败"
                  : row[EOpenAccountTable.OPEN_ACCOUNT_STATUS] == 4
                  ? "部分开户成功"
                  : ""
              }}
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="row.errorMsg"
                placement="bottom"
                :disabled="!row.errorMsg || row.errorMsg == ''"
                v-if="row[EOpenAccountTable.OPEN_ACCOUNT_STATUS] == 3"
              >
                <el-icon class="select_icon">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </div>
            <div
              v-if="
                row.prop == EOpenAccountTable.OPEN_ACCOUNT_STATUS &&
                row[EOpenAccountTable.AUDIT_STATUS] != 3
              "
            >
              --
            </div>
          </template>
        </dynamicTable>
      </div>
    </div>
    <!-- 谷歌 -->
    <googleDialog
      v-if="dialogStatus.googleShow"
      v-model:visible="dialogStatus.googleShow"
      :type="currentType"
      :row-data="currentRow"
      :openType="openTypeNum"
      @close="getList"
    />
    <!-- facebook-线上 -->
    <metaDialogOnLine
      v-if="dialogStatus.metaShow"
      v-model:visible="dialogStatus.metaShow"
      :type="currentType"
      @close="getList"
      :openType="openTypeNum"
      :row-data="currentRow"
    />
    <!-- facebook-线下 -->
    <metaDialogOffline
      v-if="dialogStatus.metaOfflineShow"
      v-model:visible="dialogStatus.metaOfflineShow"
      :type="currentType"
      @close="getList"
      :openType="openTypeNum"
      :row-data="currentRow"
    />
    <!-- 抖音-线上 -->
    <tiktokDialogOnLine
      v-if="dialogStatus.tiktokShow"
      v-model:visible="dialogStatus.tiktokShow"
      :openType="openTypeNum"
      :type="currentType"
      @close="getList"
      :row-data="currentRow"
    />
    <!-- 抖音-线下 -->
    <tiktokDialogOffline
      v-if="dialogStatus.tiktokOfflineShow"
      v-model:visible="dialogStatus.tiktokOfflineShow"
      :type="currentType"
      :row-data="currentRow"
      :openType="openTypeNum"
      @close="getList"
    />
    <bingDialog
      v-if="dialogStatus.bingShow"
      v-model:visible="dialogStatus.bingShow"
      :type="currentType"
      :openType="openTypeNum"
      @close="getList"
      :row-data="currentRow"
    />
    <linkedinDialog
      v-if="dialogStatus.linkedinShow"
      v-model:visible="dialogStatus.linkedinShow"
      :type="currentType"
      :openType="openTypeNum"
      @close="getList"
      :row-data="currentRow"
    />
    <!-- 线下-yandex -->
    <yangdexDialogOffline
      v-if="dialogStatus.yandexOfflineShow"
      v-model:visible="dialogStatus.yandexOfflineShow"
      :type="currentType"
      :openType="openTypeNum"
      @close="getList"
      :row-data="currentRow"
    />
    <!-- 线上-yandex -->
    <yangdexDialogOnLine
      v-if="dialogStatus.yangdexShow"
      v-model:visible="dialogStatus.yangdexShow"
      :type="currentType"
      :openType="openTypeNum"
      @close="getList"
      :row-data="currentRow"
    />
    <!-- 开户成功账户 -->
    <OpenAccountSuccess :gridData="gridData" ref="openAccountSuccessRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h, onMounted, computed } from "vue";
import { getOpenReportListApi, openAccountSuccess } from "@/api/openAccount";
import dynamicTable from "@/components/dynamicTable.vue";
import mianTabs from "@/views/pages/accountList/components/list/mediaTabs.vue";
import googleDialog from "../accountOpenApply/component/googleDialog.vue";
import tiktokDialogOffline from "../accountOpenApply/component/tiktokOffline.vue";
import tiktokDialogOnLine from "../accountOpenApply/component/tiktokDialog.vue";
import metaDialogOnLine from "../accountOpenApply/component/metaDialog.vue";
import metaDialogOffline from "../accountOpenApply/component/metaOffline.vue";
import bingDialog from "../accountOpenApply/component/bingDialog.vue";
import linkedinDialog from "../accountOpenApply/component/linkedinDialog.vue";
import yangdexDialogOnLine from "../accountOpenApply/component/yangdexDialog.vue";
import yangdexDialogOffline from "../accountOpenApply/component/yandexOffline.vue";
import { ElInput, ElButton, ElSelect, ElTooltip, ElIcon, ElPopover } from "element-plus";
import { EOpenAccountForm, EOpenAccountTable } from "./enums";
import { downloadFile } from "@/utils/common";
import OpenAccountSuccess from "./accountTble.vue";
import iconGoogle from "@/assets/images/home/<USER>";
import iconYandex from "@/assets/images/home/<USER>";
import iconMeta from "@/assets/images/home/<USER>";
import iconTikTok from "@/assets/images/home/<USER>";
import iconBing from "@/assets/images/home/<USER>";
import iconLink from "@/assets/images/home/<USER>";
import { useRoute } from "vue-router";
import { getQueryParamsByKey } from "../../../../utils/common";
import usePermissionStore from "@/store/modules/permission";
import Breadcrumb from "@/components/Breadcrumb/index.vue";
import MoreIcon from "@/components/moreIcon.vue"

const permissionStore = usePermissionStore();
const accountOpeningLogControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountOpenMain?.indexOf(
      "accountOpeningLogControls"
    ) > -1
);
const route = useRoute();
let dialogStatus = reactive({
  googleShow: false,
  tiktokShow: false,
  metaShow: false,
  bingShow: false,
  linkedinShow: false,
  yangdexShow: false,
  yandexOfflineShow: false,
  metaOfflineShow: false,
  tiktokOfflineShow: false,
});
const gridData = ref([]);
const openTypeNum = ref(1);
const openAccountSuccessRef = ref();
let activeTab = ref(getQueryParamsByKey("mediaType") || 1);

const tabList = [
  {
    id: 1,
    label: "Google",
    img: iconGoogle,
  },
  {
    id: 2,
    label: "Yandex",
    img: iconYandex,
  },
  {
    id: 3,
    label: "Meta",
    img: iconMeta,
  },
  {
    id: 4,
    label: "Tiktok",
    img: iconTikTok,
  },

  {
    id: 5,
    label: "Bing",
    img: iconBing,
  },
  {
    id: 6,
    label: "Linkedin",
    img: iconLink,
  },
];
const columns = ref([
  {
    label: "申请单号",
    prop: "applyId",
    width: "200",
  },
  {
    label: "开户主体",
    prop: EOpenAccountTable.OPEN_ACCOUNT_SUBJECT,
    width: "200",
  },
  {
    label: "审核状态",
    prop: EOpenAccountTable.AUDIT_STATUS,
    type: "custom",
  },
  {
    label: "开户状态",
    prop: EOpenAccountTable.OPEN_ACCOUNT_STATUS,
    type: "custom",
    width: "120",
  },
  {
    label: "申请提交时间",
    prop: "createTime",
    width: "180",
  },
  {
    label: "申请数量",
    prop: "applyNo",
  },
  {
    label: "申请人",
    prop: "operatorName",
  },
  {
    label: "操作",
    width: "160px",
    prop: (row) => btnElement(row),
  },
]) as any;
const formItems = [
  {
    label: "开户主体",
    prop: EOpenAccountForm.OPEN_ACCOUNT_SUBJECT,
    component: ElInput,
  },
  {
    label: "开户状态",
    prop: EOpenAccountForm.OPEN_ACCOUNT_STATUS,
    component: ElSelect,
    props: {
      selectOptions: [
        {
          label: "全部",
          value: 5,
        },
        {
          label: "开户中",
          value: 1,
        },
        {
          label: "开户成功",
          value: 2,
        },
        {
          label: "开户失败",
          value: 3,
        },
        {
          label: "部分成功",
          value: 4,
        },
      ],
    },
  },
  {
    label: "申请单号",
    prop: "applyId",
    // 默认值
    defaultValue: route.query.applyId ? route.query.applyId : "",
    component: ElInput,
  },
  {
    label: "OE申请编号",
    prop: "thirdApplyId",
    component: ElInput,
  },
  {
    label: "审核状态",
    prop: "auditState",
    component: ElSelect,
    // 默认值
    defaultValue: route.query.auditState ? route.query.auditState : "",
    props: {
      selectOptions: [
        {
          label: "全部",
          value: "5",
        },
        {
          label: "待审核",
          value: "1",
        },
        // {
        //   label: "审核中",
        //   value: '2',
        // },
        {
          label: "审核通过",
          value: "3",
        },
        {
          label: "审核驳回",
          value: "4",
        },
      ],
    },
  },
];
let tableLoading = ref(false);
const mediaChange = () => {};
const switchTab = (row: any) => {
  if (row.type === 3) {
    columns.value.splice(1, 0, {
      label: "OE申请编号",
      prop: "thirdApplyId",
      width: "150px",
    });
  } else {
    columns.value = columns.value?.filter((item) => {
      return item.prop !== "thirdApplyId";
    });
  }
  activeTab.value = row.type;
  baseParams.value.pageSize = 10;
  baseParams.value.pageIndex = 1;
  dynamicTableRef.value?.setCurrentPage(1);
  dynamicTableRef.value?.resetForm();
};
const btnElement = (row: any) => {
  let btnList = [
    {
      label: "详情",
      type: 1,
      callback: btnAction,
      permission: "openAccountHistoryLook",
      visible: false,
      color: "#519C66",
      bgColor: "rgba(50, 147, 111, 0.1)",
      class: "table-button view-table-button" 
    },
  ];
  if (row[EOpenAccountTable.AUDIT_STATUS] == 4) {
    btnList.push({
      label: "再次申请",
      type: 2,
      callback: btnAction,
      permission: "openAccountHistoryBtn",
      visible: !accountOpeningLogControls.value,
      color: "#519C66",
      bgColor: "rgba(50, 147, 111, 0.1)",
      class: "table-button"
    });
  }
  if (row.state == 2 || row.state == 4) {
    btnList.push({
      label: "账户",
      type: 1,
      callback: lookAccount,
      permission: "openAccountHistoryLook",
      visible: !accountOpeningLogControls.value,
      color: "#519C66",
      bgColor: "rgba(50, 147, 111, 0.1)",
      class: "table-button"
    });
  }
  btnList = btnList?.filter((item) => {
    return !item.visible;
  });
  return h(
    "div",
    {},
    btnList.map((item, index) => {
      if(index <= 1) {
        return h(
        ElButton,
        {
          class: item.class,
          onClick: () => item.callback(row, item.type),
        },
        () => item.label
      );
      }  else {
          return h(
            ElPopover,
            {
              trigger: "hover",
              placement: "bottom",
              popperStyle: "padding:0;min-width:auto;width:auto;"
            },
            {
              reference: () => h(MoreIcon),
              default: () =>
                h(
                  "div",
                  {
                    class: "popList"
                  },
                  btnList.slice(2).map((item, index) =>
                    h(
                      "div",
                      {
                        class: "pop_button",
                        onClick: () => item.callback(index + 1, row)
                      },
                      { default: () => item.label }
                    )
                  )
                )
            }
          )
        }
      
    })
  );
};
const lookAccount = async (row) => {
  console.log(row);
  try {
    const res: any = await openAccountSuccess({ applyId: row.applyId });
    if (res.code == 200) {
      console.log(res);
      gridData.value = res.data;
      openAccountSuccessRef.value?.open();
    }
  } catch (error) {
    console.log(error);
  }
};
const btnAction = (row: any, type: number) => {
  switch (type) {
    case 1:
      view(row);
      break;
    case 2:
      aginApply(row);
      break;
  }
};
let currentRow = ref<any>();
let currentType = ref<"view" | "edit">("view");
const view = (value) => {
  openTypeNum.value = value.openType;
  switch (value.mediumType) {
    case 1:
      dialogStatus.googleShow = true;
      break;
    case 2:
      if (openTypeNum.value == 1) {
        dialogStatus.yangdexShow = true;
      } else {
        dialogStatus.yandexOfflineShow = true;
      }
      break;
    case 3:
      if (openTypeNum.value == 1) {
        dialogStatus.metaShow = true;
      } else {
        dialogStatus.metaOfflineShow = true;
      }
      break;
    case 4:
      if (openTypeNum.value == 1) {
        dialogStatus.tiktokShow = true;
      } else {
        dialogStatus.tiktokOfflineShow = true;
      }
      break;
    case 5:
      dialogStatus.bingShow = true;
      break;
    case 6:
      dialogStatus.linkedinShow = true;
      break;
  }
  currentRow.value = value;
  currentType.value = "view";
};
const aginApply = (value) => {
  openTypeNum.value = value.openType;
  switch (value.mediumType) {
    case 1:
      dialogStatus.googleShow = true;
      break;
    case 2:
      if (value.openType == 1) {
        dialogStatus.yangdexShow = true;
      } else {
        dialogStatus.yandexOfflineShow = true;
      }
      break;
    case 3:
      if (value.openType == 1) {
        dialogStatus.metaShow = true;
      } else {
        dialogStatus.metaOfflineShow = true;
      }
      break;
    case 4:
      if (value.openType == 1) {
        dialogStatus.tiktokShow = true;
      } else {
        dialogStatus.tiktokOfflineShow = true;
      }
      break;
    case 5:
      dialogStatus.bingShow = true;
      break;
    case 6:
      dialogStatus.linkedinShow = true;
      break;
  }
  currentRow.value = value;
  currentType.value = "edit";
};
let baseParams = ref({
  pageIndex: 1,
  pageSize: 10,
  [EOpenAccountForm.MEDIUM_TYPE]: activeTab.value,
  applyId: route.query.applyId ? route.query.applyId : "",
  auditState: route.query.auditState ? route.query.auditState : "",
});
// 处理入参
const handlerParams = (value) => {
  let params = {};
  if (value) {
    params = {
      // ...baseParams.value,
      [EOpenAccountForm.MEDIUM_TYPE]: activeTab.value,
      pageIndex: value?.currentPage,
      pageSize: value?.pageSize,
      thirdApplyId: value?.thirdApplyId,
      applyId: value?.applyId,
      auditState: value?.auditState == 5 ? "" : value?.auditState,
      [EOpenAccountForm.OPEN_ACCOUNT_SOURCE]:
        value[EOpenAccountForm.OPEN_ACCOUNT_SOURCE] || undefined,
      [EOpenAccountForm.OPEN_ACCOUNT_STATUS]:
        value[EOpenAccountForm.OPEN_ACCOUNT_STATUS] == 5
          ? ""
          : value[EOpenAccountForm.OPEN_ACCOUNT_STATUS] || undefined,
      [EOpenAccountForm.OPEN_ACCOUNT_SUBJECT]:
        value[EOpenAccountForm.OPEN_ACCOUNT_SUBJECT] || undefined,
    };
  } else {
    params = baseParams.value;
  }
  baseParamsReport.value = params;
  return params;
};
let tableData = ref([]) as any;
let dynamicTableRef = ref();
let baseParamsReport = ref();
const getOpenReportList = (value?) => {
  let data = handlerParams(value);
  tableLoading.value = true;
  getOpenReportListApi(data)
    .then((res: any) => {
      tableData.value = res.data?.list;
      dynamicTableRef.value?.setPageTotal(Number(res.data.total));
    })
    .finally(() => {
      tableLoading.value = false;
    });
};
const getList = () => {
  dynamicTableRef.value?.fetchData();
};
const downloadFileHandler = () => {
  console.log(import.meta.env);
  const isPre = window.location.href.indexOf("pre") > -1;
  const isTest = window.location.href.indexOf("test-") > -1;
  const url = "https://pre-ads.gmarketing.tech";
  const urlTest = "https://test-ads.gmarketing.tech";
  downloadFile({
    url:
      (isPre ? url : isTest ? urlTest : import.meta.env?.VITE_REQUEST_URL) +
      "/ad-trade-web/customer/openlistExport",
    params: baseParamsReport.value,
    method: "POST",
    headers: {},
    name: "开户记录列表",
  });
};
// 媒体tab组件实列
const mediaTabsRef = ref<InstanceType<typeof mianTabs>>(null);
// 重置媒体类型
const resetMediaType = () => {
  const mediaType = getQueryParamsByKey("mediaType") as any;
  if (mediaType) {
    mediaTabsRef.value.setCurrentMedia(mediaType);
  }
};
onMounted(() => {
  resetMediaType();
  getOpenReportList();
});
</script>

<style scoped lang="scss">
.openReport-container {
  padding-bottom: 20px;

  .title {
    color: #202020;
    font-size: 20px;
    font-weight: 500;
    font-family: "PingFang SC";
    margin-bottom: 20px;
  }
  .head-tab {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    ul {
      display: flex;
      gap: 12px;
      li {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 10px;
        border-radius: 8px;
        border: 1px solid #d9d9d9;
        cursor: pointer;
        position: relative;
        .tab-icon {
          display: flex;
          .iconImg {
            width: 16px;
            height: 16px;
          }
        }
        .bingo {
          position: absolute;
          color: #fff;
          bottom: -1px;
          right: 0;
        }
        img {
          width: 24px;
          height: 24px;
        }
      }
      .liActive {
        border: 1px solid #1f7bf2;
      }
    }
  }
}
</style>
