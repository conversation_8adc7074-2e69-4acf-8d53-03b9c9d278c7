<template>
  <el-dialog v-model="dialogTableVisible" title="账户信息" width="500">
    <el-table :data="gridData">
      <el-table-column property="thirdAccountName" label="账户名称" />
      <el-table-column property="thirdAccountId" label="账户ID" />
    </el-table>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref } from "vue";
const dialogTableVisible = ref(false);
defineProps({
  gridData: {
    type: Array,
    default: () => [],
  },
});
const open = () => {
  dialogTableVisible.value = true;
};
defineExpose({
  open,
});
</script>
