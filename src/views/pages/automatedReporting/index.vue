<template>
  <div class="zq-zuto-report">
    <Breadcrumb />
    <el-form
      ref="searchRef"
      :model="searchForm"
      :inline="true"
      class="zq-advert-search"
    >
      <el-form-item>
        <el-input
          v-model="searchForm.thirdCustomerNm"
          style="width: 180px"
          placeholder="账户名称或ID"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.reportStatus"
          placeholder="请选择生成状态"
          style="width: 180px"
          clearable
        >
          <el-option
            v-for="item in generateStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="searchForm.reportType"
          placeholder="请选择报表类型"
          style="width: 180px"
          clearable
        >
          <el-option
            v-for="item in reportTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item style="margin-right: 0">
        <el-button type="primary" :icon="Search" @click="autoReportSearch"
          >查询</el-button
        >
        <el-button
          type="primary"
          @click="generateReport"
          :disabled="!ruleButtonAuth"
          id="add_report"
          >生成报告</el-button
        >
      </el-form-item>
    </el-form>
    <div class="container">
      <el-table
        :data="tableData"
        style="height: calc(100vh - 250px); width: 100%; min-height: 200px"
        v-loading="loading"
      >
        <el-table-column prop="mediumType" label="媒体平台">
          <template #default="{ row }">
            {{ midumeTypeMapping[row.mediumType] }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="thirdCustomerName" label="账户名称">
          <template #default="{ row }">
            {{ row.thirdCustomerId + "/" + row.thirdCustomerName }}
          </template>
        </el-table-column>
        <el-table-column label="报表类型">
          <template #default="{ row }">
            {{ reportType[row.reportType] }}
          </template>
        </el-table-column>

        <el-table-column label="报表周期">
          <template #default="{ row }">
            {{
              row.reportStartDate?.split(" ")[0] +
              " - " +
              row.reportEndDate?.split(" ")[0]
            }}
          </template>
        </el-table-column>
        <el-table-column label="生成状态">
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span
                class="reportStatus-point"
                :style="{
                  background: reportStatusBackfround[row.reportStatus],
                }"
              ></span>
              <div
                v-if="row.reportStatus === 2"
                style="display: flex; align-items: center"
              >
                {{ reportStatus[row.reportStatus] }}
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="
                    row.reportErrorMsg
                      ?.map((item, index) => {
                        return item
                          ? `${index + 1}、${item}！`
                          : `${index + 1}、未知错误！`;
                      })
                      .join('\n') || '未知错误！'
                  "
                  placement="top"
                >
                  <WarningFilled
                    style="
                      width: 1em;
                      height: 1em;
                      margin-left: 0.2em;
                      cursor: pointer;
                    "
                  />
                </el-tooltip>
              </div>

              <span v-else>{{ reportStatus[row.reportStatus] }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reportDate" label="生成日期" />
        <el-table-column label="操作">
          <template #default="{ row, index }">
            <div style="display: flex; align-items: center" id="add_dowloand">
              <el-button
                class="table-button view-table-button"
                :icon="View"
                @click="look(row)"
                :disabled="row.reportStatus != 3 || !ruleButtonAuth"
                >查看</el-button
              >
              <!-- <el-button link type="primary" size="small" :icon="Download" @click="download(row)">下载</el-button> -->
              <el-dropdown :disabled="row.reportStatus != 3">
                <el-button
                  class="table-button"
                  :icon="Download"
                  :disabled="row.reportStatus != 3 || !ruleButtonAuth"
                  >下载</el-button
                >
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="downloadPDF(row)"
                      >PDF</el-dropdown-item
                    >
                    <el-dropdown-item @click="downloadEXCEL(row)"
                      >EXCEL</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-if="page.total"
        :total="page.total"
        :currentPage="page.pageIndex"
        :pageSize="page.pageSize"
        @update:pageSize="changePageSize"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
    <el-drawer
      v-model="isShowReport"
      size="40%"
      destroy-on-close
      @close="closeDialog"
    >
      <template #title>
        <div class="drawer_title">生成报告</div>
      </template>
      <el-form
        ref="generateReportRef"
        :model="generateReportForm"
        :rules="rules"
        label-width="100"
        label-position="left"
        :inline="false"
      >
        <el-form-item label="媒体平台" prop="mediumType">
          <el-select
            v-model="generateReportForm.mediumType"
            placeholder="请选择媒体平台"
            @change="changeMediumType"
          >
            <el-option
              v-for="item in subsriptionMeaid"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告账户" prop="third">
          <el-select
            v-model="generateReportForm.third"
            remote
            filterable
            :remote-method="remoteMethod"
            :loading="loading"
            placeholder="请选择广告账号"
          >
            <el-option
              v-for="item in thirdList"
              :key="item.thirdCustomerId"
              :label="item.thirdCustomerId + '/' + item.thirdCustomerName"
              :value="
                item.thirdCustomerId +
                '/' +
                item.thirdCustomerName +
                '/' +
                item.customerId
              "
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报告类型" prop="reportType">
          <el-select
            v-model="generateReportForm.reportType"
            placeholder="请选择报表类型"
            disabled
          >
            <el-option :value="3" label="自定义" />
          </el-select>
        </el-form-item>
        <el-form-item label="报告日期" prop="date">
          <el-date-picker
            v-model="generateReportForm.date"
            type="daterange"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            :disabled-date="pickerOptionsRang"
            end-placeholder="结束日期"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close(generateReportRef)">取消</el-button>
          <el-button
            type="primary"
            @click="ok(generateReportRef)"
            :loading="generateReportLoading"
            >确认</el-button
          >
        </div>
      </template>
    </el-drawer>
    <GoogleReport
      :taskId="reportTaskId"
      :typeName="downloadTypeName"
      :downloadExcelName="downloadExcelName"
      v-if="isShowGoogleReport"
      @colseExcel="colseExcel"
      ref="googleReportRef"
      class="look-report"
    />
    <YandexReport
      :taskId="reportTaskId"
      :typeName="downloadTypeName"
      :downloadExcelName="downloadExcelName"
      v-if="isShowYandexReport"
      ref="yandexReportRef"
      @colseExcel="colseExcel"
      class="look-report"
    />
    <FacebookReport
      :taskId="reportTaskId"
      :typeName="downloadTypeName"
      :downloadExcelName="downloadExcelName"
      v-if="isShowFacebookReport"
      ref="FacebookReportRef"
      class="look-report"
    />
    <TikTokReport
      :taskId="reportTaskId"
      :typeName="downloadTypeName"
      :downloadExcelName="downloadExcelName"
      v-if="isShowTikTokReport"
      @colseExcel="colseExcel"
      ref="TikTokReportRef"
      class="look-report"
    />
    <BingReport
      :taskId="reportTaskId"
      :typeName="downloadTypeName"
      :downloadExcelName="downloadExcelName"
      v-if="isShowBingReport"
      ref="bingReportRef"
      class="look-report"
      @colseExcel="colseExcel"
    />
    <LinkedInReport
      :taskId="reportTaskId"
      :typeName="downloadTypeName"
      :downloadExcelName="downloadExcelName"
      v-if="isShowLinekedInReport"
      ref="linekedInReportRef"
      class="look-report"
      @colseExcel="colseExcel"
    />
    <TourStep :open="open" :tourStep="tourPageList" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { Search, View, Download, WarningFilled } from "@element-plus/icons-vue";
import {
  getReportList,
  generateReportInfo,
  downloadReport,
} from "@/api/autoReportApi";
import { searchMediumTypeList } from "@/api/adUserSetingApi";
import Pagination from "@/components/Pagination/index.vue";
import GoogleReport from "@/views/report/google.vue";
import YandexReport from "@/views/report/yandex.vue";
import FacebookReport from "@/views/report/facebook.vue";
import TikTokReport from "@/views/report/tikTok.vue";
import BingReport from "@/views/report/bing.vue";
import LinkedInReport from "@/views/report/linkedIn.vue";
import { useRouter } from "vue-router";
import type { FormInstance } from "element-plus";
import usePermissionStore from "@/store/modules/permission";
import TourStep from "@/components/tourStep/index.vue";
import { microForceDispatch } from "@/utils";
import {
  subsriptionMeaid,
  midumeTypeMapping,
  mediumTypePath,
  generateStatus,
  reportTypeList,
  reportStatusBackfround,
  reportType,
  reportStatus,
} from "@/utils/mapping";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const guideDetail = JSON.parse(localStorage.getItem("guideDetail") || "{}");
const open = ref(false);
const downloadExcelName = ref<any>(null);
const tourPageList = [
  {
    target: "add_report",
    placement: "bottom",
    title: "生成报告",
    description: "支持按天选择任意时间范围的报告",
  },
  {
    target: "add_dowloand",
    placement: "bottom",
    title: "查看/下载",
    description: "广告投放月报表系统会自动生成，可以在这里查看和下载",
  },
];
const debounceClick = ref(false);
const permissionStore = usePermissionStore();
const ruleButtonAuth = computed(
  () =>
    permissionStore.buttonAuth["AutomatedReporting"]?.indexOf("controls") > -1
); // 自动化报表权限
const router = useRouter();
const googleReportRef = ref();
const yandexReportRef = ref();
const FacebookReportRef = ref();
const TikTokReportRef = ref();
const bingReportRef = ref();
const linekedInReportRef = ref();
const loading = ref(false);
const searchForm = ref({
  thirdCustomerNm: "",
  reportStatus: "",
  reportType: "",
});
const cloneThirdListOptions = ref<ThirdList[]>();
const reportTaskId = ref("");
// 报表显示状态
const reportVisibility = reactive({
  google: false,
  yandex: false,
  facebook: false,
  tikTok: false,
  bing: false,
  linekedIn: false,
});
// 重命名变量以便更清晰地使用
const isShowGoogleReport = computed(() => reportVisibility.google);
const isShowYandexReport = computed(() => reportVisibility.yandex);
const isShowFacebookReport = computed(() => reportVisibility.facebook);
const isShowTikTokReport = computed(() => reportVisibility.tikTok);
const isShowBingReport = computed(() => reportVisibility.bing);
const isShowLinekedInReport = computed(() => reportVisibility.linekedIn);
const generateReportRef = ref<FormInstance>();
const generateReportLoading = ref(false);
const downloadTypeName = ref("pdf");
const rules = ref({
  mediumType: [
    { required: true, message: "请选择媒体类型", trigger: "change" },
  ],
  third: [{ required: true, message: "请选择三方客户", trigger: "change" }],
  reportType: [
    { required: true, message: "请选择报表类型", trigger: "change" },
  ],
  date: [{ required: true, message: "请选择日期", trigger: "change" }],
});
const isShowReport = ref(false);
const generateReportForm = ref({
  mediumType: "",
  third: "",
  reportType: 3,
  date: [],
});
interface ThirdList {
  thirdCustomerId: string;
  thirdCustomerName: string;
  customerId: string;
}
const thirdList = ref<ThirdList[]>([]);
const page = reactive({
  pageIndex: 1,
  pageSize: 10,
  total: 0,
});
const tableData = ref([]);
// 手动搜索
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      thirdList.value = cloneThirdListOptions.value.filter((item: any) => {
        return item.label.toLowerCase().includes(query.toLowerCase());
      });
    }, 200);
  } else {
    thirdList.value = cloneThirdListOptions.value;
  }
};
const isEdit = ref(false);
const pickerOptionsRang = (date) => {
  // 禁用当前日期之后的所有日期
  const today = new Date();
  return date.getTime() >= today.setHours(0, 0, 0, 0); // 禁用当天以及之后的的日期
};
const closeDialog = () => {
  isEdit.value = false;
};
const isSHowTour = computed(() => {
  return guideDetail.publicPoolManage == 0 || !guideDetail.publicPoolManage;
});
onMounted(() => {
  generateReportData();
});

const autoReportSearch = () => {
  generateReportData();
  // 埋点
  microForceDispatch("mixpanel", {
    key: "marketingClickSearchReport",
    otherData: {
      search_text: searchForm?.value?.thirdCustomerNm || "",
      generate_state:
        (generateStatus.filter(
          (sItem) => sItem.value === searchForm?.value?.reportStatus
        ) || [])[0]?.label || "",
      report_type:
        (reportTypeList.filter(
          (sItem) => sItem.value === searchForm?.value?.reportType
        ) || [])[0]?.label || "",
    },
  });
};
// 获取自动表格
const generateReportData = async () => {
  try {
    loading.value = true;
    const param = {
      ...searchForm.value,
      pageIndex: page.pageIndex,
      pageSize: page.pageSize,
    };
    const res: any = await getReportList(param);
    loading.value = false;
    if (res.code == 200) {
      tableData.value = res?.data?.list;
      page.total = res?.data?.total;
      if (window.microApp) {
        // 判断是不是需要开启引导
        window.microApp.addDataListener((data: any) => {
          if (data.type === "openGuide") {
            // open.value = true;
          }
          return "";
        });
      }
    }
  } catch (error) {
    loading.value = false;
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
  window.parent.postMessage(
    {
      type: "CHILD_COMPONENT_MOUNTED",
      component: "MyComponent",
    },
    "*"
  );
};
// 生成报告
const generateReport = () => {
  isShowReport.value = true;
  getMediaList();
  // 埋点
  microForceDispatch("mixpanel", {
    key: "marketingClickProduceReport",
    otherData: {},
  });
};
// 查看报表
const look = async (row: any) => {
  console.log(row.mediumType);
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: `/ads/${mediumTypePath[row.mediumType]}?taskId=${row.reportTaskId}`,
      name: "ads",
      parentPath: "/ads",
      isChild: true,
    });
  }
};
// 下载报表 PDF
const downloadPDF = async (row: any) => {
  if (!row.reportTaskId) return;

  const loading = ElLoading.service({
    lock: true,
    text: "下载中...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    const res: any = await downloadReport({ reportTaskId: row.reportTaskId });

    if (res?.code == 200) {
      // 处理PDF二进制数据
      const fileName = `${row.thirdCustomerName}-${midumeTypeMapping[row.mediumType]}-${
        reportType[row.reportType]
      }-报表.pdf`;

      downloadBinaryFile(res.data, "application/pdf", fileName);
    } else {
      ElMessage.error(res?.msg || "下载PDF失败");
    }
  } catch (error) {
    console.error("下载PDF错误:", error);
    ElMessage.error("下载PDF失败");
  } finally {
    loading.close();
  }
};
/**
 * 处理二进制下载
 */
 const downloadBinaryFile = (base64Data, mimeType, fileName) => {
  const binaryString = window.atob(base64Data);
  const bytes = new Uint8Array(binaryString.length);

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  const blob = new Blob([bytes], { type: mimeType });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");

  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};
/**
 * 关闭Excel报表
 */
 const colseExcel = () => {
  Object.keys(reportVisibility).forEach((key) => {
    reportVisibility[key] = false;
  });
};
// 下载Excel
/**
 * 下载Excel报表
 */
 const downloadEXCEL = (row: any) => {
  if (!row.reportTaskId) return;

  downloadTypeName.value = "excel";
  downloadExcelName.value = row;
  reportTaskId.value = row.reportTaskId;

  // 重置报表显示状态
  colseExcel();

  // 媒体类型与报表组件映射
  const reportComponents = {
    1: { ref: googleReportRef, visibility: "google" },
    2: { ref: yandexReportRef, visibility: "yandex" },
    3: { ref: FacebookReportRef, visibility: "facebook" },
    4: { ref: TikTokReportRef, visibility: "tikTok" },
    5: { ref: bingReportRef, visibility: "bing" },
    6: { ref: linekedInReportRef, visibility: "linekedIn" },
  };

  const component = reportComponents[row.mediumType];

  if (component) {
    reportVisibility[component.visibility] = true;

    // 调用对应报表组件的下载方法
    const ref = component.ref.value;
    if (ref && typeof ref.downloadEXCEL == "function") {
      ref.downloadEXCEL(row.reportTaskId, downloadTypeName.value);
    }
  }
    // 埋点
    microForceDispatch("mixpanel", {
    key: "marketingClickCheckDownload",
    otherData: {
      media_platform: midumeTypeMapping[row.mediumType],
      download_type: "Excel",
    },
  });
};
// 关闭弹窗
const close = (formEl: FormInstance | undefined) => {
  formEl.resetFields();
  isShowReport.value = false;
};

//确认生成报表
const ok = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (debounceClick.value) return;
      debounceClick.value = true;
      generateReportLoading.value = true;
      let timeOut = setTimeout(async () => {
        debounceClick.value = false;
        clearTimeout(timeOut);
        try {
          const param = {
            ...generateReportForm.value,
            customerId: generateReportForm.value.third.split("/")[2],
            thirdCustomerId: generateReportForm.value.third.split("/")[0],
            thirdCustomerName: generateReportForm.value.third.split("/")[1],
            reportStartDate: generateReportForm.value.date[0],
            reportEndDate: generateReportForm.value.date[1],
          };
          delete param.date;
          delete param.third;
          console.log(param);
          const res: any = await generateReportInfo(param);
          generateReportLoading.value = false;
          if (res?.code == 200) {
            ElMessage.success("报表生成中，请稍等。。。");
            isShowReport.value = false;
            generateReportData();
            formEl.resetFields();
          } else {
            ElMessage.error(res.msg);
          }
        } catch (error) {
          globalThis.$sentry.captureMessage(error);
          console.log(error);
        }
      }, 1000);
    } else {
      console.log("error submit!", fields);
    }
  });
};
// 选择每页条数
const changePageSize = (val: number) => {
  console.log(val);
  page.pageSize = val;
  generateReportData();
};
//切换页码
const handleCurrentChange = (val: number) => {
  page.pageIndex = val;
  generateReportData();
};
// 切换媒体类型
const changeMediumType = () => {
  getMediaList();
};
// 更具媒体类型查询账户
const getMediaList = async () => {
  if (!generateReportForm.value.mediumType) return;
  try {
    const params = {
      mediumType: generateReportForm.value.mediumType,
    };
    const res: any = await searchMediumTypeList(params);
    if (res?.code == 200) {
      thirdList.value = res?.data;
      cloneThirdListOptions.value = res?.data?.map((item: any) => {
        return {
          ...item,
          label: item.thirdCustomerId + "/" + item.thirdCustomerName,
        };
      });
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
  }
};
// 获取媒体类型账号
</script>

<style lang="scss" scoped>
.ads-title {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 20px;
  color: #202020;
  flex: 1;
}
.zq-zuto-report {
  box-sizing: border-box;
  // padding: 20px;
  height: 100%;
  overflow-y: auto;
}
.zq-advert-search {
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  // padding-left: 20px;
  margin-top: 20px;
}

.look-report {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: auto;
  z-index: -1;
}
.container {
  height: calc(100vh - 154px);
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 20px;
}
.drawer_title {
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
}
.reportStatus-point {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}
:deep(.el-drawer__header) {
  padding-left: 24px;
  padding-top: 0px !important;
  margin: 0 !important;
  height: 57px;
}
:deep(.el-drawer__footer) {
  padding: 0 !important;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
:deep(.el-form-item) {
  display: flex;
  flex-direction: column;
}
:deep(.el-drawer__body) {
  border-bottom: 1px solid #dcdee0;
  border-top: 1px solid #dcdee0;
}
#add_dowloand {
  .el-button {
    color: #519C66;
    border: none;
    border-radius: 8px;
    background: rgba(50, 147, 111, 0.1);
    margin-right: 10px;
  }
}
</style>
