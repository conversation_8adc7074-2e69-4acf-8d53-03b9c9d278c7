<template>
  <el-dialog v-model="dialogVisible" title="批量订阅" width="50%">
    <div class="upload-header">
      {{ uploadFileInfo.success }}成功，{{ uploadFileInfo.error }}失败
    </div>
    <el-table :data="uploadFileInfo.errorList" border style="width: 100%">
      <el-table-column label="失败账户">
        <template #default="{ row }">
          {{ row.thirdCustomerName }}/{{ row.thirdCustomerId }}
        </template>
      </el-table-column>
      <el-table-column prop="errorMsg" label="失败原因" />
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, defineProps, defineExpose } from "vue";
const dialogVisible = ref(false);
defineProps({
  uploadFileInfo: {
    type: Object,
    default: () => ({}),
  },
});
const isOpen = () => {
  dialogVisible.value = true;
};
defineExpose({
  isOpen,
});
</script>
<style lang="scss" scoped>
.upload-header {
  height: 30px;
  width: 100%;
  line-height: 30px;
  background-color: rgb(251, 230, 232);
  text-indent: 20px;
  font-size: 16px;
}
.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
