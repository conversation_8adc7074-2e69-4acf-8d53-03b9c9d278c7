<template>
  <div
    class="zq-sub-body"
    v-loading="loading"
    element-loading-text="上传中，请稍等。。。"
  >
    <h2 class="ads-title f" v-if="isShowHistory">
      <el-icon class="title_icon" @click="goBackReport"  ><Back /></el-icon
      >发送记录
    </h2>
    <Breadcrumb v-else />
    <el-form
      ref="searchRef"
      :model="subscriptionForm"
      :inline="true"
      class="zq-advert-search"
    >
      <el-form-item class="search-form-item">
        <el-input
          v-model="subscriptionForm.thirdCustomerNm"
          placeholder="请输入客户名称或ID"
          style="width: 180px"
          clearable
        />
      </el-form-item>
      <!-- <el-form-item v-if="isShowHistory">
                <el-button type="primary" @click="goFatherTable" :icon="ArrowLeft">上一页</el-button>
            </el-form-item> -->
      <el-form-item v-if="!isShowHistory" class="search-form-item">
        <el-select
          v-model="subscriptionForm.subReportStatus"
          placeholder="请选择订阅状态"
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in subsriptionList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item v-if="isShowHistory">
                <el-input v-model="subscriptionForm.thirdCustomerNm" style="width: 240px" placeholder="请输入客户名称或ID"
                    clearable />
            </el-form-item> -->
      <el-form-item v-if="!isShowHistory" class="search-form-item">
        <el-select
          v-model="subscriptionForm.reportType"
          placeholder="请选择订阅报表类型"
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in subsriptionReportList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        class="search-form-item"
        :style="{ 'margin-right': isShowHistory ? '0px' : '16px' }"
      >
        <el-button type="primary" :icon="Search" @click="searchData"
          >查询</el-button
        >
      </el-form-item>
      <el-form-item class="search-form-item" v-if="!isShowHistory">
        <el-button
          type="primary"
          :icon="CirclePlus"
          @click="generateReport"
          v-if="!isShowHistory"
          :disabled="!ruleButtonAuth"
          id="report_subscription"
          >新增订阅</el-button
        >
      </el-form-item>
      <el-form-item class="search-form-item" v-if="!isShowHistory">
        <el-button
          type="primary"
          :icon="Download"
          @click="downloadTemplate"
          v-if="!isShowHistory"
          :disabled="!ruleButtonAuth"
          >下载模版</el-button
        >
      </el-form-item>
      <el-form-item style="margin-right: 0" v-if="!isShowHistory">
        <el-upload
          class="zq-upload-template"
          :show-file-list="false"
          multiple
          :http-request="uploadFile"
          :before-upload="beforeUpload"
        >
          <el-button type="primary" :icon="Upload" :disabled="!ruleButtonAuth"
            >上传文件</el-button
          >
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="container">
      <el-table
        :data="historyList"
        style="height: calc(100vh - 250px); width: 100%; min-height: 200px"
        v-if="isShowHistory"
      >
        <el-table-column prop="reportSubName" label="报表订阅名称" />
        <el-table-column prop="mediumType" label="媒体平台">
          <template #default="{ row }">
            {{ midumeTypeMapping[row.mediumType] }}
          </template>
        </el-table-column>
        <el-table-column prop="mediumType" label="广告账户名称和账户ID">
          <template #default="{ row }">
            {{ row.thirdCustomerName + "/" + row.thirdCustomerId }}
          </template>
        </el-table-column>
        <el-table-column label="报表类型">
          <template #default="{ row }">
            {{ row.reportType == 2 ? "周报" : "月报" }}
          </template>
        </el-table-column>
        <el-table-column label="报表周期">
          <template #default="{ row }">
            {{ row.reportStartDate + "至" + row.reportEndDate }}
          </template>
        </el-table-column>
        <el-table-column prop="reportEmail" label="接收邮箱" />
        <el-table-column prop="reportSendEmailDate" label="发送时间" />
        <el-table-column label="发送状态">
          <template #default="{ row }">
            {{ row.reportSendStatus == 1 ? "成功" : "失败" }}
          </template>
        </el-table-column>
      </el-table>
      <el-table
        :data="subscriptList"
        style="height: calc(100vh - 250px); width: 100%; min-height: 200px"
        v-else
      >
        <el-table-column label="订阅状态">
          <template #default="{ row }">
            <el-switch
              v-model="row.subReportStatus"
              :active-value="1"
              :inactive-value="0"
              :disabled="!ruleButtonAuth"
              @change="changeSubReportStatus(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="reportSubName" label="报表订阅名称" />
        <el-table-column label="媒体平台">
          <template #default="{ row }">
            {{ midumeTypeMapping[row.mediumType] }}
          </template>
        </el-table-column>
        <el-table-column label="订阅报表类型">
          <template #default="{ row }">
            {{ row.reportType == 2 ? "周报" : "月报" }}
          </template>
        </el-table-column>
        <el-table-column prop="subReportDate" label="新增订阅时间" />
        <el-table-column prop="thirdCustomerCount" label="广告账户信息">
          <template #default="{ row }">
            <div class="thirdCustomerCount_look">
              <el-tooltip
                effect="dark"
                placement="top"
                popper-class="custom-tooltip"
              >
                <template #content>
                  <div
                    v-for="item in row.thirdCustomerList"
                    :key="item?.customerId"
                  >
                    {{ item?.thirdCustomerId + "/" + item?.thirdCustomerName }}
                  </div>
                </template>
                <span
                  v-for="item in row.thirdCustomerList"
                  :key="item?.customerId"
                >
                  {{ item?.thirdCustomerId + "/" + item?.thirdCustomerName }}
                </span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reportEmailList" label="接收邮箱">
          <template #default="{ row }">
            <div class="thirdCustomerCount_look">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="Top Center prompts info"
                placement="top"
              >
                <template #content>
                  <div v-for="item in row.reportEmailList" :key="item">
                    {{ item }}
                  </div>
                </template>
                <span v-for="item in row.reportEmailList" :key="item">
                  {{ item }}
                </span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template #default="{ row }">
            <div class="table-operation f">
              <el-button
                class="table-button"
                :icon="Histogram"
                @click="lookHistory(row)"
                >发送记录</el-button
              >
              <el-button
                class="table-button"
                :icon="Edit"
                @click="handleEdit(row)"
                :disabled="!ruleButtonAuth"
                >编辑</el-button
              >
              <el-popover
                placement="bottom"
                width="126px"
                trigger="hover"
                popper-style="padding:0; width:124px;"
                popper-class="collapse-menu-popover-left"
              >
                <template #reference>
                  <MoreIcon />
                </template>
                <div class="popList">
                  <div class="pop_button" @click="deleteSubscription(row)">
                    删除
                  </div>
                </div>
              </el-popover>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-if="page.total"
        :total="page.total"
        :currentPage="page.pageIndex"
        :pageSize="page.pageSize"
        @update:pageSize="changePageSize"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
    <el-drawer
      v-model="isShowReport"
      size="45%"
      destroy-on-close
      @close="closeDialog"
    >
      <template #title>
        <div class="drawer_title">{{ isEdit ? "修改订阅" : "新增订阅" }}</div>
      </template>
      <el-form
        ref="subscriptionFormRef"
        :model="addSubscriptionForm"
        :rules="rules"
        label-position="left"
      >
        <el-form-item
          label="订阅报告类型"
          prop="reportType"
          style="position: relative"
        >
          <template #label>
            <label>订阅报告类型</label>
            <div class="zq-sub-form_tips">
              <el-icon><WarningFilled /></el-icon
              >月报次月2日开始发送；周报次周一开始发送
            </div>
          </template>
          <el-radio-group
            v-model="addSubscriptionForm.reportType"
            :disabled="isEdit"
          >
            <el-radio label="月报" :value="1" />
            <el-radio label="周报" :value="2" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="报告接收邮箱" prop="reportEmailList">
          <!-- <el-input v-model="addSubscriptionForm.email" placeholder="请选择接收邮箱" /> -->
          <el-select
            v-model="addSubscriptionForm.reportEmailList"
            @change="changeTrim"
            multiple
            filterable
            allow-create
            default-first-option
            remote
            persistent
            :reserve-keyword="false"
            placeholder="请选择或输入选项"
          >
          </el-select>
        </el-form-item>
        <el-form-item label="选择媒体平台" prop="mediumType">
          <el-select
            v-model="addSubscriptionForm.mediumType"
            placeholder="请选择媒体平台"
            clearable
            @change="changeMediaType"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in subsriptionMeaid"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择广告账户" prop="user">
          <el-transfer
            v-model="addSubscriptionForm.user"
            filterable
            :render-content="renderContent"
            :filter-method="filterMethod"
            filter-placeholder="请输入筛选的广告账户"
            :titles="['客户列表', '已选客户']"
            :data="transferData"
          />
        </el-form-item>
        <el-form-item label="报表订阅名称" prop="reportSubName">
          <el-input
            v-model="addSubscriptionForm.reportSubName"
            placeholder="请输入报表订阅名称"
            maxlength="32"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isShowReport = false">取消</el-button>
          <el-button
            type="primary"
            @click="okSubscription(subscriptionFormRef)"
            :loading="okSubscriptionLoading"
            >确认</el-button
          >
        </div>
      </template>
    </el-drawer>
    <UploadFileDialog
      :uploadFileInfo="uploadFileInfo"
      ref="UploadFileDialogRef"
    />
    <TourStep
      :open="open"
      :tourStep="tourPageList"
      :typeName="'publicPoolManage'"
    />
  </div>
</template>

<script setup lang="ts" name="system-user">
import { ref, reactive, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Download,
  Upload,
  CirclePlus,
  Edit,
  Histogram,
  Search,
  Back,
  Delete,
} from "@element-plus/icons-vue";
import Pagination from "@/components/Pagination/index.vue";
import {
  getSubscribeList,
  addSubscribe,
  editSubscribe,
  getSendHistoryList,
  editSubscribeState,
  subscribeUpLoadFile,
  deleteSubscribe,
} from "@/api/reportSubscriptionApi";
import type { FormInstance } from "element-plus";
import { searchMediumTypeList } from "@/api/adUserSetingApi";
import {
  TableList,
  HistoryList,
  AddSubscriptionForm,
} from "@/types/reportSubscription";
import usePermissionStore from "@/store/modules/permission";
import UploadFileDialog from "./uploadFile.vue";
import TourStep from "@/components/tourStep/index.vue";
import { microForceDispatch } from "@/utils";
import {
  subsriptionMeaid,
  subsriptionReportList,
  subsriptionList,
  midumeTypeMapping,
} from "@/utils/mapping";
import Breadcrumb from "@/components/Breadcrumb/index.vue";
import MoreIcon from "@/components/moreIcon.vue";

const guideDetail = JSON.parse(localStorage.getItem("guideDetail") || "{}");

const open = ref(false);

const tourPageList = [
  {
    target: "report_subscription",
    placement: "bottom",
    title: "生成报告",
    description: "支持按天选择任意时间范围的报告",
  },
];
const UploadFileDialogRef = ref(null);
const uploadFileInfo = ref<any>();
const permissionStore = usePermissionStore();
console.log(permissionStore?.buttonAuth["ReportSubscription"]);
// 自动化报表 操作权限
const ruleButtonAuth = computed(
  () =>
    permissionStore.buttonAuth["ReportSubscription"]?.indexOf("controls") > -1
);
const route = useRoute();
const router = useRouter();
const loading = ref(false);
const debounceClick = ref(false);
console.log(router);
const subscriptionForm = reactive({
  subReportStatus: "", //订阅状态
  reportType: "", //报告类型
  thirdCustomerNm: "", //账户名称
});
const subscriptionFormRef = ref<FormInstance>(null);
const okSubscriptionLoading = ref(false);
const addSubscriptionForm: AddSubscriptionForm = reactive({
  reportType: "",
  reportEmailList: [],
  mediumType: 1,
  user: [],
  reportSubName: "",
});
const reportSubId = ref("");
const rules = ref({
  reportType: [
    { required: true, message: "请选择订阅报告类型", trigger: "change" },
  ],
  reportEmailList: [
    {
      required: true,
      message: "请输入接收邮箱",
      trigger: ["change", "blur"],
      type: "array",
    },
  ],
  mediumType: [
    { required: true, message: "请选择媒体平台", trigger: "change" },
  ],
  user: [{ required: true, message: "请选择广告账号", trigger: "change" }],
  reportSubName: [
    { required: true, message: "请输入订阅名称", trigger: "change" },
  ],
});
const isShowHistory = ref(false);
const isShowReport = ref(false);
const transferData = ref([]);
const page = ref({
  total: 0,
  pageIndex: 1,
  pageSize: 10,
});
const historyList = ref<HistoryList[]>();
const subscriptList = ref<TableList[]>();
const isEdit = ref(false);
const isSHowTour = computed(() => {
  return guideDetail.publicPoolManage == 0 || !guideDetail.publicPoolManage;
});
onMounted(() => {
  window.parent.postMessage(
    {
      type: "CHILD_COMPONENT_MOUNTED",
      component: "MyComponent",
    },
    "*"
  );
  getSubscribeData();
});
const changeTrim = (arr: []) => {
  console.log(arr);
  addSubscriptionForm.reportEmailList = arr?.map((item: any) => item.trim());
  console.log(addSubscriptionForm.reportEmailList);
};
const deleteSubscription = (row: any) => {
  ElMessageBox({
    title: "删除",
    message: "是否删除该订阅？",
    type: "warning",
    showCancelButton: true,
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    beforeClose: async (action, instance, done) => {
      if (action === "confirm") {
        instance.confirmButtonLoading = true;
        instance.confirmButtonText = "删除中...";
        try {
          const res: any = await deleteSubscribe({
            reportSubId: row.reportSubId,
          });
          if (res?.code == 200) {
            ElMessage.success("删除成功");
            getSubscribeData();
          } else {
            ElMessage.error(res?.msg);
          }
          done();
          instance.confirmButtonLoading = false;
        } catch (error) {
          done();
          instance.confirmButtonLoading = false;
        }
      }
      done();
    },
  }).then((action) => {
    console.log(action);
  });
};
const filterMethod = (query, item) => {
  // console.log(query, 'query')
  // console.log(item, 'item')
  return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
};
const renderContent = (h, option) => {
  return h(
    "span",
    {
      class: "transfer-item",
      title: option.label, // 设置title属性
    },
    option.label
  );
};
// 获取订阅列表
const getSubscribeData = async () => {
  try {
    const param = {
      ...subscriptionForm,
      pageIndex: page.value.pageIndex,
      pageSize: page.value.pageSize,
    };
    const res: any = await getSubscribeList(param);
    if (res.code == 200) {
      subscriptList.value = res?.data?.list;
      page.value.total = res?.data?.total;
      isShowHistory.value = false;
      if (window.microApp) {
        // 判断是不是需要开启引导
        window.microApp.addDataListener((data: any) => {
          if (data.type === "openGuide" && !isSHowTour.value) {
            console.log("openGuide");
            // open.value = true;
          }
          return "";
        });
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};

// 确认新增订阅
const okSubscription = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (debounceClick.value) return;
      okSubscriptionLoading.value = true;
      debounceClick.value = true;
      let timeOut = setTimeout(async () => {
        clearTimeout(timeOut);
        debounceClick.value = false;
        try {
          var reg =
            /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
          if (
            addSubscriptionForm.reportEmailList.length > 0 &&
            addSubscriptionForm.reportEmailList.length <= 5
          ) {
            if (
              addSubscriptionForm?.reportEmailList?.some(
                (item: any) => !reg.test(item)
              )
            ) {
              ElMessage.error("请输入正确的邮箱地址");
              okSubscriptionLoading.value = false;
              return;
            }
          } else {
            ElMessage.error("最多输入5个邮箱");
            okSubscriptionLoading.value = false;
            return;
          }
          console.log(addSubscriptionForm.reportEmailList);
          const param = {
            ...addSubscriptionForm,
            thirdCustomerList: transferData.value
              ?.filter(
                (item: any, index: number) =>
                  addSubscriptionForm.user.includes(item.customerId) ||
                  addSubscriptionForm.user.includes(index)
              )
              .map((obj: any) => {
                return {
                  customerId: obj.customerId,
                  thirdCustomerId: obj.thirdCustomerId,
                  thirdCustomerName: obj.thirdCustomerName,
                };
              }),
          };
          delete param.user;
          const res: any = isEdit.value
            ? await editSubscribe({ ...param, reportSubId: reportSubId.value })
            : await addSubscribe(param);
          okSubscriptionLoading.value = false;
          if (res?.code == 200) {
            ElMessage.success(isEdit.value ? "编辑订阅成功" : "新增订阅成功");
            getSubscribeData();
            isShowReport.value = false;
          } else {
            ElMessage.error(res.msg);
            okSubscriptionLoading.value = false;
          }
        } catch (error) {
          globalThis.$sentry.captureMessage(error);
          okSubscriptionLoading.value = false;
          console.log(error);
        }
      }, 1000);
    } else {
      console.log("error submit!", fields);
    }
  });
};
// 修改订阅
const handleEdit = (row: any) => {
  console.log(row);
  isShowReport.value = true;
  isEdit.value = true;
  reportSubId.value = row.reportSubId;
  addSubscriptionForm.mediumType = row.mediumType;
  addSubscriptionForm.reportEmailList = row.reportEmailList;
  addSubscriptionForm.reportType = row.reportType;
  addSubscriptionForm.reportSubName = row.reportSubName;
  addSubscriptionForm.user = row.thirdCustomerList;
  getMeaidList(row.mediumType);
  console.log(transferData.value);
  // 埋点
  microForceDispatch("mixpanel", {
    key: "marketingClickSubscriptionEdit",
    otherData: {},
  });
};
// 关闭弹窗
const closeDialog = () => {
  isEdit.value = false;
};
// 新增订阅
const generateReport = () => {
  isShowReport.value = true;
  isEdit.value = false;
  addSubscriptionForm.mediumType = 1;
  addSubscriptionForm.reportEmailList = [];
  addSubscriptionForm.reportType = "";
  addSubscriptionForm.reportSubName = "";
  addSubscriptionForm.user = [];
  getMeaidList(addSubscriptionForm.mediumType);
  // 埋点
  microForceDispatch("mixpanel", {
    key: "marketingClickAddSubscription",
    otherData: {},
  });
};

// 发送记录查询
const searchData = () => {
  console.log(subscriptionForm);
  if (isShowHistory.value) {
    getSendList(subscriptionForm);
  } else {
    getSubscribeData();
  }
  // 埋点
  microForceDispatch("mixpanel", {
    key: "marketingClickSubscriptionQuery",
    otherData: {
      search_text: subscriptionForm?.thirdCustomerNm || "",
      generate_state:
        (subsriptionList.filter(
          (sItem) => sItem.value === subscriptionForm?.subReportStatus
        ) || [])[0]?.label || "",
      report_type:
        (subsriptionReportList.filter(
          (sItem) => sItem.value === subscriptionForm?.reportType
        ) || [])[0]?.label || "",
    },
  });
};
// 查看发送记录
const lookHistory = async (obj: any) => {
  reportSubId.value = obj.reportSubId;
  subscriptionForm.thirdCustomerNm = obj.thirdCustomerNm;
  subscriptionForm.reportType = obj.reportType;
  isShowHistory.value = true;
  getSendList(obj);
};
// 获取发送记录数据
const getSendList = async (obj: any) => {
  try {
    console.log(obj);
    const param = {
      thirdCustomerNm: obj.thirdCustomerNm,
      reportType: obj.reportType,
      reportSubId: obj.reportSubId || reportSubId.value,
      pageIndex: page.value.pageIndex,
      pageSize: page.value.pageSize,
    };

    const res: any = await getSendHistoryList(param);
    if (res.code == 200) {
      historyList.value = res?.data?.list;
      page.value.total = res?.data?.total || 0;
      page.value.pageIndex = 1;
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 修改订阅状态接口
const changeSubReportStatus = async (obj: any) => {
  console.log(obj);
  try {
    const param = {
      reportSubId: obj.reportSubId,
      subReportStatus: obj.subReportStatus,
    };
    const res: any = await editSubscribeState(param);
    if (res.code == 200) {
      ElMessage.success("修改订阅状态成功！");
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
  // 埋点
  microForceDispatch("mixpanel", {
    key: "marketingClickSubscriptionStateRenew",
    otherData: {
      media_platform:
        (subsriptionMeaid.filter((i) => i.value === obj.mediumType) || [])[0]
          ?.label || "",
    },
  });
};
// 下载模版
const downloadTemplate = () => {
  var link = document.createElement('a');
  link.href = '/file/NewTemplate.xlsx';
  link.download = 'template.xlsx';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
// 文件上传前的判断
const beforeUpload = (file: File) => {
  console.log(file.size);
  if (file.name.indexOf(".xlsx") !== -1) {
    if (file.size > 10485760) {
      ElMessage.error("文件大小不能超过10M");
      return false;
    }
    return true;
  } else {
    ElMessage.error("请上传正确模版！");
    return false;
  }
};

// 上传文件
const uploadFile = async (file) => {
  console.log(file);
  const formData = new FormData();
  formData.append("upLoadFile", file.file);
  console.log(formData);
  try {
    loading.value = true;
    const res: any = await subscribeUpLoadFile(formData);
    loading.value = false;
    if (res.code == 200) {
      if (res?.data?.error > 0) {
        uploadFileInfo.value = res?.data;
        UploadFileDialogRef.value?.isOpen();
      } else {
        ElMessage.success("上传成功！");
      }
      getSubscribeData();
    } else {
      ElMessage.warning(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 选择每页条数
const changePageSize = (val: number) => {
  console.log(val);
  page.value.pageSize = val;
  if (isShowHistory.value) {
    getSendList(subscriptionForm);
  } else {
    getSubscribeData();
  }
};
// 分页
const handleCurrentChange = (value: number) => {
  console.log(value);
  page.value.pageIndex = value;
  if (isShowHistory.value) {
    getSendList(subscriptionForm);
  } else {
    getSubscribeData();
  }
};

// 选择媒体平台
const changeMediaType = (val: number) => {
  // addSubscriptionForm.mediumType = val
  getMeaidList(val);
};

// 获取媒体类型账户
const getMeaidList = async (val: number) => {
  try {
    const params = {
      mediumType: val,
    };
    const res: any = await searchMediumTypeList(params);
    if (res?.code == 200) {
      transferData.value = res?.data?.map((item: any, index: number) => {
        return {
          ...item,
          key: index,
          label: item.companyName || item.thirdCustomerName,
        };
      });
      if (isEdit.value) {
        console.log(addSubscriptionForm.user);
        // console.log(hasCommonElement(transferData.value, addSubscriptionForm.user))
        addSubscriptionForm.user = hasCommonElement(
          transferData.value,
          addSubscriptionForm.user
        );
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    globalThis.$sentry.captureMessage(error);
    console.log(error);
  }
};
// 回显穿梭框的数据
const hasCommonElement = (arr1, arr2) => {
  var conut = [];
  for (let i = 0; i < arr1.length; i++) {
    for (let j = 0; j < arr2.length; j++) {
      if (arr1[i].thirdCustomerId === arr2[j].thirdCustomerId) {
        conut.push(i);
      }
    }
  }
  return conut;
};
const goBackReport = () => {
  isShowHistory.value = false;
  subscriptionForm.subReportStatus = "";
  subscriptionForm.reportType = "";
  subscriptionForm.thirdCustomerNm = "";
  getSubscribeData();
};
</script>
<style>
.custom-tooltip {
  max-height: 200px !important; /* 设置最小高度 */
  overflow-y: auto !important;
}
</style>
<style lang="scss" scoped>
.zq-sub-body {
  box-sizing: border-box;
  // padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.zq-advert-search {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  // padding-left: 20px;
}

.zq-sub-form_tips {
  display: flex;
  align-items: center;
  margin-left: 110px;
  color: #909399;
  position: absolute;
  font-weight: 400;
}

.zq-sub-breadcrumb {
  margin: 15px 0;
}

.zq-upload-template {
  display: flex;
  // margin-left: 12px;
}

::v-deep(.el-upload) {
  height: 32px;
}
.search-form-item {
  margin-right: 16px;
}
.ads-title {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #202020;
  // margin-bottom: 20px;
  // flex: 1;
  display: flex;
  align-items: center;
  min-width: 80px;
  .title_icon {
    margin-right: 8px;
  }
  .title_icon:hover {
    color: var(--el-color-primary);
    cursor: pointer;
  }
}

.container {
  height: calc(100vh - 174px);
}
.thirdCustomerCount_look {
  height: 30px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 20px;
}
:deep(.el-drawer__header) {
  padding-left: 24px;
  padding-top: 0px !important;
  margin: 0 !important;
  height: 57px;
}
:deep(.el-drawer__footer) {
  padding: 0 !important;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
:deep(.el-form-item) {
  display: flex;
  flex-direction: column;
}
:deep(.el-drawer__body) {
  border-bottom: 1px solid #dcdee0;
  border-top: 1px solid #dcdee0;
}
.drawer_title {
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
}
.hover-effect {
  background-color: #e0f7fa !important;
  border-color: #4fc3f7 !important;
  color: #007bb5 !important;
}
.table-operation {
  .el-button {
    color: #519c66;
    border: none;
    border-radius: 8px;
    background: rgba(50, 147, 111, 0.1);
    margin: 0 10px 0 0 !important;

    &:last-child {
      color: #cc5f5f;
      background: rgba(255, 141, 141, 0.1);
    }
  }
}
</style>
