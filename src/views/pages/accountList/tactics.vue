<template>
  <h2 class="ads-title">
    <el-icon size="24" @click="goBack()"><Back /></el-icon>
    <span>策略配置</span>
  </h2>
  <div class="tacticsMain">
    <el-form
      ref="ruleFormRef"
      :model="allData"
      class="demo-ruleForm"
      status-icon
      label-position="top"
    >
      <el-form-item
        label="策略名称: "
        prop="configName"
        :rules="{
          required: true,
          message: '请输入策略名称',
          trigger: 'blur',
        }"
      >
        <!-- <el-input
          v-model="allData.configName"
          placeholder="账户余额不足预警配置"
          style="width: 500px"
          readonly
        /> -->
        <div>账户余额不足预警配置</div>
      </el-form-item>
      <el-form-item
        label="策略应用范围: "
        prop="platformType"
        :rules="{
          required: true,
          message: '请选择策略应用范围',
          trigger: 'change',
        }"
      >
        <el-radio-group v-model="allData.platformType">
          <el-radio value="1">全媒体平台</el-radio>
          <el-radio value="2">单媒体平台</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="触发规则: "
        prop="accountRuleRequestListAll"
        :rules="{
          required: true,
          message: '请输入触发规则',
          trigger: 'blur',
        }"
        v-if="allData.platformType === '1'"
      >
        <div class="ruleDiv">
          账户余额小于最近
          <el-input-number
            v-model="allData.accountRuleRequestListAll"
            :min="0"
            :precision="0"
            style="width: 100px; margin-left: 6px"
            :controls="false"
          />
          <span class="ruleDivDay">天</span>
          消耗平均值
        </div>
      </el-form-item>
      <el-form-item
        label="触发规则: "
        prop="accountRuleRequestList"
        :rules="{
          type: 'array',
          required: true,
          message: '请选择触发规则',
          trigger: 'change',
        }"
        v-if="allData.platformType === '2'"
      >
        <el-checkbox-group v-model="allData.accountRuleRequestList">
          <el-checkbox value="Google" name="type"> Google </el-checkbox>
          <el-checkbox value="Meta" name="type"> Meta </el-checkbox>
          <el-checkbox value="Tiktok" name="type"> Tiktok </el-checkbox>
          <el-checkbox value="Yandex" name="type"> Yandex </el-checkbox>
          <el-checkbox value="Bing" name="type"> Bing </el-checkbox>
          <el-checkbox value="LinkedIn" name="type"> LinkedIn </el-checkbox>
        </el-checkbox-group>
        <div
          class="accountRuleRequest"
          v-if="
            allData.accountRuleRequestList &&
            allData.accountRuleRequestList.length &&
            allData.accountRuleRequestList.length > 0
          "
        >
          <div
            class="accountRuleRequestRow"
            v-for="(item, index) in allData.accountRuleRequestList"
            :key="index"
          >
            <div class="rowType">{{ item }}</div>
            <div class="rowDesc">当账户余额小于</div>
            <el-input-number
              v-model="
                accountRuleRequestList[
                  accountRuleRequestList.findIndex(
                    (i) => i.mediumTypeName == item
                  )
                ].threshold
              "
              :min="0"
              :precision="0"
              style="width: 100px; margin-left: 6px"
              :controls="false"
            />
            <div class="rowUnit">
              {{
                accountRuleRequestList[
                  accountRuleRequestList.findIndex(
                    (i) => i.mediumTypeName == item
                  )
                ].currency
              }}
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="触发状态: " prop="triggerStatus">
        <el-switch v-model="allData.triggerStatus" />
      </el-form-item>
      <el-form-item
        label="预警方式: "
        prop="notificationType"
        :rules="{
          type: 'array',
          required: true,
          message: '请选择预警方式',
          trigger: 'change',
        }"
        v-if="allData.triggerStatus"
      >
        <el-checkbox-group v-model="allData.notificationType">
          <el-checkbox value="1" name="type"> 站内消息 </el-checkbox>
          <el-checkbox value="2" name="type"> 邮件预警 </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item
        label="收件邮箱: "
        prop="receiverMailbox"
        :rules="[
          {
            required: true,
            message: '请输入收件邮箱',
            trigger: 'blur',
          },
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: 'change',
          },
        ]"
        v-if="allData.triggerStatus && allData.notificationType.includes('2')"
      >
        <el-input
          v-model="allData.receiverMailbox"
          placeholder="请输入预警通知收件邮箱"
          style="width: 500px"
        />
      </el-form-item>
    </el-form>
  </div>
  <div class="tacticsFoot">
    <el-button @click="goBack()">取消</el-button>
    <el-button type="primary" @click="confirm(ruleFormRef)">保存配置</el-button>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import { ElLoading } from "element-plus";
import { getTactics, setTactics } from "@/api/tactics";

const ruleFormRef = ref<FormInstance>();
// 表单数据
let allData = ref<any>({
  configName: "账户余额不足预警配置", // 策略名称
  platformType: "1", // 策略应用范围
  triggerStatus: true, // 触发状态
  notificationType: ["1"], // 预警方式
  receiverMailbox: "", // 收件邮箱
  accountRuleRequestListAll: 0, // 触发规则
  accountRuleRequestList: [], // 触发规则
});
// 单平台触发规则
let accountRuleRequestList = ref([
  {
    triggerDays: 0, // 触发天数
    mediumType: 1, // 媒体类型
    mediumTypeName: "Google", // 媒体类型名称
    threshold: 0, // 触发阈值
    currency: "CNY", // 币种
  },
  {
    triggerDays: 0, // 触发天数
    mediumType: 3, // 媒体类型
    mediumTypeName: "Meta", // 媒体类型名称
    threshold: 0, // 触发阈值
    currency: "USD", // 币种
  },
  {
    triggerDays: 0, // 触发天数
    mediumType: 4, // 媒体类型
    mediumTypeName: "Tiktok", // 媒体类型名称
    threshold: 0, // 触发阈值
    currency: "USD", // 币种
  },
  {
    triggerDays: 0, // 触发天数
    mediumType: 2, // 媒体类型
    mediumTypeName: "Yandex", // 媒体类型名称
    threshold: 0, // 触发阈值
    currency: "USD", // 币种
  },
  {
    triggerDays: 0, // 触发天数
    mediumType: 5, // 媒体类型
    mediumTypeName: "Bing", // 媒体类型名称
    threshold: 0, // 触发阈值
    currency: "USD", // 币种
  },
  {
    triggerDays: 0, // 触发天数
    mediumType: 6, // 媒体类型
    mediumTypeName: "LinkedIn", // 媒体类型名称
    threshold: 0, // 触发阈值
    currency: "CNY", // 币种
  },
]);
// 返回
const goBack = () => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "routerGo",
      path: "/ads/accountManage/account-list",
      name: "ads",
      parentPath: "/ads/accountManage/account-list",
      isChild: false,
      go: -1,
    });
  }
};
// 保存接口
const setTacticsData = async (params: any) => {
  const loading = ElLoading.service({
    lock: true,
    text: "保存中！",
  });
  try {
    let { code, msg } = await setTactics(params);
    if (code && code == 200) {
      ElMessage({
        message: msg,
        type: "success",
      });
      goBack();
    } else {
      ElMessage({
        message: msg,
        type: "error",
      });
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.close();
  }
};
// 确定按钮
const confirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      let params = {
        accountWarnRequest: {
          configName: "账户余额不足预警配置",
          platformType: allData.value.platformType,
          triggerStatus: allData.value.triggerStatus ? 1 : 0,
          notificationType: allData.value.notificationType.join(","),
          receiverMailbox: allData.value.receiverMailbox,
        },
        accountRuleRequestList: [],
      };
      if (allData.value.platformType == "1") {
        params.accountRuleRequestList.push({
          triggerDays: allData.value.accountRuleRequestListAll,
        });
      } else {
        allData.value.accountRuleRequestList.forEach((item: any) => {
          let index = accountRuleRequestList.value.findIndex(
            (i) => i.mediumTypeName == item
          );
          if (index > -1) {
            params.accountRuleRequestList.push({
              triggerDays: accountRuleRequestList.value[index].triggerDays,
              mediumType: accountRuleRequestList.value[index].mediumType,
              amountThreshold: accountRuleRequestList.value[index].threshold,
              currency: accountRuleRequestList.value[index].currency,
            });
          }
        });
      }
      setTacticsData(params);
    } else {
      console.log("error submit!", fields);
    }
  });
};
const getData = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: "加载中！",
  });
  try {
    let { code, data } = await getTactics();
    if (code && code == 200 && data && data.accountWarnResp) {
      data.accountWarnResp.triggerStatus =
        data.accountWarnResp.triggerStatus == 1 ? true : false;
      data.accountWarnResp.notificationType =
        data.accountWarnResp.notificationType.split(",");
      data.accountWarnResp.platformType = data.accountWarnResp.platformType
        ? data.accountWarnResp.platformType + "" // 兼容老数据
        : "1";
      if (data.accountWarnResp.platformType == "1") {
        data.accountWarnResp.accountRuleRequestListAll =
          data.accountRuleRespList[0].triggerDays;
      } else {
        if (data.accountRuleRespList && data.accountRuleRespList.length > 0) {
          data.accountWarnResp.accountRuleRequestList = [];
          data.accountRuleRespList.forEach((item: any) => {
            let mediumTypeName =
              item.mediumType == 1
                ? "Google"
                : item.mediumType == 2
                ? "Yandex"
                : item.mediumType == 3
                ? "Meta"
                : item.mediumType == 4
                ? "Tiktok"
                : item.mediumType == 5
                ? "Bing"
                : item.mediumType == 6
                ? "LinkedIn"
                : "";
            item.mediumTypeName = mediumTypeName;
            data.accountWarnResp.accountRuleRequestList.push(mediumTypeName);
            let index = accountRuleRequestList.value.findIndex(
              (i) => i.mediumType == item.mediumType
            );
            if (index > -1) {
              accountRuleRequestList.value[index].triggerDays =
                item.triggerDays;
              accountRuleRequestList.value[index].threshold = item.threshold;
              accountRuleRequestList.value[index].currency = item.currency;
            }
          });
          // accountRuleRequestList.value = data.accountRuleRespList;
        }
      }
      data.accountWarnResp.configName = "账户余额不足预警配置";
      allData.value = data.accountWarnResp;
    } else {
      allData.value = {
        configName: "账户余额不足预警配置", // 策略名称
        platformType: "1", // 策略应用范围
        triggerStatus: true, // 触发状态
        notificationType: ["1"], // 预警方式
        receiverMailbox: "", // 收件邮箱
        accountRuleRequestListAll: 0, // 触发规则
        accountRuleRequestList: [], // 触发规则
      };
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.close();
  }
};
onMounted(() => {
  getData();
});
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: PingFangSC-Medium;
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  cursor: pointer;
  display: flex;
  align-items: center;
  span {
    flex: 1;
    margin-left: 10px;
  }
}
.tacticsMain {
  width: 100%;
  max-height: calc(100% - 140px);
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
  box-sizing: border-box;
  padding: 20px;
  overflow-y: auto;
}
.tacticsFoot {
  width: 100%;
  height: 60px;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;
}
:deep(.el-form-item__label) {
  color: #333333;
}
.ruleDiv {
  display: flex;
  align-items: center;
  color: #a8abb2;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC";
  .ruleDivDay {
    color: #303133;
    margin: 0 6px;
  }
}
.accountRuleRequest {
  width: 100%;
  display: flex;
  flex-direction: column;
  .accountRuleRequestRow {
    display: flex;
    align-items: center;
    margin-top: 10px;
    .rowType {
      width: 50px;
      color: #303133;
      font-size: 14px;
      font-weight: 400;
      font-family: "PingFang SC";
    }
    .rowDesc {
      color: #a8abb2;
      font-size: 14px;
      font-weight: 400;
      font-family: "PingFang SC";
      margin: 0 6px 0 12px;
    }
    .rowUnit {
      color: #303133;
      font-size: 14px;
      font-weight: 400;
      font-family: "PingFang SC";
      margin-left: 10px;
    }
  }
}
</style>
