/**
 * @description 清零管理列表请求参数属性枚举
 */
export enum EFormPropKeys {
  // 广告账户名称/ID
  ACCOUNT_NAME_OR_ID = "thirdAccountName",
  // 申请清零时间
  APPLY_TIME = "applyTime",
  // 媒体
  MEDIA = "mediumType",
}

/**
 * @description 清零管理列表表格列属性枚举
 */
export enum ETableColumnKeys {
  // 清零订单号
  ORDER_NO = "resetOrder",
  // 清零媒体
  MEDIA = "mediumType",
  // 清零广告账户名称
  ACCOUNT_NAME = "thirdAccountName",
  // 清零广告账户名称ID
  ACCOUNT_ID = "thirdAccountId",
  // 清零币种
  CURRENCY = "currency",
  // 清零金额
  CLEARING_AMOUNT = "amount",
  // 清零状态
  CLEARING_STATUS = "state",
  // 汇率
  EXCHANGE_RATE = "exchangeRate",
  // 清零完成时间
  CLEARING_TIME = "resetEndTime",
  // 申请清零时间
  APPLY_TIME = "resetStateTime",
}

/**
 * @description 清零管理类型项枚举
 */
export enum EZeroClearingType {
  // 清零记录
  CLEARING_RECORD = "clearingRecord",
  // 清零进度
  CLEARING_PROGRESS = "clearingProgress",
}
