/**
 * @description 充值管理列表请求参数属性枚举
 */
export enum EFormPropKeys {
  // 广告账户名称
  ACCOUNT_NAME = "thirdAccountName",
  // 申请充值时间
  APPLY_TIME = "applyTime",
  // 充值状态
  RECHARGE_STATUS = "state",
  // 媒体
  MEDIA = "mediumType",
}

/**
 * @description 充值管理列表表格列属性枚举
 */
export enum ETableColumnKeys {
  // 媒体
  MEDIA = "mediumType",
  // 广告账户名称
  NAME = "thirdAccountName",
  // 广告账户ID
  ID = "thirdAccountId",
  // 账户币种
  ACCOUNT_CURRENCY = "accountCurrency",
  // 到账金额
  ARRIVAL_AMOUNT = "realAmount",
  // 汇率
  EXCHANGE_RATE = "exchangeRate",
  // 完成充值时间
  COMPLETE_TIME = "rechargeEndTime",
  // 充值币种
  RECHARGE_CURRENCY = "currency",
  // 充值金额
  RECHARGE_AMOUNT = "amount",
  // 充值状态
  RECHARGE_STATUS = "state",
  // 充值订单号
  ORDER_NO = "rechargeOrder",
  // 申请充值时间
  APPLY_TIME = "rechargeStateTime",
}

/**
 * @description 充值管理类型项枚举
 */
export enum ERechargeType {
  // 充值记录
  RECHARGE_RECORD = "rechargeRecord",
  // 充值进度
  RECHARGE_PROGRESS = "rechargeProgress",
}
