/**
 * @description 开户账户列表请求参数属性枚举
 */
export enum EFormPropKeys {
  // 开户主体
  SUBJECT = "serviceCode",
  // 账户状态
  STATUS = "state",
}

/**
 * @description 开户账户列表表格列属性枚举
 */
export enum ETableColumnKeys {
  // 广告账户名称
  NAME = "thirdAccountName",
  // 广告账户ID
  ID = "thirdAccountId",
  // 账户状态
  STATUS = "state",
  // 开户主体
  SUBJECT = "openName",
  // 币种
  CURRENCY = "currency",
  // 余额
  BALANCE = "remainAmount",
}

/**
 * @description 开户账户列表操作属性枚举
 */
export enum ETableActionKeys {
  // 充值
  RECHARGE = "recharge",
  // 转账
  TRANSFER = "transfer",
  // 清零
  CLEAR = "clear",
  // 转款
  TRANSFER_MONEY = "transferMoney",
  // 预算调整
  BUDGET_ADJUST = "budgetAdjust",
}
