/**
 * @description 充值管理列表请求参数属性枚举
 */
export enum EFormPropKeys {
  // 广告账户名称/ID
  ACCOUNT_NAME_OR_ID = "thirdAccountName",
  // 申请转款时间
  APPLY_TIME = "applyTime",
  // 转入媒体
  TRANSFER_IN_MEDIA = "inMediumType",
  // 转出媒体
  TRANSFER_OUT_MEDIA = "outMediumType",
  // 转款状态
  TRANSFER_STATUS = "state",
}

/**
 * @description 充值管理列表表格列属性枚举
 */
export enum ETableColumnKeys {
  // 转账订单号
  TRANSFER_ORDER_NO = "transferNm",
  // 转出媒体
  TRANSFER_OUT_MEDIA = "outMediumType",
  // 转出广告账户名称
  TRANSFER_OUT_ACCOUNT_NAME = "outAccountName",
  // 转出广告账户ID
  TRANSFER_OUT_ACCOUNT_ID = "outAccountId",
  // 转出币种
  TRANSFER_OUT_CURRENCY = "outCurrency",
  // 转出金额
  TRANSFER_OUT_AMOUNT = "outAmount",
  // 转入媒体
  TRANSFER_IN_MEDIA = "inMediumType",
  // 转入广告账户名称
  TRANSFER_IN_ACCOUNT_NAME = "inAccountName",
  // 转入广告账户ID
  TRANSFER_IN_ACCOUNT_ID = "inAccountId",
  // 转入币种
  TRANSFER_IN_CURRENCY = "inCurrency",
  // 转入金额
  TRANSFER_IN_AMOUNT = "inAmount",
  // 到账金额
  ARRIVAL_AMOUNT = "inRealAmount",
  // 转款状态
  TRANSFER_STATUS = "state",
  // 汇率
  EXCHANGE_RATE = "inExchangeRate",
  // 转款到账时间
  ARRIVAL_TIME = "transferEndTime",
  // 申请转款时间
  APPLY_TIME = "transferStateTime",
}

/**
 * @description 跨媒体转账类型项枚举
 */
export enum EMediaTransferType {
  // 转账进度
  TRANSFER_PROGRESS = "transferProgress",
  // 转账记录
  TRANSFER_RECORD = "transferRecord",
}
