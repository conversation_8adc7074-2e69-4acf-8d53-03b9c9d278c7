<template>
  <RouterView v-if="currentRoute.name !== 'AccountList'" />
  <div class="ad-account-list" v-else>
    <div class="title">
      <Breadcrumb />
    </div>
    <!-- 媒体渠道余额组件 -->
    <MediaChannel />
    <!-- 媒体渠道tab组件 -->
    <mediaTabs ref="mediaTabsRef" @tab-click="mediaChange" />
    <!-- 动态表格组件  -->
    <DynamicTable
      ref="dyTableRef"
      :form-items="formItems"
      :columns="columns"
      :initial-data="tableData"
      dy-table-radius="0 0 8px 8px"
      backgroundColor="#f5f5fa"
      max-height="360px"
      v-loading="loading"
      @search="getListData"
      @sort-change="tableSortChange"
      v-if="authLength > 0"
    >
      <template #formBtn>
        <el-button
          type="primary"
          @click="exportExcel"
          v-if="
            permissionStore?.buttonAuth?.AccountList?.indexOf(tabAuth) != -1
          "
        >
          导出
        </el-button>
      </template>
      <template #middle>
        <div
          class="forewarning"
          v-if="
            permissionStore?.buttonAuth?.AccountList?.indexOf(tabAuth) != -1
          "
        >
          <div class="forewarningDesc">
            {{ warnStr }}
          </div>
          <div class="forewarningBtnList" id="balance_alarm">
            <div
              class="forewarningBtn"
              @click="goTactics('tactics')"
              v-if="isShowForewarningBtn"
            >
              {{ warnStatus ? "修改余额预警策略配置" : "余额预警策略配置" }}
            </div>
            <div
              class="forewarningBtn"
              v-if="warnStatus"
              @click="changeWarnStatus()"
            >
              停用现有策略
            </div>
            <div
              class="forewarningBtn"
              v-if="warnStatus"
              @click="goTactics('tacticsLog')"
            >
              配置记录
            </div>
          </div>
        </div>
      </template>
      <template #headerSlot="{ row }">
        <div style="display: flex; align-items: center">
          {{ row.label }}
          <el-tooltip
            effect="dark"
            content="余额每日0点更新，非实时数据"
            placement="top"
          >
            <el-icon style="margin-left: 5px"><Warning /></el-icon>
          </el-tooltip>
        </div>
      </template>
      <template #custom="{ row }">
        <!-- 余额 -->
        <div class="tooltip-div">
          <div>
            {{ row[ETableColumnKeys.BALANCE] }}
          </div>
          <div v-if="row.freezeAmount && row.freezeAmount != 0">
            冻结金额：{{ row.freezeAmount }}
          </div>
        </div>
      </template>
    </DynamicTable>
    <component
      :is="currentDialog"
      v-model:visible="dialogVisible"
      :loading="detailLoading"
      :mediaType="currentMedia"
      :account-data="currentAccountDetail"
      @upData="upData"
    />
    <Drawer
      :editInfo="editInfo"
      :drawerTitle="drawerTitle"
      :drawerTitleTwo="drawerTitleTwo"
      :isShowBudgetDay="isShowBudgetDay"
      :drawerType="drawerType"
      :mediumType="mediumType"
      @close="close"
      @submit="submit"
      @getList="getListData(saveParams)"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, shallowRef, onMounted, computed, h } from "vue";
import MediaChannel from "./components/list/mediaChannel.vue";
import mediaTabs from "./components/list/mediaTabs.vue";
import DynamicTable from "@/components/dynamicTable.vue";
import AccountTopUp from "./components/list/accountTopup.vue";
import TransferMoney from "./components/list/transferMoney.vue";
import ZeroClearing from "./components/list/zeroClearing.vue";
import {
  ElSelect,
  ElButton,
  ElTooltip,
  ElMessage,
  ElLoading,
} from "element-plus";
import { EFormPropKeys, ETableColumnKeys, ETableActionKeys } from "./enum/list";
import {
  getAccountOptionListApi,
  getAccountList,
  getAccountDetail,
} from "@/api/accountListApi";
import { downloadFile, getQueryParamsByKey } from "../../../utils/common";
import configLocation from "../../../config";
import usePermissionStore from "@/store/modules/permission";
import { useRoute } from "vue-router";
import { getTacticsLog, stopTactics } from "@/api/tactics";
import Drawer from "./components/drawer.vue";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const isShowForewarningBtn = computed(() => {
  return localStorage.getItem("applicationList").indexOf("ADS_AUTO") > -1;
});
// 转款、预算调整 -- start
let editInfo = ref({
  mediaType: null,
  thirdAccountId: "",
  remainAmount: "",
  currency: "",
  budgetDay: "",
  accountInfoId: "",
});
let drawerTitle = ref("");
let drawerTitleTwo = ref("");
let isShowBudgetDay = ref(false);
let drawerType = ref(1);
let mediumType = ref(1);
const close = () => {
  isShowBudgetDay.value = false;
};
const submit = () => {
  isShowBudgetDay.value = false;
};
// 转款、预算调整 -- end
const currentRoute = useRoute();
const permissionStore = usePermissionStore();
let authLength = computed(() => {
  let index = 0;
  if (
    permissionStore &&
    permissionStore.buttonAuth &&
    permissionStore.buttonAuth.AccountList
  ) {
    permissionStore.buttonAuth.AccountList.forEach((item: any) => {
      if (item === "AccountGoogleView") {
        index++;
      }
      if (item === "AccountYandexView") {
        index++;
      }
      if (item === "AccountFacebookView") {
        index++;
      }
      if (item === "AccountTikTokView") {
        index++;
      }
      if (item === "AccountBingView") {
        index++;
      }
      if (item === "AccountLinkView") {
        index++;
      }
    });
  } else {
    index = 0;
  }
  return index;
});
// loading
const loading = ref(false);
// 对话框flag
const dialogVisible = ref(false);
// 当前对话框实例
const currentDialog = shallowRef(null);
// 媒体tab组件实列
const mediaTabsRef = ref<InstanceType<typeof mediaTabs>>(null);
// 当前媒体类型
const currentMedia = ref(getQueryParamsByKey("mediaType") || 1);
// 表格组件实例
const dyTableRef = ref<InstanceType<typeof DynamicTable>>(null);
// 开户主体列表
const subjectList = ref([]);
// 当前账户id
const currentAccountId = ref("");
// 当前账户详情
const currentAccountDetail = ref({});
// 账户详情展示loading
const detailLoading = ref(false);
// 记录查询参数数据
const saveParams = ref({});

// 动态表单查询配置
const formItems = computed(() => [
  {
    label: "开户主体",
    prop: EFormPropKeys.SUBJECT,
    component: ElSelect,
    placeholder: "请选择",
    props: {
      selectOptions: subjectList.value,
    },
  },
  {
    label: "账户状态",
    prop: EFormPropKeys.STATUS,
    component: ElSelect,
    placeholder: "请选择",
    props: {
      selectOptions: [
        {
          label: "正常",
          value: 1,
        },
        {
          label: "禁用",
          value: 2,
        },
      ],
    },
  },
]);

// 动态表格列配置
const columns = computed(() => {
  return [
    {
      label: "广告账户名称",
      prop: ETableColumnKeys.NAME,
    },
    {
      label: "广告账户ID",
      prop: ETableColumnKeys.ID,
    },
    {
      label: "账户状态",
      prop: ETableColumnKeys.STATUS,
      formatter: (row) => {
        return row.state === 1 ? "正常" : "冻结";
      },
    },
    {
      label: "开户主体",
      prop: ETableColumnKeys.SUBJECT,
    },
    {
      label: "币种",
      prop: ETableColumnKeys.CURRENCY,
    },
    {
      label: "余额",
      prop: ETableColumnKeys.BALANCE,
      sortable: "custom",
      type: "custom",
    },
    {
      label: "操作",
      fixed: "right",
      prop: (row) => listButtonNode(row),
      width: 188,
      headerType: "custom",
    },
  ].filter((item) => {
    return true;
  });
});

// 表格数据
const tableData = ref([]);

// 按钮操作回调
const rowHandler = (type, row) => {
  currentAccountId.value = row.accountInfoId;
  switch (type) {
    case ETableActionKeys.RECHARGE:
      currentDialog.value = AccountTopUp;
      dialogVisible.value = true;
      break;
    case ETableActionKeys.TRANSFER:
      currentDialog.value = TransferMoney;
      dialogVisible.value = true;
      break;
    case ETableActionKeys.CLEAR:
      currentDialog.value = ZeroClearing;
      dialogVisible.value = true;
      break;
    case ETableActionKeys.TRANSFER_MONEY:
      editInfo.value = row;
      isShowBudgetDay.value = true;
      drawerTitle.value = "转款";
      drawerTitleTwo.value = "选择转入账户信息";
      drawerType.value = 3;
      break;
    case ETableActionKeys.BUDGET_ADJUST:
      editInfo.value = row;
      isShowBudgetDay.value = true;
      drawerTitle.value = "调整预算";
      drawerTitleTwo.value = "申请调整日预算";
      drawerType.value = 1;
      break;
  }
  getAccountDetailData(row.accountInfoId);
};
let tabAuth = ref("");
const btnListConfigs = ref([
  {
    label: "充值",
    callback: rowHandler,
    type: ETableActionKeys.RECHARGE,
  },
  {
    label: "减款",
    callback: rowHandler,
    type: ETableActionKeys.TRANSFER,
  },
  {
    label: "清零",
    callback: rowHandler,
    type: ETableActionKeys.CLEAR,
  },
]) as any;
// render表格操作列按钮节点
const listButtonNode = (row) => {
  let btnListConfigsList = [];
  if (
    row.source == 3 &&
    permissionStore?.buttonAuth?.AccountList?.indexOf(tabAuth.value) != -1
  ) {
    btnListConfigsList = btnListConfigs.value;
  }
  if (
    row.source == 1 &&
    permissionStore?.buttonAuth?.AccountList?.indexOf(tabAuth.value) != -1
  ) {
    btnListConfigsList = [
      {
        label: "转款",
        callback: rowHandler,
        type: ETableActionKeys.TRANSFER_MONEY,
        color: "#519C66",
        bgColor: "rgba(50, 147, 111, 0.1)"
      },
      {
        label: "调整预算",
        callback: rowHandler,
        type: ETableActionKeys.BUDGET_ADJUST,
        color: "#519C66",
        bgColor: "rgba(50, 147, 111, 0.1)"
      },
    ];
  }
  return h(
    "div",
    {},
    btnListConfigsList.map((item, index) =>
      h(
        ElButton,
        {
          class: "table-button",
          type: "primary",
          onClick: () => item.callback(item.type, row),
          permission: tabAuth.value,
        },
        () => item.label
      )
    )
  );
};

// 获取开户主体数据
const getSubjectList = () => {
  getAccountOptionListApi({}).then((res: any) => {
    if (Array.isArray(res.data) && res.data.length > 0) {
      subjectList.value = res.data.map((item) => {
        return {
          label: item.customerMainName,
          value: item.customerInfoId,
        };
      });
    }
  });
};

const formatListParams = (data) => {
  let params = {};
  if (data) {
    params = {
      pageIndex: data.currentPage,
      pageSize: data.pageSize,
      [EFormPropKeys.SUBJECT]: data[EFormPropKeys.SUBJECT],
      [EFormPropKeys.STATUS]: data[EFormPropKeys.STATUS],
      mediumType: currentMedia.value,
      orderBy: data.orderBy || "DESC",
    };
  } else {
    params = {
      pageIndex: 1,
      pageSize: 10,
      mediumType: currentMedia.value,
    };
  }
  saveParams.value = params;
  return params;
};

// 获取账户列表数据
const getListData = (data?: object) => {
  loading.value = true;
  const params = formatListParams(data);
  getAccountList(params)
    .then((res: any) => {
      if (res && res.code === "200" && res.data) {
        loading.value = false;
        tableData.value = res.data.list;
        dyTableRef.value.setPageTotal(res.data.total);
      } else {
        loading.value = false;
        tableData.value = [];
        dyTableRef.value.setPageTotal(0);
      }
    })
    .catch((err) => {
      loading.value = false;
      console.log(err, "err");
    });
};

// 获取账户详情数据
const getAccountDetailData = (id) => {
  detailLoading.value = true;
  currentAccountDetail.value = {};
  getAccountDetail({
    accountInfoId: id,
  })
    .then((res: any) => {
      detailLoading.value = false;
      currentAccountDetail.value = res.data;
    })
    .catch((err) => {
      detailLoading.value = false;
      console.log(err, "err");
    });
};

// excel数据导出
const exportExcel = () => {
  downloadFile({
    url: configLocation.baseURL + "/ad-trade-web/account/viewDownload",
    params: saveParams.value,
    method: "POST",
    headers: {},
    name: "账户列表",
  });
};

// 重置媒体类型
const resetMediaType = () => {
  const mediaType = getQueryParamsByKey("mediaType") as any;
  if (mediaType) {
    mediaTabsRef.value.setCurrentMedia(mediaType);
  }
};
// 处理排序变更
const tableSortChange = ({ prop, order }) => {
  console.log("tableSortChange", prop, order);
  // 默认当order为null时，转换为ascending排序
  // 这样用户只会在ascending和descending之间切换
  const effectiveOrder = order; //|| "ascending"
  console.log("effectiveOrder", effectiveOrder);
  let orderBy = "DESC";
  if (effectiveOrder === "ascending") {
    orderBy = "ASC";
  } else if (effectiveOrder === "descending") {
    orderBy = "DESC";
  }
  const params = {
    ...saveParams.value,
    orderBy: orderBy,
  };
  getListData(params);
};
// 跳转策略配置记录
const goTactics = (path) => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: "/ads/accountManage/account-list/" + path,
      name: "ads",
      parentPath: "/ads/:page*",
      isChild: true,
    });
  }
};
let configId = ref("");
let platformType = ref("1");
let warnStr = ref("");
let threshold = ref("");
let currency = ref("");
let warnStatus = ref(false);
let accountRuleRespList = ref([]);
// 媒体类型切换
const mediaChange = (val) => {
  tabAuth.value = val.btnAuth;
  currentMedia.value = val.type;
  if (platformType.value == "2") {
    warnStr.value = "";
    accountRuleRespList.value.forEach((item) => {
      if (item.mediumType == currentMedia.value) {
        threshold.value = item.threshold;
        currency.value = item.currency;
        warnStr.value = `当账户余额小于${threshold.value}${currency.value}触发预警`;
      }
    });
  }
  dyTableRef.value.resetForm();
};
// 策略状态
const getWarnData = async () => {
  try {
    let { code, data } = await getTacticsLog();
    if (code && code == 200 && data) {
      warnStatus.value = data.triggerStatus == 1;
      if (!warnStatus.value) {
        warnStr.value = "";
        return;
      }
      accountRuleRespList.value = data.ruleRespList;
      platformType.value = data.platformType + "";
      configId.value = data.id;
      if (data.platformType == "1") {
        if (!data.ruleRespList || data.ruleRespList.length == 0) {
          warnStr.value = "";
          return;
        }
        // 全媒体
        warnStr.value = `账户余额小于最近${data.ruleRespList[0].triggerDays}天消耗平均值触发余额预警`;
      } else {
        if (!data.ruleRespList || data.ruleRespList.length == 0) {
          warnStr.value = "";
          return;
        }
        // 单媒体
        accountRuleRespList.value.forEach((item) => {
          if (item.mediumType == currentMedia.value) {
            threshold.value = item.threshold;
            currency.value = item.currency;
          }
        });
        warnStr.value = `当账户余额小于${threshold.value}${currency.value}触发预警`;
      }
    } else {
      warnStr.value = "";
      warnStatus.value = false;
    }
  } catch (error) {
    warnStr.value = "";
    warnStatus.value = false;
    console.error(error);
  }
};
// 预警启用停用
const changeWarnStatus = async () => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: "停用中...",
    });
    let { code, msg } = await stopTactics({
      id: configId.value,
      triggerStatus: 0,
    });
    if (code && code == 200) {
      ElMessage({
        type: "success",
        message: msg,
      });
      getWarnData();
      loading.close();
    } else {
      ElMessage({
        type: "error",
        message: msg,
      });
      loading.close();
    }
  } catch (error) {
    console.log(error);
  }
};
// 更新数据
const upData = (val) => {
  getListData(saveParams.value);
};
let authList = ref([
  "AccountGoogleView",
  "AccountYandexView",
  "AccountFacebookView",
  "AccountTikTokView",
  "AccountBingADSView",
  "AccountLinkInADSView",
  "AccountVkADSView",
  "AccountPinADSView",
]);
onMounted(() => {
  if (currentRoute.name && currentRoute.name === "AccountList") {
    authList.value.forEach((item, index) => {
      if (
        permissionStore?.buttonAuth?.AccountList?.indexOf(item) != -1 &&
        tabAuth.value == ""
      ) {
        // View改成Controls
        tabAuth.value = item.replace("View", "Controls");
        if (currentMedia.value == 1 && item != "AccountGoogleView") {
          currentMedia.value = index + 1;
        }
      }
    });
    console.log("authList", authList.value, tabAuth.value);
    resetMediaType();
    getListData();
    getSubjectList();
    getWarnData();
    window.parent.postMessage(
      {
        type: "CHILD_COMPONENT_MOUNTED",
        component: "MyComponent",
      },
      "*"
    );
  }
});
</script>

<style lang="scss" scoped>
.ad-account-list {
  .title {
    color: #202020;
    font-size: 20px;
    font-weight: 500;
    font-family: "PingFang SC";
    margin-bottom: 20px;
  }
}
.forewarning {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  .forewarningDesc {
    color: #333333;
    font-size: 14px;
    font-weight: 500;
    font-family: "PingFang SC";
  }
  .forewarningBtnList {
    display: flex;
    align-items: center;
    .forewarningBtn {
      color: var(--el-color-primary);
      font-size: 14px;
      font-weight: 500;
      font-family: "PingFang SC";
      text-decoration: underline;
      cursor: pointer;
      margin: 0 12px;
    }
  }
}
</style>
