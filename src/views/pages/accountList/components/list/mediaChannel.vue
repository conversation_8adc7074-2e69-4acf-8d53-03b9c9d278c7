<template>
  <div class="media-channel">
    <div class="media-channel-list">
      <div
        v-for="item in showMediaChannelList"
        :key="item.name"
        class="media-channel-item"
      >
        <div class="media-channel-item__title">
          <div class="media-channel-item__icon">
            <img :src="item.icon" alt="" class="iconImg" />
          </div>
          <div class="media-channel-item__name">{{ item.name }}</div>
        </div>
        <div class="media-channel-item__balance">
          <div class="media-channel-item__balance__symbol">
            {{ item.currency }}
          </div>
          <div class="media-channel-item__balance__value">
            {{ item.balance || "--"}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getAccountBalanceSummary } from "@/api/accountListApi";
import iconMeta from "@/assets/images/home/<USER>";
import iconGoogle from "@/assets/images/home/<USER>";
import iconTikTok from "@/assets/images/home/<USER>";
import iconBing from "@/assets/images/home/<USER>";
import iconYandex from "@/assets/images/home/<USER>";
import iconLink from "@/assets/images/home/<USER>";
import iconVKontakte from "@/assets/images/home/<USER>";
import iconPinterest from "@/assets/images/home/<USER>";
import usePermissionStore from "@/store/modules/permission";

const permissionStore = usePermissionStore();
let showMediaChannelList = ref([]) as any;
const mediaChannelList = ref([
  {
    type: "google",
    name: "Google Ads",
    currency: "¥",
    balance: 0.0,
    key: "googleRemainAmount",
    icon: iconGoogle,
    auth: "AccountGoogleView",
  },
  {
    type: "yandex",
    name: "Yandex",
    currency: "$",
    balance: 0.0,
    key: "yandexRemainAmount",
    icon: iconYandex,
    auth: "AccountYandexView",
  },
  {
    type: "meta",
    name: "Meta",
    currency: "$",
    balance: 0.0,
    key: "facebookRemainAmount",
    icon: iconMeta,
    auth: "AccountFacebookView",
  },
  {
    type: "tiktok",
    name: "TikTok",
    currency: "$",
    balance: 0.0,
    key: "tiktokRemainAmount",
    icon: iconTikTok,
    auth: "AccountTikTokView",
  },
  {
    type: "bing",
    name: "Bing Ads",
    currency: "$",
    balance: 0.0,
    key: "bingRemainAmount",
    icon: iconBing,
    auth: "AccountBingADSView",
  },
  {
    type: "linkedln",
    name: "LinkedIn",
    currency: "¥",
    balance: 0.0,
    key: "linkedinRemainAmount",
    icon: iconLink,
    auth: "AccountLinkInADSView",
  },
  {
    type: "VKontakte",
    name: "VKontakte",
    currency: "$",
    balance: 0.0,
    key: "VkontakteRemainAmount",
    icon: iconVKontakte,
    auth: "AccountVkADSView",
  },
  {
    type: "pinterest",
    name: "Pinterest",
    currency: "$",
    balance: 0.0,
    key: "pinterestRemainAmount",
    icon: iconPinterest,
    auth: "AccountPinADSView",
  }
]);
mediaChannelList.value.forEach((item) => {
  if (permissionStore?.buttonAuth?.AccountList?.indexOf(item.auth) != -1) {
    showMediaChannelList.value.push(item);
  }
});
// 获取账户余额汇总数据
const getAccountBalanceSummaryData = () => {
  getAccountBalanceSummary({}).then((res: any) => {
    const balanceData = res.data;
    showMediaChannelList.value.forEach((item) => {
      item.balance = balanceData[item.key];
    });
  });
};

onMounted(() => {
  getAccountBalanceSummaryData();
});
</script>

<style scoped lang="scss">
.media-channel {
  width: 100%;
  height: auto;
  margin-bottom: 20px;

  .media-channel-list {
    width: 100%;
    height: 98px;
    display: flex;

    .media-channel-item {
      flex: 1;
      background-color: #ffffff;
      margin-right: 15px;
      border-radius: 8px;
      padding: 10px 10px 0;
      &:last-child {
        margin-right: 0;
      }

      .media-channel-item__title {
        display: flex;
        align-items: center;
        padding: 5px 8px 0;
        margin-bottom: 10px;
        .media-channel-item__icon {
          display: flex;
          align-items: center;
          margin-right: 8px;
          .iconImg {
            width: 24px;
            height: 24px;
          }
        }
        .media-channel-item__name {
          color: #333333;
          font-size: 14px;
          font-weight: 600;
        }
      }

      .media-channel-item__balance {
        display: flex;
        align-items: baseline;
        padding: 0 8px;
        justify-content: end;
        .media-channel-item__balance__symbol {
          color: #333333;
          font-size: 14px;
          font-weight: 600;
          margin-right: 4px;
        }
        .media-channel-item__balance__value {
          color: #333333;
          font-size: 24px;
          font-weight: 700;
        }
      }
    }
  }
}
</style>
