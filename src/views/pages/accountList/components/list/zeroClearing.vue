<template>
  <CustomDialog
    ref="customDialogRef"
    :visible="visible"
    top="20vh"
    title="清零"
    width="571px"
    @update:visible="handleClose"
    @confirm="accountClearZeroApply"
  >
    <template #bodyContent>
      <div class="body-content" v-loading="loading">
        <div class="zero-clearing-account-information">
          <div class="account-information-title">核对清零账户信息</div>
          <div class="account-information-content">
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">媒体：</div>
              <div class="account-information-content-item-value">
                {{ formatterMediaTypeInList(accountData.mediumType) }}
              </div>
            </div>
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">
                广告账户名称/ID：
              </div>
              <div class="account-information-content-item-value">
                {{ accountData.thirdAccountName }}
                {{
                  accountData.thirdAccountId &&
                  `(${accountData.thirdAccountId})`
                }}
              </div>
            </div>
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">币种：</div>
              <div class="account-information-content-item-value">
                {{ accountData.currency }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CustomDialog>
  <el-dialog v-model="okDia" width="500" center>
    <div class="my-dialog">
      <el-icon color="#47bc91" size="40"><SuccessFilled /></el-icon>
      <div class="diaTitle">提交成功</div>
      <div class="diaDesc">清零大约需1~2个工作日完成，请耐心等待</div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="goProgress()"> 查看进度 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, withDefaults, defineProps, defineEmits } from "vue";
import CustomDialog from "../common/customDialog.vue";
import { accountClearZero } from "@/api/accountListApi";
import { formatterMediaTypeInList } from "@/utils/formatter";
import { ElMessage } from "element-plus";

const props = withDefaults(
  defineProps<{
    visible: boolean;
    loading: boolean;
    accountData: any;
  }>(),
  {
    visible: false,
    loading: false,
    accountData: () => {},
  }
);
const okDia = ref(false);
const emits = defineEmits(["update:visible", "upData"]);

const customDialogRef = ref<InstanceType<typeof CustomDialog>>(null);

const handleClose = () => {
  emits("update:visible", false);
};

// 账户清零申请
const accountClearZeroApply = () => {
  if (!props.accountData.remainAmount || props.accountData.remainAmount <= 0) {
    ElMessage({
      message: "账户余额不足",
      type: "error",
    });
    return;
  }
  if (props.loading) return;

  if (props.accountData && props.accountData.freezeAmount > 0) {
    ElMessage.warning("账户存在冻结金额，暂不能清零");
    return;
  }
  customDialogRef.value.setSubmitLoading(true);
  accountClearZero({
    mediumType: props.accountData.mediumType,
    merchantId: props.accountData.merchantId,
    customerId: props.accountData.customerId,
    walletAccountId: props.accountData.walletAccountId,
    accountInfoId: props.accountData.accountInfoId,
    type: 1,
    thirdAccountId: props.accountData.thirdAccountId,
    thirdAccountName: props.accountData.thirdAccountName,
    companyName: props.accountData.companyName,
  })
    .then((res: any) => {
      customDialogRef.value.setSubmitLoading(false);
      if (res && res.code == 200) {
        emits("upData");
        handleClose();
        console.log("res", res);
        okDia.value = true;
        // 三秒后自动关闭弹窗
        setTimeout(() => {
          okDia.value = false;
        }, 3000);
      } else {
        ElMessage({
          message: res.msg,
          type: "error",
        });
      }
    })
    .catch((err) => {
      console.log(err, "err--");
      customDialogRef.value.setSubmitLoading(false);
    });
};
const goProgress = () => {
  okDia.value = false;
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: `/ads/accountManage/deductionHistory?accountName=${props.accountData.thirdAccountName}`,
      name: "ads",
      parentPath: "/ads/workbench",
      isChild: false,
    });
  }
};
</script>

<style lang="scss">
.body-content {
  padding: 0 18px;

  .zero-clearing-account-information {
    padding: 22px 6px 0;

    .account-information-title {
      color: #333333;
      font-size: 14px;
      font-weight: 600;
    }

    .account-information-content {
      padding: 12px 0 20px;

      .account-information-content-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .account-information-content-item-label {
          flex: 1;
          color: #333333;
          font-size: 14px;
          font-weight: 400;
          text-align: right;
        }

        .account-information-content-item-value {
          flex: 2;
          color: #333333;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
