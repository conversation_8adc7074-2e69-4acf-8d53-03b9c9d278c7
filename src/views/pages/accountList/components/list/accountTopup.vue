<template>
  <CustomDialog
    ref="customDialogRef"
    :visible="visible"
    title="充值"
    top="15vh"
    width="571px"
    @update:visible="handleClose"
    @confirm="handleRechargeApply"
  >
    <template #bodyContent>
      <div class="body-content" v-loading="loading">
        <div class="account-information">
          <div class="account-information-title">核对账户信息</div>
          <div class="account-information-content">
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">媒体：</div>
              <div class="account-information-content-item-value">
                {{ formatterMediaTypeInList(accountData.mediumType) }}
              </div>
            </div>
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">
                广告账户名称/ID：
              </div>
              <div class="account-information-content-item-value">
                {{ accountData.thirdAccountName }}
                {{
                  accountData.thirdAccountId &&
                  `(${accountData.thirdAccountId})`
                }}
              </div>
            </div>
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">币种：</div>
              <div class="account-information-content-item-value">
                {{ accountData.currency }}
              </div>
            </div>
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">
                账户余额：
              </div>
              <div class="account-information-content-item-value">
                {{ accountData.remainAmount }}
              </div>
            </div>
          </div>
        </div>
        <div class="recharge-amount">
          <div class="recharge-amount-title">
            充值金额
            <!-- <span class="tip">单次充值金额不得低于 7000CNY</span> -->
          </div>
          <div class="wallet-balance">
            <div class="wallet-balance-label">钱包余额：</div>
            <div class="wallet-balance-value">
              <span class="amount-symbol">¥</span>
              <span class="amount-sum">
                {{ accountData.walletRemainAmount || 0 }}
              </span>
            </div>
          </div>
          <div class="recharge-amount-content">
            <el-input
              placeholder="请输入充值金额"
              v-model="rechargeAmount"
              @focus="onFocusHandel"
              @input="filterDecimal"
            >
              <template #append><span>CNY</span></template>
            </el-input>
          </div>
        </div>
      </div>
    </template>
    <template #footerContent>
      <div class="footer-content">共计：{{ rechargeAmount }} CNY</div>
    </template>
  </CustomDialog>
  <el-dialog v-model="okDia" width="500" center>
    <div class="my-dialog">
      <el-icon color="#47bc91" size="40"><SuccessFilled /></el-icon>
      <div class="diaTitle">提交成功</div>
      <div class="diaDesc">充值大约需1~2个工作日完成，请耐心等待</div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="goProgress()"> 查看进度 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { withDefaults, defineProps, defineEmits, ref, watch } from "vue";
import CustomDialog from "../common/customDialog.vue";
import { formatterMediaTypeInList } from "../../../../../utils/formatter";
import { accountRechargeApply } from "@/api/accountListApi";
import { ElMessage } from "element-plus";

const props = withDefaults(
  defineProps<{
    visible: boolean;
    loading: boolean;
    mediaType: number;
    accountData: any;
  }>(),
  {
    visible: false,
    loading: false,
    mediaType: 1,
    accountData: () => {},
  }
);

// 重置上次输入金额
watch(
  () => props.visible,
  (val) => {
    if (!val) {
      rechargeAmount.value = 0;
    }
  }
);

const customDialogRef = ref<InstanceType<typeof CustomDialog>>(null);

const rechargeAmount = ref(0) as any;

const emits = defineEmits(["update:visible", "upData"]);

const handleClose = () => {
  emits("update:visible", false);
};

const onFocusHandel = () => {
  if (rechargeAmount.value == 0) {
    rechargeAmount.value = "";
  }
};

const filterDecimal = () => {
  rechargeAmount.value = rechargeAmount.value.replace(/[^0-9.]/g, ""); // 只允许数字和小数点
  rechargeAmount.value = rechargeAmount.value.replace(/^\.+/, ""); // 不能以小数点开头
  rechargeAmount.value = rechargeAmount.value.replace(/\.{2,}/g, "."); // 不能有多个小数点
  rechargeAmount.value = rechargeAmount.value.replace(/(\.\d*)\./g, "$1"); // 只能有一个小数点
};

const okDia = ref(false);
// 充值申请
const handleRechargeApply = () => {
  if (props.loading) return;

  // if (rechargeAmount.value < 7000) {
  //   ElMessage.error("单次充值金额不得低于 7000CNY");
  //   return;
  // }

  customDialogRef.value.setSubmitLoading(true);
  accountRechargeApply({
    ...props.accountData,
    amount: rechargeAmount.value,
  })
    .then((res: any) => {
      customDialogRef.value.setSubmitLoading(false);
      if (res && res.code == 200) {
        emits("upData");
        handleClose();
        console.log("res", res);
        okDia.value = true;
        // 三秒后自动关闭弹窗
        setTimeout(() => {
          okDia.value = false;
        }, 3000);
      } else {
        ElMessage({
          message: res.msg,
          type: "error",
        });
      }
    })
    .catch((err) => {
      console.log(err, "err");
      customDialogRef.value.setSubmitLoading(false);
    });
};
const goProgress = () => {
  okDia.value = false;
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: `/ads/accountManage/rechargeHistory?accountName=${props.accountData.thirdAccountName}`,
      name: "ads",
      parentPath: "/ads/workbench",
      isChild: false,
    });
  }
};
</script>

<style lang="scss">
.body-content {
  padding: 0 18px;

  .account-information {
    padding: 22px 6px 0;

    .account-information-title {
      color: #333333;
      font-size: 14px;
      font-weight: 600;
    }

    .account-information-content {
      padding: 12px 0 20px;
      border-bottom: 1px solid #dcdee0;

      .account-information-content-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .account-information-content-item-label {
          flex: 1;
          color: #333333;
          font-size: 14px;
          font-weight: 400;
          text-align: right;
        }

        .account-information-content-item-value {
          flex: 2;
          color: #333333;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  .recharge-amount {
    .recharge-amount-title {
      color: #333333;
      font-size: 14px;
      font-weight: 600;
      padding-top: 20px;

      .tip {
        color: #666666;
        font-size: 12px;
        font-weight: 400;
      }
    }

    .wallet-balance {
      padding-top: 17px;
      display: flex;
      align-items: center;

      .wallet-balance-label {
        color: #333333;
        font-size: 14px;
        font-weight: 400;
      }

      .wallet-balance-value {
        display: flex;
        align-items: baseline;

        .amount-symbol {
          color: #333333;
          font-size: 14px;
          font-weight: 400;
          margin-right: 6px;
        }

        .amount-sum {
          color: #333333;
          font-size: 20px;
          font-weight: 500;
        }
      }
    }

    .recharge-amount-content {
      padding-bottom: 26px;
      padding-top: 12px;
      .el-input {
        width: 253px;
        height: 41px;
        .el-input__wrapper {
          border: none;
        }

        .el-input-group__append {
          width: 47px;
          border: none;
          background-color: transparent;

          span {
            color: #333333;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

.footer-content {
  color: #333333;
  font-size: 16px;
  font-weight: 600;
}
.my-dialog {
  width: 100%;
  text-align: center;
  .diaTitle {
    font-size: 20px;
    font-weight: 600;
    color: #333333;
    margin-top: 10px;
  }
  .diaDesc {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    margin-top: 10px;
  }
}
</style>
