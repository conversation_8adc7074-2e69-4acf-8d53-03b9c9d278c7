<template>
  <CustomDialog
    ref="customDialogRef"
    :visible="visible"
    top="8vh"
    width="571px"
    title="减款"
    @update:visible="handleClose"
    @confirm="handleTransferApply"
  >
    <template #bodyContent>
      <div class="body-content" v-loading="loading">
        <div class="roll-out-account-information">
          <div class="account-information-title">核对账户信息</div>
          <div class="account-information-content">
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">媒体：</div>
              <div class="account-information-content-item-value">
                {{ formatterMediaTypeInList(accountData.mediumType) }}
              </div>
            </div>
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">
                广告账户名称/ID：
              </div>
              <div class="account-information-content-item-value">
                {{ accountData.thirdAccountName }}
                {{
                  accountData.thirdAccountId &&
                  `(${accountData.thirdAccountId})`
                }}
              </div>
            </div>
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">币种：</div>
              <div class="account-information-content-item-value">
                {{ accountData.currency }}
              </div>
            </div>
            <div class="account-information-content-item">
              <div class="account-information-content-item-label">
                账户余额：
              </div>
              <div class="account-information-content-item-value">
                {{ accountData.remainAmount }}
              </div>
            </div>
          </div>
        </div>
        <div class="transfer-amount">
          <div class="transfer-amount-title">申请减款金额</div>
          <div class="recharge-amount-content">
            <el-input
              placeholder="请输入转款金额"
              v-model="transferAmount"
              @focus="onFocusHandel"
              @input="filterDecimal"
            >
              <template #append
                ><span>{{ accountData.currency }}</span></template
              >
            </el-input>
          </div>
          <div class="transfer-tips">
            <div class="tip">
              减款/清零以实际执行金额为准，申请提交后将在1～2个工作日内完成。
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #footerContent>
      <div class="footer-content">
        共计：{{ transferAmount || 0 }} {{ accountData.currency }}
      </div>
    </template>
  </CustomDialog>
  <el-dialog v-model="okDia" width="500" center>
    <div class="my-dialog">
      <el-icon color="#47bc91" size="40"><SuccessFilled /></el-icon>
      <div class="diaTitle">提交成功</div>
      <div class="diaDesc">减款大约需1~2个工作日完成，请耐心等待</div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="goProgress()"> 查看进度 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  ref,
  withDefaults,
  defineProps,
  defineEmits,
  reactive,
  watch,
  computed,
} from "vue";
import CustomDialog from "../common/customDialog.vue";
import { EMediaType } from "../../enum/components";
import { formatterMediaTypeInList } from "@/utils/formatter";
import { getCustomerAccountList, accountClearZero } from "@/api/accountListApi";
import { ElMessage } from "element-plus";

const props = withDefaults(
  defineProps<{
    visible: boolean;
    loading: boolean;
    accountData: any;
  }>(),
  {
    visible: false,
    loading: false,
    accountData: () => {},
  }
);
const okDia = ref(false);
const emits = defineEmits(["update:visible", "upData"]);

const customDialogRef = ref<InstanceType<typeof CustomDialog>>(null);

// 媒体列表
const mediaList = computed(() => {
  return [
    {
      label: "Google",
      value: EMediaType.GOOGLE,
      currency: "CNY",
    },
    {
      label: "Yandex",
      value: EMediaType.YANDEX,
      currency: "USD",
    },
    {
      label: "Meta",
      value: EMediaType.META,
      currency: "USD",
    },
    {
      label: "TikTok",
      value: EMediaType.TIKTOK,
      currency: "USD",
    },
    {
      label: "Bing",
      value: EMediaType.BING,
      currency: "USD",
    },
    {
      label: "LinkedIn",
      value: EMediaType.LINKEDIN,
      currency: "CNY",
    },
  ].filter((item) => {
    return item.value !== currentMediaType.value;
  });
});

// 当前媒体类型
const currentMediaType = ref(props.accountData.mediaType);
// 广告账户列表
const accountList = ref([]);
// 广告账户下拉选配置
const accountOptions = ref([]);
// 当前转入账户信息
const transferToAccount = ref({}) as any;

const entryInformationFrom = reactive({
  inMediumType: "",
  inAccountId: "",
  inCurrency: "CNY",
}) as any;

watch(
  () => mediaList.value,
  (val) => {
    if (Array.isArray(val) && val.length > 0) {
      entryInformationFrom.inMediumType = val[0].value;
      entryInformationFrom.inCurrency = val[0].currency;
    }
  }
);

watch(
  () => props.accountData,
  (val) => {
    if (val && JSON.stringify(val) !== "{}") {
      currentMediaType.value = val.mediumType;
      getCustomerAccountListHandel();
    }
  }
);

// 重置上次输入金额 && 选择的广告账户
watch(
  () => props.visible,
  (val) => {
    if (!val) {
      entryInformationFrom.inAccountId = "";
      transferAmount.value = 0;
    }
  }
);

// 转款金额
const transferAmount = ref(0) as any;

const handleClose = () => {
  emits("update:visible", false);
};

const onFocusHandel = () => {
  if (transferAmount.value == 0) {
    transferAmount.value = "";
  }
};

const filterDecimal = () => {
  transferAmount.value = transferAmount.value.replace(/[^0-9.]/g, ""); // 只允许数字和小数点
  transferAmount.value = transferAmount.value.replace(/^\.+/, ""); // 不能以小数点开头
  transferAmount.value = transferAmount.value.replace(/\.{2,}/g, "."); // 不能有多个小数点
  transferAmount.value = transferAmount.value.replace(/(\.\d*)\./g, "$1"); // 只能有一个小数点
};

// 媒体类型切换
const mediaChange = (val: number) => {
  // 获取对应媒体的币种
  entryInformationFrom.inCurrency = mediaList.value.find(
    (item) => item.value === val
  )?.currency;
  // 根据媒体类型获取广告账户列表
  accountOptions.value = accountList.value.filter(
    (item) => item.mediumType === val
  );
  // 重置上次选择的广告账户
  entryInformationFrom.inAccountId = "";
};

// 转入账户信息切换
const transferToAccountChange = (val) => {
  transferToAccount.value = accountOptions.value.find(
    (item) => item.thirdAccountId === val
  );
  console.log(transferToAccount.value, "transferToAccount.value---");
};

// 获取客户账户列表
const getCustomerAccountListHandel = () => {
  getCustomerAccountList({
    serviceCode: props.accountData.serviceCode,
  }).then((res: any) => {
    accountList.value = res.data;
    accountOptions.value = accountList.value.filter(
      (item) => item.mediumType === entryInformationFrom.inMediumType
    );
  });
};

// 申请转款请求参数 formatter
const accountTransferApplyParams = () => {
  let params = {
    mediumType: props.accountData.mediumType,
    merchantId: props.accountData.merchantId,
    customerId: props.accountData.customerId,
    walletAccountId: props.accountData.walletAccountId,
    accountInfoId: props.accountData.accountInfoId,
    type: 2,
    reductionAmount: transferAmount.value,
    thirdAccountId: props.accountData.thirdAccountId,
    thirdAccountName: props.accountData.thirdAccountName,
    companyName: props.accountData.companyName,
  };
  console.log(params, "params---");
  return params;
};

const handleTransferApply = () => {
  if (!props.accountData.remainAmount || props.accountData.remainAmount <= 0) {
    ElMessage({
      message: "账户余额不足",
      type: "error",
    });
    return;
  }
  if (props.loading) return;
  customDialogRef.value.setSubmitLoading(true);
  accountClearZero(accountTransferApplyParams())
    .then((res: any) => {
      customDialogRef.value.setSubmitLoading(false);
      if (res && res.code == 200) {
        emits("upData");
        handleClose();
        console.log("res", res);
        okDia.value = true;
        // 三秒后自动关闭弹窗
        setTimeout(() => {
          okDia.value = false;
        }, 3000);
      } else {
        ElMessage({
          message: res.msg,
          type: "error",
        });
      }
    })
    .catch((err) => {
      console.log(err, "err");
      customDialogRef.value.setSubmitLoading(false);
    });
};
const goProgress = () => {
  okDia.value = false;
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: `/ads/accountManage/deductionHistory?accountName=${props.accountData.thirdAccountName}`,
      name: "ads",
      parentPath: "/ads/workbench",
      isChild: false,
    });
  }
};
</script>

<style lang="scss">
.body-content {
  padding: 0 24px;

  .roll-out-account-information {
    padding-top: 22px;

    .account-information-title {
      color: #333333;
      font-size: 14px;
      font-weight: 600;
    }

    .account-information-content {
      padding: 12px 0 20px;
      border-bottom: 1px solid #dcdee0;

      .account-information-content-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .account-information-content-item-label {
          flex: 1;
          color: #333333;
          font-size: 14px;
          font-weight: 400;
          text-align: right;
        }

        .account-information-content-item-value {
          flex: 2;
          color: #333333;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  .transfer-to-account-information {
    padding-top: 19px;
    .account-information-title {
      color: #333333;
      font-size: 14px;
      font-weight: 600;
    }

    .account-information-content {
      padding-bottom: 3px;
      border-bottom: 1px solid #dcdee0;

      .el-form {
        padding-top: 12px;
        .el-form-item {
          .el-form-item__label {
            color: #333333;
            font-size: 14px;
            font-weight: 400;
            padding-right: 0;
          }
          .el-form-item__content {
            .el-input,
            .el-select {
              width: 382px;
              height: 32px;
            }
          }
        }
      }
    }
  }

  .transfer-amount {
    .transfer-amount-title {
      color: #333333;
      font-size: 14px;
      font-weight: 600;
      padding-top: 20px;

      .tip {
        color: #666666;
        font-size: 12px;
        font-weight: 400;
      }
    }

    .wallet-balance {
      padding-top: 17px;
      display: flex;
      align-items: center;

      .wallet-balance-label {
        color: #333333;
        font-size: 14px;
        font-weight: 400;
      }

      .wallet-balance-value {
        display: flex;
        align-items: baseline;

        .amount-symbol {
          color: #333333;
          font-size: 14px;
          font-weight: 400;
          margin-right: 6px;
        }

        .amount-sum {
          color: #333333;
          font-size: 20px;
          font-weight: 500;
        }
      }
    }

    .recharge-amount-content {
      padding-top: 12px;
      .el-input {
        width: 253px;
        height: 41px;

        .el-input__wrapper {
          border: none;
        }

        .el-input-group__append {
          width: 47px;
          border: none;
          background-color: transparent;

          span {
            color: #333333;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
    .transfer-tips {
      padding-top: 8px;
      margin-bottom: 26px;

      .tip {
        color: #666666;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
      }
    }
  }
}

.footer-content {
  color: #333333;
  font-size: 16px;
  font-weight: 600;
}
</style>
