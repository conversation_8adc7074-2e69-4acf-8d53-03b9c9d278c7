<template>
  <div class="media-tabs">
    <el-tabs v-model="activeMedia" class="el-tabs" @tab-click="handleClick">
      <template v-for="item in showMediaTabs" :key="item.name">
        <el-tab-pane :name="item.name">
          <template #label>
            <span class="custom-tabs-label">
              <img :src="item.icon" alt="" class="iconImg" />
              <span>{{ item.name }}</span>
            </span>
          </template>
        </el-tab-pane>
      </template>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, defineExpose } from "vue";
import { MediaType, EMediaType } from "../../enum/components";
import iconMeta from "@/assets/images/home/<USER>";
import iconGoogle from "@/assets/images/home/<USER>";
import iconTikTok from "@/assets/images/home/<USER>";
import iconBing from "@/assets/images/home/<USER>";
import iconYandex from "@/assets/images/home/<USER>";
import iconLink from "@/assets/images/home/<USER>";
import iconVKontakte from "@/assets/images/home/<USER>";
import iconPinterest from "@/assets/images/home/<USER>";
import usePermissionStore from "@/store/modules/permission";

const permissionStore = usePermissionStore();

const emits = defineEmits(["tabClick"]);
const props = defineProps({
  // 是否显示新媒体
  showNewMedia: {
    type: Boolean,
    default: true,
  },
  noProsime: {
    type: Boolean,
    default: false,
  },
});

let showMediaTabs = ref([]) as any;
const activeMedia = ref("Google Ads");
const mediaTabs = computed(() => [
    {
      name: MediaType.GOOGLE,
      icon: iconGoogle,
      type: EMediaType.GOOGLE,
      auth: "AccountGoogleView",
      btnAuth: "AccountGoogleControls",
    },
    {
      name: MediaType.YANDEX,
      icon: iconYandex,
      type: EMediaType.YANDEX,
      auth: "AccountYandexView",
      btnAuth: "AccountYandexControls",
    },
    {
      name: MediaType.META,
      icon: iconMeta,
      type: EMediaType.META,
      auth: "AccountFacebookView",
      btnAuth: "AccountFacebookControls",
    },
    {
      name: MediaType.TIKTOK,
      icon: iconTikTok,
      type: EMediaType.TIKTOK,
      auth: "AccountTikTokView",
      btnAuth: "AccountTikTokControls",
    },
    {
      name: MediaType.BING,
      icon: iconBing,
      type: EMediaType.BING,
      auth: "AccountBingADSView",
      btnAuth: "AccountBingADSControls",
    },
    {
      name: MediaType.LINKEDIN,
      icon: iconLink,
      type: EMediaType.LINKEDIN,
      auth: "AccountLinkInADSView",
      btnAuth: "AccountLinkInADSControls",
    },
    {
      name: MediaType.VKONTAKTE,
      icon: iconVKontakte,
      type: EMediaType.VKONTAKTE,
      auth: "AccountVkADSView",
      btnAuth: "AccountVkADSControls",
    },
    {
      name: MediaType.PINTEREST,
      icon: iconPinterest,
      type: EMediaType.PINTEREST,
      auth: "AccountPinADSView",
      btnAuth: "AccountPinADSControls",
    }
  ].filter((item) => {
    // 如果不显示新媒体，则过滤掉VKONTAKTE和Pinterest
    if (!props.showNewMedia) {
      return item.name !== MediaType.VKONTAKTE && item.name !== MediaType.PINTEREST;
    } else {
      return true;
    }
  })
)

//  需要权限时
if (!props.noProsime) {
  mediaTabs.value.forEach((item) => {
    if (permissionStore?.buttonAuth?.AccountList?.indexOf(item.auth) != -1) {
      showMediaTabs.value.push(item);
    }
  });
} else {
  showMediaTabs.value = mediaTabs.value;
}
activeMedia.value = showMediaTabs.value[0]?.name;

const mediaEnumMAp = {
  [MediaType.GOOGLE]: 1,
  [MediaType.YANDEX]: 2,
  [MediaType.META]: 3,
  [MediaType.TIKTOK]: 4,
  [MediaType.BING]: 5,
  [MediaType.LINKEDIN]: 6,
  [MediaType.VKONTAKTE]: 7,
  [MediaType.PINTEREST]: 8,
};

const setCurrentMedia = (mediaType: MediaType) => {
  activeMedia.value =
    mediaTabs.value.find((item: any) => item.type == mediaType)?.name ||
    "Google Ads";
};

const handleClick = (e) => {
  console.log("e", e.props, e.props.name);
  const name = e.props.name;
  const auth = mediaTabs.value.find((item: any) => item.name == name)?.auth;
  const btnAuth = mediaTabs.value.find(
    (item: any) => item.name == name,
  )?.btnAuth;
  emits("tabClick", { type: mediaEnumMAp[name], auth: auth, btnAuth: btnAuth });
};

defineExpose({
  setCurrentMedia,
});
</script>

<style scoped lang="scss">
.media-tabs {
  .el-tabs {
    width: 100%;
    height: 52px;
    padding-left: 20px;
    border-radius: 8px 8px 0 0;
    background-color: #ffffff;

    :deep(.el-tabs__header) {
      height: 100%;
      margin: 0;
      .el-tabs__nav-wrap {
        height: 100%;
        .el-tabs__nav-scroll {
          height: 100%;
          .el-tabs__nav {
            height: 100%;
          }
          .el-tabs__item {
            height: 100%;
          }
        }
        .el-tabs__nav-prev,
        .el-tabs__nav-next {
          line-height: 52px;
        }
      }
    }

    .custom-tabs-label {
      display: flex;
      align-items: center;

      .iconImg {
        width: 18px;
        height: 18px;
        margin-right: 4px;
      }
    }
  }
}
</style>
