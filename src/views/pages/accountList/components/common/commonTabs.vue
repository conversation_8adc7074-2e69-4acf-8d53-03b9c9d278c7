<template>
  <div class="common-tabs">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <template v-for="item in tabList" :key="item.name">
        <el-tab-pane :label="item.label" :name="item.name" />
      </template>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, withDefaults, defineProps, defineEmits } from "vue";

interface TabList {
  label: string;
  name: string;
}

const props = withDefaults(
  defineProps<{
    tabList: TabList[];
  }>(),
  {
    tabList: () => [],
  }
);

const activeName = ref(props.tabList.length ? props.tabList[0].name : "");

const emits = defineEmits(["tabsChange"]);

const handleClick = (tab: any) => {
  const { name } = tab.props;
  emits("tabsChange", name);
};
</script>

<style scoped lang="scss">
.common-tabs {
  width: 100%;
  height: 46px;
  padding: 0 20px;
  :deep(.el-tabs) {
    height: 100%;
    .el-tabs__header {
      height: 100%;
      margin: 0;
      .el-tabs__nav-wrap,
      .el-tabs__nav-scroll,
      .el-tabs__nav,
      .el-tabs__item {
        height: 100%;
      }
    }
  }
}
</style>
