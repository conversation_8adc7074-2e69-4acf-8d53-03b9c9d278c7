<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    :top="top"
    :width="width"
    modal-class="custom-l-dialog"
    destroy-on-close
    append-to-body
    :before-close="handleClose"
  >
    <div class="dialog-body">
      <slot name="bodyContent"></slot>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div class="content-slot">
          <slot name="footerContent"></slot>
        </div>
        <div class="controls-slot">
          <slot name="footerControls">
            <el-button @click="handleClose">取消</el-button>
            <el-button
              v-loading="submitLoading"
              type="primary"
              @click="handleSubmit"
              >提交</el-button
            >
          </slot>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, withDefaults, defineProps, defineEmits, defineExpose } from "vue";

const props = withDefaults(
  defineProps<{
    visible: boolean;
    title: string;
    top: string;
    width: string;
  }>(),
  {
    visible: false,
    title: "",
    top: "15vh",
    width: "30%",
  }
);

const submitLoading = ref(false);

const emits = defineEmits(["update:visible", "confirm"]);

const setSubmitLoading = (val: boolean) => {
  submitLoading.value = val;
};

const handleClose = () => {
  emits("update:visible", false);
};

const handleSubmit = () => {
  if (submitLoading.value) return;
  emits("confirm");
};

defineExpose({
  setSubmitLoading,
});
</script>

<style lang="scss">
.custom-l-dialog {
  .el-dialog {
    padding: 0;
    border-radius: 8px;
    border: 1px solid #979797;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
      width: 100%;
      height: 52px;
      line-height: 52px;
      padding-left: 24px;
      border-bottom: 1px solid #dcdee0;

      .el-dialog__title {
        color: #323233;
        font-size: 14px;
        font-weight: 600;
      }
    }

    .el-dialog__body {
      width: 100%;
      flex: 1;
    }

    .el-dialog__footer {
      width: 100%;
      height: 72px;
      padding: 20px 16px 0 18px;
      border-top: 1px solid #dcdee0;

      .dialog-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}
</style>
