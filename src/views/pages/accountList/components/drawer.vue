q
<template>
  <el-drawer
    v-model="props.isShowBudgetDay"
    size="45%"
    destroy-on-close
    :beforeClose="close"
  >
    <template #header>
      <div class="adjust-budget-title">{{ props.drawerTitle }}</div>
    </template>
    <el-form
      ref="subscriptionFormRef"
      :model="addSubscriptionForm"
      :rules="rules"
    >
      <h6 class="adjust-budget-h6">核对转出账户信息</h6>
      <div class="adjust-budget-info">
        <el-form-item label="媒体：">
          <el-text>{{ mediumType[editInfo.mediumType] }}</el-text></el-form-item
        >
        <el-form-item label="广告账户名称/ID："
          ><el-text
            >{{ editInfo.thirdAccountName }}/{{
              editInfo.thirdAccountId
            }}</el-text
          ></el-form-item
        >
        <el-form-item label="币种："
          ><el-text>{{ editInfo.currency }}</el-text></el-form-item
        >
        <el-form-item label="账户余额："
          ><el-text>{{ editInfo.remainAmount }}</el-text></el-form-item
        >
      </div>
      <h6 class="adjust-budget-h6">{{ props.drawerTitleTwo }}</h6>
      <div class="now-budget" v-if="props.drawerType == 1">
        当前日预算：{{
          editInfo.mediumType == 1 || editInfo.mediumType == 6 ? "￥" : "$"
        }}
        {{ editInfo.budgetDay }}
      </div>
      <div class="now-budget" v-if="props.drawerType == 2">
        钱包余额：￥{{ editInfo.remainAmount }}
      </div>
      <el-form-item
        v-if="props.drawerType == 1"
        prop="budgetDay"
        style="position: relative"
      >
        <el-space>
          <el-input-number
            v-model="addSubscriptionForm.budgetDay"
            :min="editInfo.budgetAmountLimit"
            style="max-width: 600px"
            placeholder="请输入预算"
            :controls="false"
          >
          </el-input-number>
          <el-tag size="large"> {{ editInfo.currency }} </el-tag>
        </el-space>
      </el-form-item>
      <el-form-item v-if="props.drawerType == 2">
        <el-space>
          <el-input-number
            v-model="addSubscriptionForm.budgetDay"
            :min="1"
            style="max-width: 600px"
            placeholder="请输入充值金额"
            :controls="false"
          >
          </el-input-number>
          <el-tag size="large"> {{ editInfo.currency }} </el-tag>
        </el-space>
      </el-form-item>
      <span class="budget-tips" v-if="props.drawerType == 1">
        <el-icon><WarningFilled /></el-icon>周预算=日预算*7
        <span style="margin-left: 20px"
          >{{ mediumType[editInfo.mediumType] }}日预算不低于：{{
            editInfo.mediumType == 1 || editInfo.mediumType == 6 ? "￥" : "$"
          }}
          {{ moneyNumber[editInfo.mediumType] }}</span
        ></span
      >

      <span class="budget-tips" v-if="props.drawerType == 2">
        <el-icon><WarningFilled /></el-icon>单次充值金额不得低于20000
      </span>
      <div class="formColumn" v-if="props.drawerType == 3">
        <el-form-item label="媒体" prop="mediumType" label-width="100">
          <el-select
            v-model="addSubscriptionForm.mediumType"
            placeholder="请选择媒体平台"
            @change="changeMediaType"
            clearable
          >
            <el-option
              v-for="item in subsriptionMeaid"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="转入账户" class="formColumn" label-width="100">
          <el-radio-group v-model="zhuankuanType" @change="changeType">
            <el-radio :value="1" size="large">已有账户</el-radio>
            <el-radio :value="2" size="large">新开户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="广告账户"
          class="formColumn"
          prop="third"
          label-width="100"
          v-if="zhuankuanType == 1"
        >
          <el-select
            v-model="addSubscriptionForm.third"
            placeholder="请选择广告账号"
            @change="changeThird"
          >
            <el-option
              v-for="item in thirdList"
              :key="item.thirdAccountId"
              :label="item.thirdAccountName"
              :value="item.accountInfoId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="formColumn"
          label="币种"
          prop="currency"
          label-width="100"
        >
          {{ unitMap[addSubscriptionForm.mediumType] }}
        </el-form-item>
        <h6 class="adjust-budget-h6">申请转款金额</h6>
        <div class="now-budget">账户余额：{{ editInfo.remainAmount }}</div>
        <el-form-item prop="outAmount">
          <el-space>
            <el-input-number
              v-model="addSubscriptionForm.outAmount"
              :min="editInfo.limitAmount"
              style="max-width: 600px"
              :controls="false"
            >
            </el-input-number>
            <el-tag size="large"> {{ editInfo.currency }} </el-tag>
          </el-space>
        </el-form-item>
        <div class="transferfunds-tips flex-cl">
          <p>
            <el-icon><WarningFilled /></el-icon>1.单次转账金额不得低于{{
              editInfo.limitAmount ?? "0"
            }}；
          </p>
          <p>
            2.跨媒体账户转账若涉及到汇率按照财务充值当天实时汇率为准，可在转款记录查看
          </p>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <div>
          <span v-if="props.drawerType == 3">
            共计:{{ addSubscriptionForm.outAmount }}
            {{ editInfo.currency }}
          </span>
        </div>
        <div>
          <el-button @click="close">取消</el-button>
          <el-button
            type="primary"
            @click="okSubscription(subscriptionFormRef)"
            :loading="okSubscriptionLoading"
            >提交</el-button
          >
        </div>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { ref, nextTick } from "vue";
import {
  accountMoneyList,
  updateTransferAmount,
  updateBudgetDay,
} from "@/api/accountListApi";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";

let zhuankuanType = ref(1);
const thirdList = ref([]);
const okSubscriptionLoading = ref(false);
const addSubscriptionForm = ref<any>({
  budgetDay: "",
  mediumType: "",
  third: "",
  currency: "",
  outAmount: 10000,
});
const thirdData = ref<any>({});
const rules = ref({
  budgetDay: [
    { required: true, message: "请输入每日预算", trigger: "change" },
    {
      validator: (rule, value, callback) => {
        const maxDigits = 16;
        if (value && value.toString().length > maxDigits) {
          callback(new Error("输入不能超过16位"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  mediumType: [
    { required: true, message: "请选择媒体类型", trigger: "change" },
  ],
  third: [
    { required: true, message: "请选择需要转款的账号", trigger: "change" },
  ],
  // currency: [{ required: true, message: "请选择币种", trigger: "change" }],
  outAmount: [{ required: true, message: "请输入转款金额", trigger: "change" }],
});
const subsriptionMeaid = ref([
  {
    value: 1,
    label: "Google Ads",
  },
  {
    value: 2,
    label: "Yandex",
  },
  {
    value: 3,
    label: "Meta",
  },
  {
    value: 4,
    label: "TikTok",
  },
  {
    value: 5,
    label: "Bing Ads",
  },
  {
    value: 6,
    label: "LinkedIn",
  },
  {
    value: 7,
    label: "VKontakte",
  },
  {
    value: 8,
    label: "Pinterest",
  },
]);
const props = defineProps({
  isShowBudgetDay: {
    type: Boolean,
    default: false,
  },
  drawerTitle: {
    type: String,
    default: "调整预算",
  },
  drawerTitleTwo: {
    type: String,
    default: "申请调整日预算",
  },
  drawerType: {
    type: Number,
    default: 1,
  },
  editInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
  mediumType: {
    type: Number,
    default: 1,
  },
});

const emit = defineEmits(["submit", "close", "getList"]);

const subscriptionFormRef = ref<any>(null);
const mediumType = {
  1: "Google Ads",
  2: "Yandex",
  3: "Meta",
  4: "TikTok For Business",
  5: "Bing ADS",
  6: "LinkedIn",
  7: "VKontakte",
  8: "Pinterest",
};
const unitMap = {
  1: "CNY",
  2: "USD",
  3: "USD",
  4: "USD",
  5: "USD",
  6: "CNY",
  7: "USD",
  8: "USD",
};
const moneyNumber = {
  1: 100,
  2: 15,
  3: 25,
  4: 20,
  5: 20,
  6: 300,
};
// 提交转账 充值 修改每日预算
const okSubscription = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (!valid) return;
    // emit("submit", props.editInfo);
    // 转账
    if (props.drawerType == 3) {
      const res: any = await updateTransferAmount({
        outAccountInfoId: props.editInfo.accountInfoId, // 	客户账户ID
        inAccountInfoId: thirdData.value.accountInfoId, // 转入账户
        outAmount: addSubscriptionForm.value.outAmount, // 转出金额
        inMediumType: addSubscriptionForm.value.mediumType, // 转入媒体类型
        inAccountId: thirdData.value.thirdAccountId, // 转入广告账户ID
        inAccountName: thirdData.value.thirdAccountName, // 转入广告账户名称
        inCurrency: unitMap[addSubscriptionForm.value.mediumType], // 转入币种
        type: zhuankuanType.value,
      });
      if (res?.code == 200) {
        ElMessage.success("转款申请提交成功");
        close();
        emit("getList");
      } else {
        ElMessage.error(res?.msg);
      }
    }
    if (props.drawerType == 1) {
      const res: any = await updateBudgetDay({
        accountInfoId: props.editInfo.accountInfoId,
        budgetDay: addSubscriptionForm.value.budgetDay,
      });
      if (res?.code == 200) {
        ElMessage.success("日预算调整成功");
        close();
        emit("getList");
      } else {
        ElMessage.error(res?.msg);
      }
    }
  });
};
const close = () => {
  addSubscriptionForm.value.budgetDay = null;
  addSubscriptionForm.value.currency = null;
  addSubscriptionForm.value.mediumType = null;
  addSubscriptionForm.value.outAmount = null;
  addSubscriptionForm.value.third = "";
  zhuankuanType.value = 1;
  thirdList.value = [];
  emit("close", false);
  // 清除third的表单警告
  subscriptionFormRef.value.clearValidate("third");
};
// 选择媒体平台
const changeMediaType = (val: number) => {
  // addSubscriptionForm.mediumType = val
  addSubscriptionForm.value.third = "";
  addSubscriptionForm.value.currency = null;
  // 清除third的表单警告
  subscriptionFormRef.value.clearValidate("third");
  getAccountMoneyList(val);
};
const changeThird = (val: any) => {
  let filterThirdData = thirdList.value.find(
    (item) => item.accountInfoId === val,
  );
  thirdData.value = filterThirdData;
  thirdData.value.limitAmount = filterThirdData.limitAmount;
  addSubscriptionForm.value.currency = filterThirdData.currency;
};
const getAccountMoneyList = async (val: number) => {
  try {
    const params = {
      mediumType: val,
      customerId: props.editInfo.customerId,
      serviceCode: props.editInfo.serviceCode,
    };
    const res: any = await accountMoneyList(params);
    if (res?.code == 200) {
      thirdList.value = res?.data?.filter(
        (item) => item.thirdAccountId && item.source == 1,
      );
    } else {
      ElMessage.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
const changeType = () => {
  if (zhuankuanType.value == 1) {
    subsriptionMeaid.value = [
      {
        value: 1,
        label: "Google Ads",
      },
      {
        value: 2,
        label: "Yandex",
      },
      {
        value: 3,
        label: "Meta",
      },
      {
        value: 4,
        label: "TikTok",
      },
      {
        value: 5,
        label: "Bing Ads",
      },
      {
        value: 6,
        label: "LinkedIn",
      },
      {
        value: 7,
        label: "VKontakte",
      },
      {
        value: 8,
        label: "Pinterest",
      },
    ];
  } else {
    subsriptionMeaid.value = [
      {
        value: 1,
        label: "Google Ads",
      },
      {
        value: 2,
        label: "Yandex",
      },
      {
        value: 3,
        label: "Meta",
      },
      {
        value: 4,
        label: "TikTok",
      },
      {
        value: 5,
        label: "Bing Ads",
      },
      {
        value: 6,
        label: "LinkedIn",
      },
    ];
  }
  thirdList.value = [];
  addSubscriptionForm.value.third = "";
  addSubscriptionForm.value.mediumType = "";
  setTimeout(() => {
    subscriptionFormRef.value.clearValidate("third");
    subscriptionFormRef.value.clearValidate("mediumType");
  }, 100);
};
</script>
<style lang="scss" scoped>
.adjust-budget-title {
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
  line-height: 26px;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.formColumn {
  :deep(.el-form-item) {
    display: block !important;
    .el-form-item__label {
      display: block !important;
    }
  }
}

.adjust-budget-h6 {
  font-family: PingFangSC-SNaNpxibold;
  font-size: 16px;
  color: #303133;
  line-height: 20px;
  padding-left: 12px;
  margin-bottom: 20px;
  position: relative;
  &:after {
    content: "";
    position: absolute;
    height: 16px;
    width: 4px;
    background: var(--el-color-primary);
    left: 0;
    top: 2px;
  }
}
.budget-tips {
  display: inline-flex;
  align-items: center;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #606266;
  padding: 2px 10px;
  background: #f0f1f4;
  border-radius: 3px;
  :deep(.el-icon) {
    color: var(--el-color-primary);
    margin-right: 7px;
  }
}
.transferfunds-tips {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #606266;
  padding: 2px 10px;
  background: #f0f1f4;
  border-radius: 3px;
  line-height: 22px;
  p {
    display: flex;
    align-items: center;
  }
  p:nth-child(2) {
    text-indent: 20px;
  }
  :deep(.el-icon) {
    color: var(--el-color-primary);
    margin-right: 7px;
  }
}
.now-budget {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: var(--el-color-primary);
  margin: 12px 0;
}
.adjust-budget-info {
  border: 1px solid #dcdfe6;
  width: 90%;
  padding: 20px;
  margin: 12px 0 20px 0;
}
</style>
