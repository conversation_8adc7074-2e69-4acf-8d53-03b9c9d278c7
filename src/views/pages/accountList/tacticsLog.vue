<template>
  <h2 class="ads-title">
    <el-icon size="24" @click="goBack()"><Back /></el-icon>
    <span>策略配置记录</span>
  </h2>
  <div class="tacticsMain">
    <!-- 动态表格组件  -->
    <DynamicTable
      ref="dyTableRef"
      :columns="columns"
      :initial-data="tacticsList"
      dy-table-radius="0 0 8px 8px"
      backgroundColor="#f5f5fa"
      max-height="360px"
      :showPage="false"
      v-loading="loading"
    >
      <template #custom="{ row }">
        <div class="tooltip-div f" style="flex-direction: column">
          <div v-for="(item, index) in row.ruleArr" :key="index">
            {{ item }}
          </div>
        </div>
      </template>
    </DynamicTable>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { getTacticsLog } from "@/api/tactics";

// 返回
const goBack = () => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "routerGo",
      path: "/ads/accountManage/account-list",
      name: "ads",
      parentPath: "/ads/accountManage/account-list",
      isChild: false,
      go: -1,
    });
  }
}; // 动态表格列配置
const columns = [
  {
    label: "策略名称",
    prop: "configName",
  },
  {
    label: "策略应用范围",
    prop: "platformType",
    formatter: (row) => {
      return row.platformType === 1 ? "全媒体平台" : "单媒体平台";
    },
  },
  {
    label: "触发规则",
    prop: "ruleArr",
    type: "custom",
  },
  {
    label: "触发状态",
    prop: "triggerStatus",
    formatter: (row) => {
      return row.triggerStatus === 1 ? "开启" : "关闭";
    },
  },
  {
    label: "预警方式",
    prop: "notificationType",
    formatter: (row: any) => {
      let str = "";
      if (row.notificationType.indexOf(1) > -1) {
        str += "站内消息";
      }
      if (row.notificationType.indexOf(2) > -1) {
        str = str == "" ? "邮件预警" : str + "、邮件预警";
      }
      return str;
    },
  },
  {
    label: "收件邮箱",
    prop: "receiverMailbox",
  },
  {
    label: "生效时间",
    prop: "effectiveTime",
  },
  {
    label: "操作时间",
    prop: "updateTime",
  },
];
let loading = ref(false);
let tacticsList = ref([]);
const getData = async () => {
  try {
    loading.value = true;
    let { code, data } = await getTacticsLog();
    if (code && code == 200 && data) {
      loading.value = false;
      let warnArr = [];
      if (data.platformType == "1") {
        if (!data.ruleRespList || data.ruleRespList.length == 0) {
          warnArr = [];
          return;
        }
        // 全媒体
        warnArr = [
          `当账户余额小于最近${data.ruleRespList[0].triggerDays}天消耗平均值`,
        ];
      } else {
        if (!data.ruleRespList || data.ruleRespList.length == 0) {
          warnArr = [];
          return;
        }
        // 单媒体
        data.ruleRespList.forEach((item: any, index: number) => {
          let platType =
            item.mediumType == 1
              ? "Google"
              : item.mediumType == 2
              ? "Yandex"
              : item.mediumType == 3
              ? "Meta"
              : item.mediumType == 4
              ? "Tiktok"
              : item.mediumType == 5
              ? "Bing"
              : item.mediumType == 6
              ? "LinkedIn"
              : "";
          warnArr.push(
            `${platType}当账户余额小于${item.threshold}${item.currency}`
          );
        });
      }
      data.ruleArr = warnArr;
      tacticsList.value = [data];
    } else {
      loading.value = false;
      tacticsList.value = [];
    }
  } catch (error) {
    loading.value = false;
    console.error(error);
  }
};
onMounted(() => {
  getData();
});
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: "PingFangSC-Medium";
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  cursor: pointer;
  display: flex;
  align-items: center;
  span {
    flex: 1;
    margin-left: 10px;
  }
}
.tacticsMain {
  width: 100%;
  height: calc(100% - 80px);
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  overflow-y: auto;
}
</style>
