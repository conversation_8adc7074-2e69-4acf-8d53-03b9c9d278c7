<template>
  <h2 class="ads-title">账户列表</h2>
  <div class="ads-remainAmount">
    <div
      class="remainAmount-item"
      v-for="(item, index) in remainAmountList"
      :key="item.mediaType"
    >
      <div class="item-top">
        <img :src="item.icon" alt="" class="top-icon" />{{ item.name }}
      </div>
      <div class="item-bottom" style="display: flex; justify-content: flex-end">
        {{ item.mediaType == 1 || item.mediaType == 6 ? "￥" : "$"
        }}<el-statistic
          v-if="item.mediaType !== 6 && item.mediaType !== 5"
          :value="outputValue[index]"
        />
        <span v-else>--</span>
      </div>
    </div>
  </div>
  <el-tabs
    class="demo-tabs"
    v-model="activeTabs"
    @tab-change="changeTabs"
    style="background-color: #ffffff"
  >
    <el-tab-pane
      v-for="item in remainAmountList"
      :key="item.mediaType"
      :name="item.mediaType"
    >
      <template #label v-if="item.isShow">
        <span class="custom-tabs-label">
          <img :src="item.icon" class="tabs-icon" />
          <span>{{ item.name.replace("账户余额", "") }}</span>
        </span>
      </template>
    </el-tab-pane>
  </el-tabs>
  <div style="background-color: #ffffff">
    <el-input
      placeholder="请输入开户主体"
      v-model="openName"
      @change="changeOpenName"
      :suffix-icon="Search"
      style="width: 240px; margin-left: 20px"
    ></el-input>
    <el-select
      v-model="state"
      placeholder="请选择账户状态"
      @change="changeMediumType"
      style="width: 240px; margin-left: 20px"
    >
      <el-option :value="1" label="正常" />
      <el-option :value="2" label="暂停" />
    </el-select>
  </div>
  <el-table :data="tableData" v-loading="loading" style="padding: 20px">
    <el-table-column type="index" label="序号" width="100"> </el-table-column>
    <el-table-column prop="thirdAccountName" label="广告账户名称">
    </el-table-column>
    <el-table-column prop="thirdAccountId" label="广告账户ID">
    </el-table-column>
    <el-table-column prop="budgetDay" label="日预算" show-word-limit>
      <template #default="{ row }">
        <el-button
          type="primary"
          link
          @click="setBudget(row)"
          :disabled="commonControls || !row.thirdAccountId"
          >{{ row.budgetDay
          }}<el-icon style="margin-left: 7px"><EditPen /></el-icon
        ></el-button>
      </template>
    </el-table-column>
    <el-table-column prop="state" label="账户状态">
      <template #default="{ row }">
        <div style="display: flex; align-items: center">
          <div
            :style="{ background: row.state == 1 ? '#10CC81' : 'red' }"
            style="
              width: 6px;
              height: 6px;
              margin-right: 4px;
              border-radius: 50%;
            "
          ></div>
          <el-text> {{ stateName[row.state] }}</el-text>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="openName" label="开户主体"> </el-table-column>
    <el-table-column prop="currency" label="币种"> </el-table-column>
    <el-table-column prop="remainAmount" label="余额">
      <template #header
        >余额
        <el-tooltip
          class="box-item"
          effect="dark"
          content="余额每日0点更新，非实时数据"
          placement="top"
        >
          <el-icon><Warning /></el-icon>
        </el-tooltip>
      </template>
      <template #default="{ row }">
        <div
          v-if="(row.mediumType != 5 && row.mediumType != 6) || commonControls"
        >
          {{ row.remainAmount }}
          <p>冻结余额：{{ row.freezeAmount }}</p>
        </div>
        <div v-else>--</div>
      </template>
    </el-table-column>
    <el-table-column label="操作" fixed="right" width="150">
      <template #default="{ row }">
        <el-button
          type="primary"
          link
          @click="transferFunds(row)"
          :disabled="
            row.mediumType == 5 ||
            row.mediumType == 6 ||
            commonControls ||
            !row.thirdAccountId
          "
          >转款</el-button
        >
        <!-- <el-button type="primary" link @click="rechargeDrawer(row)"
          >充值</el-button
        > -->
      </template>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <Pagination
    v-if="total"
    :total="total"
    :currentPage="currentPage"
    :pageSize="pageSize"
    @update:pageSize="changePageSize"
    @handleCurrentChange="handleCurrentChange"
    style="padding-right: 20px"
  />
  <Drawer
    :editInfo="editInfo"
    :drawerTitle="drawerTitle"
    :drawerTitleTwo="drawerTitleTwo"
    :isShowBudgetDay="isShowBudgetDay"
    :drawerType="drawerType"
    :mediumType="mediumType"
    @close="close"
    @submit="submit"
    @getList="getAccountPageList"
  />
</template>
<script lang="ts" setup>
import { ref, onMounted, computed, watch } from "vue";
import { accountPageList, remainAmount } from "@/api/accountListApi";
import GoogleIcon from "@/assets/img/Google-icon.png";
import YandexIcon from "@/assets/img/Yandex-icon.png";
import FacebookIcon from "@/assets/img/facebook-icon.png";
import TikTokIcon from "@/assets/img/tikTok-icon.png";
import BingIcon from "@/assets/img/bing-icon.png";
import LinkedinIcon from "@/assets/img/Linkedin-icon.png";
import Pagination from "@/components/Pagination/index.vue";
import Drawer from "./components/drawer.vue";
import type { FormInstance } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import usePermissionStore from "@/store/modules/permission";
import { useTransition } from "@vueuse/core";
const permissionStore = usePermissionStore();
const commonControls = ref(false);
// const commonView = ref(true);
const AccountGoogleView = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountGoogleView") > -1
);
const AccountYandexView = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountYandexView") > -1
);
const AccountTikTokView = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountTikTokView") > -1
);
const AccountFacebookView = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountFacebookView") >
    -1
);
const AccountBingADSView = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountBingADSView") > -1
);
const AccountLinkInADSView = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountLinkInADSView") >
    -1
);
const AccountGoogleControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountGoogleControls") >
    -1
);
const AccountYandexControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountYandexControls") >
    -1
);
const AccountTikTokControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf("AccountTikTokControls") >
    -1
);
const AccountFacebookControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf(
      "AccountFacebookControls"
    ) > -1
);
const AccountBingADSControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf(
      "AccountBingADSControls"
    ) > -1
);
const AccountLinkInADSControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountList?.indexOf(
      "AccountLinkInADSControls"
    ) > -1
);
const activeTabs = ref(1);
const openName = ref("");
const mediumType = ref<any>(1);
const state = ref();
const total = ref(10);
const currentPage = ref(1);
const pageSize = ref(10);
const drawerTitle = ref("调整预算");
const drawerTitleTwo = ref("申请调整日预算");
const drawerType = ref(1); // 1调整预算  2 充值  3 账户准款
const editInfo = ref({
  mediaType: null,
  thirdAccountId: "",
  remainAmount: "",
  currency: "",
  budgetDay: "",
  accountInfoId: "",
});
const stateName = {
  1: "正常",
  2: "暂停",
};
let numList = ref([0, 0, 0, 0, 0, 0, 0]);
const outputValue = useTransition(numList, {
  duration: 1000,
});
const tableData = ref([]);
const loading = ref(false);
const isShowBudgetDay = ref(false);
const remainAmountAll = ref<any>(null);
const remainAmountList = ref<any>([
  {
    name: "Google Ads",
    key: "googleRemainAmount",
    mediaType: 1,
    icon: GoogleIcon,
    isShow: AccountGoogleView,
  },
  {
    name: "Yandex",
    key: "yandexRemainAmount",
    mediaType: 2,
    icon: YandexIcon,
    isShow: AccountYandexView,
  },
  {
    name: "Facebook",
    key: "facebookRemainAmount",
    mediaType: 3,
    icon: FacebookIcon,
    isShow: AccountFacebookView,
  },
  {
    name: "Tiktok For Business",
    key: "tiktokRemainAmount",
    mediaType: 4,
    icon: TikTokIcon,
    isShow: AccountFacebookView,
  },
  {
    name: "Bing Ads",
    key: "bingRemainAmount",
    mediaType: 5,
    icon: BingIcon,
    isShow: AccountBingADSView,
  },
  {
    name: "LinkedIn",
    key: "linkedinRemainAmount",
    mediaType: 6,
    icon: LinkedinIcon,
    isShow: AccountLinkInADSView,
  },
]);
onMounted(() => {
  getAccountPageList();
  getRemainAmount();
});
const getRemainAmount = async () => {
  try {
    const res: any = await remainAmount({});
    if (res?.code == 200) {
      remainAmountAll.value = res?.data;
      numList.value = [
        res?.data?.googleRemainAmount,
        res?.data?.yandexRemainAmount,
        res?.data?.facebookRemainAmount,
        res?.data?.tiktokRemainAmount,
        res?.data?.bingRemainAmount,
        res?.data?.linkedinRemainAmount,
      ];
    }
  } catch (error) {
    console.log(error);
  }
};
const getAccountPageList = async () => {
  try {
    const params = {
      mediumType: mediumType.value,
      openName: openName.value,
      state: state.value,
      customerId: "",
      pageIndex: currentPage.value,
      pageSize: pageSize.value,
    };
    loading.value = true;
    const res: any = await accountPageList(params);
    loading.value = false;
    if (res?.code == 200) {
      tableData.value = res?.data?.list;
      total.value = res.data?.total;
    }
  } catch (error) {
    console.log(error);
  }
};
const changeTabs = (val) => {
  activeTabs.value = val;
  currentPage.value = 1;
  mediumType.value = activeTabs.value;
  getAccountPageList();
};
const changeMediumType = () => {
  currentPage.value = 1;
  getAccountPageList();
};
const changeOpenName = () => {
  currentPage.value = 1;
  getAccountPageList();
};
const transferFunds = (row: any) => {
  console.log(row);
  editInfo.value = row;
  isShowBudgetDay.value = true;
  drawerTitle.value = "转款";
  drawerTitleTwo.value = "选择转入账户信息";
  drawerType.value = 3;
};
const setBudget = (row: any) => {
  console.log(row);
  editInfo.value = row;
  isShowBudgetDay.value = true;
  drawerTitle.value = "调整预算";
  drawerTitleTwo.value = "申请调整日预算";
  drawerType.value = 1;
};
const rechargeDrawer = (row: any) => {
  console.log(row);
  editInfo.value = row;
  isShowBudgetDay.value = true;
  drawerTitle.value = "充值";
  drawerTitleTwo.value = "申请充值金额";
  drawerType.value = 2;
};
const close = () => {
  isShowBudgetDay.value = false;
};
const submit = () => {
  isShowBudgetDay.value = false;
};
// 分页
const handleCurrentChange = (value: number) => {
  console.log(value);
  currentPage.value = value;
  getAccountPageList();
};
// 选择每页条数
const changePageSize = (val: number) => {
  console.log(val);
  pageSize.value = val;
  getAccountPageList();
};
// 确认新增订阅
const okSubscription = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
};
watch(
  () => activeTabs.value,
  (newVal) => {
    if (newVal) {
      switch (newVal) {
        case 1:
          commonControls.value = !AccountGoogleControls.value;
          break;
        case 2:
          commonControls.value = !AccountYandexControls.value;
          break;
        case 3:
          commonControls.value = !AccountFacebookControls.value;
          break;
        case 4:
          commonControls.value = !AccountTikTokControls.value;
          break;
        case 5:
          commonControls.value = !AccountBingADSControls.value;
          break;
        case 6:
          commonControls.value = !AccountLinkInADSControls.value;
          break;
        default:
          commonControls.value = !AccountGoogleControls.value;
          break;
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 20px;
  color: #202020;
  margin: 20px 0;
}
.ads-remainAmount {
  display: flex;
  gap: 15px;
  margin-bottom: 12px;
  min-width: 221px;
  width: 100%;
  flex-wrap: nowrap;
  overflow-x: auto;

  .remainAmount-item {
    width: 298px;
    min-width: 221px;
    height: 91px;
    background: #ffffff;
    border-radius: 3px;
    padding: 16px;
    box-sizing: border-box;
    .item-top {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Medium;
      font-weight: bold;
      font-size: 16px;
      color: #303133;
      .top-icon {
        width: 16px;
        height: 16px;
        margin-right: 12px;
      }
    }
    .item-bottom {
      text-align: right;
      font-family: HelveticaNeue-Medium;
      font-weight: 500;
      font-size: 24px;
      color: #303133;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 12px;
    }
  }
}
.ads-remainAmount::-webkit-scrollbar {
  height: 5px; /* 滚动条的高度 */
}

.demo-tabs {
  box-sizing: border-box;
  padding: 20px;
  //   height: calc(100vh - 251px);
}

.custom-tabs-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;

  .tabs-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}
</style>
