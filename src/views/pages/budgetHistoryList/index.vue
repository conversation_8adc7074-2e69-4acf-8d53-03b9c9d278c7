<template>
  <h2 class="ads-title">
    <Breadcrumb />
    <div class="filter">
      <el-date-picker
        clearable
        v-model="date"
        type="daterange"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeDate"
      />
      <el-select
        v-model="mediumType"
        placeholder="请选择媒体平台"
        @change="changeMediaType"
        clearable
        style="width: 240px; margin: 0 16px"
      >
        <el-option
          v-for="item in subsriptionMeaid"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="state"
        placeholder="调整状态"
        @change="changeMediaType"
        clearable
        style="width: 240px"
      >
        <el-option label="调整中" :value="1" />
        <el-option label="调整完成" :value="2" />
      </el-select>
    </div>
  </h2>
  <div style="background-color: #ffffff; padding: 20px; padding-top: 0">
    <TableCustom
      :columns="columns"
      :height="0"
      :total="total"
      :tableData="tableData"
      :currentPage="currentPage"
      :changePage="handleCurrentChange"
      :changePageSize="handleCurrentPageSize"
      :isShowPagination="true"
      :isAdmin="true"
      :isHistoryPage="true"
      style="padding: 20px"
      v-loading="loading"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { budgetDayPageList } from "@/api/accountListApi";
import TableCustom from "@/components/table-custom.vue";
import { subsriptionMeaid, midumeTypeMapping, stateMapping } from "@/utils/mapping"
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const mediumType = ref();
const loading = ref(false);
const date = ref([]);
const total = ref(0);
const state = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const columns = ref([
  { prop: "mediumType", label: "媒体" },
  { prop: "thirdAccountName", label: "广告账户名称" },
  { prop: "thirdAccountId", label: "广告账户ID" },
  { prop: "currency", label: "账户币种" },
  { prop: "budgetDayOld", label: "调整前日预算" },
  { prop: "budgetDayNew", label: "调整后日预算" },
  { prop: "state", label: "调整状态" },
  { prop: "budgetDayTime", label: "调整申请时间" },
  { prop: "operatorName", label: "申请人" },
]);
const tableData = ref([]);
onMounted(() => {
  getBudgetDayPageList();
});

const getBudgetDayPageList = async () => {
  try {
    const params = {
      mediumType: mediumType.value,
      startDate: date.value?.[0] || "",
      endDate: date.value?.[1] || "",
      state: state.value,
      pageIndex: currentPage.value,
      pageSize: pageSize.value,
    };
    loading.value = true;
    const res: any = await budgetDayPageList(params);
    loading.value = false;
    if (res?.code == 200) {
      tableData.value = res?.data?.list?.map((item) => {
        return {
          ...item,
          mediumType: midumeTypeMapping[item.mediumType],
          state: stateMapping[item.state],
        };
      });
      total.value = res.data?.total;
    }
  } catch (error) {
    console.log(error);
  }
};
const changeMediaType = () => {
  currentPage.value = 1;
  getBudgetDayPageList();
};

// 分页
const handleCurrentChange = (value: number) => {
  console.log(value);
  currentPage.value = value;
  getBudgetDayPageList();
};
// 选择日期
const changeDate = (val: number) => {
  getBudgetDayPageList();
};
// 选择每页条数
const handleCurrentPageSize = (val: number) => {
  pageSize.value = val;
  getBudgetDayPageList();
};
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: PingFangSC-Medium, serif;
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    flex: 1;
  }
}
.details-text {
  font-family: PingFangSC-SNaNpxibold, serif;
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  letter-spacing: 0;
  margin-bottom: 20px;
}
.ads-remainAmount {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  .remainAmount-item {
    width: 221px;
    height: 91px;
    background: #ffffff;
    border-radius: 3px;
    padding: 16px;
    box-sizing: border-box;
    .item-top {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Medium, serif;
      font-weight: bold;
      font-size: 16px;
      color: #303133;
      .top-icon {
        width: 16px;
        height: 16px;
        margin-right: 12px;
      }
    }
    .item-bottom {
      text-align: right;
      font-family: HelveticaNeue-Medium, serif;
      font-weight: 500;
      font-size: 24px;
      color: #303133;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 12px;
    }
  }
}

.demo-tabs {
  box-sizing: border-box;
  padding: 20px;
}

.custom-tabs-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;

  .tabs-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}
</style>
