<template>
  <h2 class="ads-title">
    <span>充值/转款{{ isProgress ? "进度" : "记录" }}</span>
  </h2>
  <div style="background-color: #ffffff; padding-top: 0">
    <el-tabs
      class="demo-tabs"
      v-model="activeTabs"
      @tab-change="changeTabs"
      style="background-color: #ffffff"
    >
      <el-tab-pane
        v-for="item in progressList"
        :key="item.key"
        :name="item.key"
      >
        <template #label>
          <span class="custom-tabs-label">
            <span>{{ item.name }}</span>
          </span>
        </template>
      </el-tab-pane>
    </el-tabs>
    <div class="filter">
      <el-input
        v-model="thirdAccountName"
        placeholder="广告账户名称或ID"
        clearable
        style="width: 25%; padding-right: 20px"
        @change="changeInput"
      ></el-input>
      <el-date-picker
        clearable
        v-model="date"
        type="daterange"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 25%"
        @change="changeDate"
      />
      <el-select
        v-model="inMediumType"
        placeholder="转入媒体"
        @change="changeMediaType"
        clearable
        style="width: 200px; margin: 0 16px"
      >
        <el-option
          v-for="item in MediumTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="outMediumType"
        placeholder="转出媒体"
        @change="changeMediaType"
        clearable
        style="width: 200px; margin: 0 16px"
      >
        <el-option
          v-for="item in MediumTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-if="isProgress"
        v-model="state"
        placeholder="转款状态"
        @change="changeMediaType"
        clearable
        style="width: 200px"
      >
        <el-option label="转款中" :value="1" />
        <el-option label="转款完成" :value="2" />
        <el-option label="转款失败" :value="3" />
      </el-select>
    </div>
    <TableCustom
      :columns="columns"
      :height="0"
      :total="total"
      :currentPage="currentPage"
      :tableData="tableData"
      :changePage="handleCurrentChange"
      :changePageSize="handleCurrentPageSize"
      :isShowPagination="true"
      :isAdmin="true"
      v-loading="loading"
      style="padding: 20px"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watchEffect } from "vue";
import { transferPageList } from "@/api/accountListApi";
import TableCustom from "@/components/table-custom.vue";
import { useRoute } from "vue-router";
import {
  midumeTypeMapping,
  MediumTypeList,
  specialFundStateMapping,
} from "@/utils/mapping";
import Breadcrumb from "@/components/Breadcrumb/index.vue";
const route = useRoute();
const date = ref([]);
const total = ref(0);
const state = ref();
const currentPage = ref(1);
const pageSize = ref(10);
const activeTabs = ref("1");
const thirdAccountName = ref("");
const inMediumType = ref("");
const outMediumType = ref("");
const columns = ref<any[]>([]);
const isProgress = ref(false);
const loading = ref(false);
const progressList = ref([
  {
    name: "转款",
    key: "1",
  },
]);
const columnsProgress = ref([
  { prop: "outMediumType", label: "转出媒体", width: "150" },
  { prop: "outAccountName", label: "转出广告账户名称", width: "200" },
  { prop: "outAccountId", label: "转出广告账户ID", width: "200" },
  { prop: "outCurrency", label: "转出币种", width: "150" },
  { prop: "outAmount", label: "转出金额", width: "150" },
  { prop: "inMediumType", label: "转入媒体", width: "150" },
  { prop: "inAccountName", label: "转入广告账户名称", minWidth: "200" },
  { prop: "inAccountId", label: "转入广告账户ID", width: "200" },
  { prop: "state", label: "转款状态", width: "150" },
  { prop: "transferNm", label: "转账订单号", width: "150" },
  { prop: "transferStateTime", label: "申请转款时间", width: "220" },
  { prop: "operatorName", label: "操作人", width: "150" },
]);
const columnsHistory = ref([
  { prop: "transferNm", label: "转账订单号", width: "150" },
  { prop: "outMediumType", label: "转出媒体", width: "150" },
  { prop: "outAccountName", label: "转出广告账户名称", minWidth: "200" },
  { prop: "outAccountId", label: "转出广告账户ID", width: "150" },
  { prop: "outCurrency", label: "转出币种", width: "100" },
  { prop: "outAmount", label: "转出金额", width: "100" },
  { prop: "inMediumType", label: "转入媒体", width: "150" },
  { prop: "inAccountName", label: "转入广告账户名称", width: "150" },
  { prop: "inAccountId", label: "转入广告账户ID", width: "150" },
  { prop: "inCurrency", label: "转入币种", width: "100" },
  { prop: "inRealAmount", label: "到账金额", width: "100" },
  { prop: "inExchangeRate", label: "汇率", width: "100" },
  { prop: "transferEndTime", label: "转款到账时间", width: "220" },
]);
const tableData = ref([]);
onMounted(() => {
  getTransferPageList();
});

const getTransferPageList = async () => {
  try {
    const params = {
      inMediumType: inMediumType.value, // 转入媒体类型
      outMediumType: outMediumType.value, //  转出媒体类型
      thirdAccountName: thirdAccountName.value, // 广告客户名称
      startDate: date.value?.[0], // 申请开始时间
      endDate: date.value?.[1], // 申请开始时间
      // state: state.value, // 转款状态
      state: route.name == "HistorySummary" ? 2 : "", // 转款状态
      pageIndex: currentPage.value,
      pageSize: pageSize.value,
    };
    loading.value = true;
    const res: any = await transferPageList(params);
    loading.value = false;
    if (res?.code == 200) {
      tableData.value = res?.data?.list?.map((item: any) => {
        return {
          ...item,
          outMediumType: midumeTypeMapping[item.outMediumType],
          inMediumType: midumeTypeMapping[item.inMediumType],
          mediumType: midumeTypeMapping[item.mediumType],
          state: specialFundStateMapping[item.state],
        };
      });
      total.value = res.data?.total;
    }
  } catch (error) {
    console.log(error);
  }
};
const changeMediaType = () => {
  currentPage.value = 1;
  getTransferPageList();
};

// 分页
const handleCurrentChange = (value: number) => {
  console.log(value);
  currentPage.value = value;
  getTransferPageList();
};
// 选择日期
const changeDate = () => {
  getTransferPageList();
};
const changeInput = () => {
  getTransferPageList();
};
// 选择每页条数
const handleCurrentPageSize = (val: number) => {
  console.log(val);
  pageSize.value = val;
  getTransferPageList();
};
const changeTabs = () => {
  currentPage.value = 1;
  getTransferPageList();
};
watchEffect(() => {
  switch (route.name) {
    case "ProgressSummary":
      // 转款进度
      columns.value = columnsProgress.value;
      isProgress.value = true;
      break;
    case "HistorySummary":
      // 转款记录
      columns.value = columnsHistory.value;
      isProgress.value = false;
      break;
    default:
      columns.value = columnsProgress.value;
      break;
  }
});
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: PingFangSC-Medium, serif;
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  span {
    flex: 1;
  }
}
.filter {
  padding-left: 20px;
}
.details-text {
  font-family: PingFangSC-SNaNpxibold, serif;
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  letter-spacing: 0;
  margin-bottom: 20px;
}
.ads-remainAmount {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  .remainAmount-item {
    width: 221px;
    height: 91px;
    background: #ffffff;
    border-radius: 3px;
    padding: 16px;
    box-sizing: border-box;
    .item-top {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Medium, serif;
      font-weight: bold;
      font-size: 16px;
      color: #303133;
      .top-icon {
        width: 16px;
        height: 16px;
        margin-right: 12px;
      }
    }
    .item-bottom {
      text-align: right;
      font-family: HelveticaNeue-Medium, serif;
      font-weight: 500;
      font-size: 24px;
      color: #303133;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 12px;
    }
  }
}

.demo-tabs {
  box-sizing: border-box;
  padding: 20px;
}

.custom-tabs-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;

  .tabs-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}
</style>
