/**
 * @description 数据中心查询条件属性枚举
 */
export enum DataCenterQueryKeys {
  // 开户主体
  ACCOUNT_OPENING_ENTITY = "openName",
  // 账户ID
  ACCOUNT_ID = "thirdCustomerId",
  // 消耗时间
  CONSUME_TIME = "consumeTime",
  // 开始时间
  START_TIME = "startTime",
  // 结束时间
  END_TIME = "endTime",
}

/**
 * @description 数据中心表格属性枚举
 */
export enum DataCenterTableKeys {
  // 日期
  DATE = "reportDate",
  // 开户主体
  ACCOUNT_OPENING_ENTITY = "customerMainName",
  // 媒体
  MEDIA = "mediumType",
  // 账户ID
  ACCOUNT_ID = "thirdCustomerId",
  // 账户名称
  ACCOUNT_NAME = "thirdCustomerName",
  // 币种
  CURRENCY = "currency",
  // 消耗金额
  CONSUME_AMOUNT = "cost",
}

/**
 * @description 数据中心账户数据枚举
 */
export enum DataCenterAccountDataKeys {
  // 账户消耗明细
  ACCOUNT_CONSUME_DETAIL = "accountConsumeDetail",
  // 账户消耗汇总
  ACCOUNT_CONSUME_SUMMARY = "accountConsumeSummary",
}
