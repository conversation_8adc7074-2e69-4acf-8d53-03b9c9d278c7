<template>
  <div class="data-center">
    <div class="titleMain">
      <Breadcrumb />
    </div>
    <div class="mainContent">
      <MediaTabs :noProsime="true" @tab-click="tabChange" />
      <div class="data-search-form">
        <el-form
          :inline="true"
          :model="searchFormInline"
          class="demo-form-inline"
        >
          <el-form-item label="">
            <el-select
              v-model="
                searchFormInline[DataCenterQueryKeys.ACCOUNT_OPENING_ENTITY]
              "
              placeholder="请选择开户主体"
              clearable
            >
              <el-option
                v-for="item in subjectList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-input
              v-model="searchFormInline[DataCenterQueryKeys.ACCOUNT_ID]"
              placeholder="广告账户名称或ID搜索"
              clearable
            />
          </el-form-item>
          <el-form-item label="">
            <el-date-picker
              v-model="searchFormInline[DataCenterQueryKeys.CONSUME_TIME]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD"
              :default-value="defaultTime"
              :disabled-date="disabledDate"
              @change="validateTimeRange"
              @visible-change="handleVisibleChange"
              @calendar-change="handleCalendarChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="updataTime">
          最近更新时间：{{ dayjs().subtract(1, "day").format("YYYY-MM-DD") }}
        </div>
      </div>
      <div class="consumption-data">
        <div class="consumption-statistics">
          <div class="total-consumption">
            <div class="title totalConsumption">
              总消耗
              <div class="totalConsumptionDesc">所选时间范围该媒体总金额</div>
            </div>
            <div class="amount">
              <span>约</span>{{ totalConsumption }} {{ currentMediaCode }}
            </div>
          </div>
          <div class="daily-consumption">
            <div
              class="title"
              style="
                display: flex;
                justify-content: space-between;
                align-items: center;
              "
            >
              每日消耗
              <el-button
                type="primary"
                @click="exportExcel(1)"
                v-if="
                  permissionStore?.buttonAuth?.DataConsumption?.indexOf(
                    'dataConsumeView'
                  ) != -1
                "
              >
                下载明细
              </el-button>
            </div>
            <div class="consumption-chart-detail"></div>
          </div>
        </div>
        <div class="consumption-ranking">
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-right: 24px;
            "
          >
            <div class="consumption-ranking-header">
              <div class="title">账户消耗排名</div>
              <div class="tip">按所选时间范围、媒体和开户主体进行排名</div>
            </div>
            <el-button
              type="primary"
              @click="exportExcel(2)"
              v-if="
                permissionStore?.buttonAuth?.DataConsumption?.indexOf(
                  'dataConsumeView'
                ) != -1
              "
            >
              下载明细
            </el-button>
          </div>
          <div class="consumption-ranking-body">
            <div class="ranking-list">
              <div
                class="ranking-item"
                v-for="(item, index) in accountConsumptionList"
                :key="item"
              >
                <div class="ranking-serial-number">
                  <img
                    v-if="index <= 2"
                    :src="sortNum[index]"
                    alt=""
                    class="svg-icon"
                  />
                  <span v-else>{{ index + 1 }}</span>
                </div>
                <img
                  :src="mediumTypeImg[item[DataCenterTableKeys.MEDIA]]"
                  alt=""
                  class="ranking-serial-icon"
                />
                <div class="ranking-info">
                  <div class="name">
                    {{ item[DataCenterTableKeys.ACCOUNT_NAME] }}
                  </div>
                  <div class="id">
                    ID: {{ item[DataCenterTableKeys.ACCOUNT_ID] }}
                  </div>
                </div>
                <div class="ranking-consumption">
                  {{ item[DataCenterTableKeys.CONSUME_AMOUNT] }}
                  {{ currentMediaCode }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="consumption-data-content"
        v-loading="tableLoading"
        v-if="false"
      >
        <div class="consumption-data-table">
          <div class="consumption-data-table-header">
            <div class="filter-button-group">
              <el-button-group>
                <el-button
                  :type="
                    currentTab ===
                    DataCenterAccountDataKeys.ACCOUNT_CONSUME_DETAIL
                      ? 'primary'
                      : ''
                  "
                  @click="
                    tableDataChange(
                      DataCenterAccountDataKeys.ACCOUNT_CONSUME_DETAIL
                    )
                  "
                  >账户消耗明细</el-button
                >
                <el-button
                  :type="
                    currentTab ===
                    DataCenterAccountDataKeys.ACCOUNT_CONSUME_SUMMARY
                      ? 'primary'
                      : ''
                  "
                  @click="
                    tableDataChange(
                      DataCenterAccountDataKeys.ACCOUNT_CONSUME_SUMMARY
                    )
                  "
                  >账户消耗汇总</el-button
                >
              </el-button-group>
            </div>
            <div class="operation-button-group">
              <el-button type="primary" @click="exportExcel"
                >excel导出</el-button
              >
            </div>
          </div>
          <div class="consumption-data-table-body">
            <el-table
              :data="tableData"
              :header-row-style="{ background: '#FAFAFA', height: '54px' }"
              :header-cell-style="{
                background: '#FAFAFA',
                color: '#000000',
                fontSize: '14px',
                fontWeight: '500',
                fontFamily: 'PingFangSC-Medium',
              }"
              :cell-style="{
                color: '#595959',
                fontSize: '14px',
                fontFamily: 'HelveticaNeue',
                height: '54px',
              }"
            >
              <template v-for="column in tableColumns" :key="column.prop">
                <el-table-column
                  :prop="column.prop"
                  :label="column.label"
                  :formatter="column.formatter"
                />
              </template>
            </el-table>
          </div>
        </div>

        <div class="consumption-data-paging">
          <el-pagination
            v-model:current-page="pagination.pageIndex"
            v-model:page-size="pagination.pageSize"
            layout="prev, pager, next, sizes, jumper"
            :total="pagination.total"
            @change="onSearch"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import MediaTabs from "@/views/pages/accountList/components/list/mediaTabs.vue";
import {
  DataCenterQueryKeys,
  DataCenterTableKeys,
  DataCenterAccountDataKeys,
} from "./enum/index";
import * as echarts from "echarts";
import {
  getDailyConsumption,
  getAccountConsumption,
  getAccountSummary,
} from "@/api/dataCenter";
import { getAccountOptionListApi } from "@/api/openAccount";
import { formatterMediaType, formatterMediaTypeIcon } from "@/utils/formatter";
import { downloadFile, validateTimeRange } from "@/utils/common";
import configLocation from "@/config";
import numOne from "@/assets/images/numOne.png";
import numTwo from "@/assets/images/numTwo.png";
import numThree from "@/assets/images/numThree.png";
import iconGoogle from "@/assets/images/home/<USER>";
import iconYandex from "@/assets/images/home/<USER>";
import iconMeta from "@/assets/images/home/<USER>";
import iconTikTok from "@/assets/images/home/<USER>";
import iconBing from "@/assets/images/home/<USER>";
import iconLink from "@/assets/images/home/<USER>";
import dayjs from "dayjs";
import usePermissionStore from "@/store/modules/permission";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const permissionStore = usePermissionStore();

// 计算最近七天的日期范围
const startDate = dayjs().subtract(30, "day").format("YYYY-MM-DD");
const endDate = dayjs().subtract(1, "day").format("YYYY-MM-DD");
const chooseDay = ref<any>(null); // 选择的第一个日期
let defaultTime = ref([startDate, endDate]) as any;
const handleCalendarChange = (val: [Date, null | Date]) => {
  // 选择的第一个日期
  const [chooseFirstDate] = val;
  chooseDay.value = new Date(chooseFirstDate);
};
// 下拉框打开时清除选择的第一个日期，可让用户任意选择开始日期
const handleVisibleChange = (val: boolean) => {
  if (val) {
    chooseDay.value = null;
  }
};
const disabledDate = (time: Date) => {
  if (!chooseDay.value) {
    // 选择的第一个日期不存在时可任意选择开始日期
    return time.getTime() > new Date().getTime() - 24 * 3600000;
  }
  return (
    // 限制选择的日期范围，选择第一个日期的前后30天，并且不能选择昨天之后的日期
    time.getTime() > new Date().getTime() - 24 * 3600000
  );
};

interface IPaging {
  pageIndex: number;
  pageSize: number;
  total?: number;
}
enum EMediaType {
  // google
  GOOGLE = 1,
  // yandex
  YANDEX = 2,
  // meta
  META = 3,
  // tiktok
  TIKTOK = 4,
  // bing
  BING = 5,
  // linkedin
  LINKEDIN = 6,
}
const sortNum = {
  0: numOne,
  1: numTwo,
  2: numThree,
};
const mediumTypeImg = {
  1: iconGoogle,
  2: iconYandex,
  3: iconMeta,
  4: iconTikTok,
  5: iconBing,
  6: iconLink,
};
// 用户信息
// const userStore = useUserStore(); userStore.userInfo.merchantType
// 商户类型
const merchantType = computed(() => 2);

// 当前媒体类型
const currentMedia = ref<number>(1);
// 当前媒体币种代码
const currentMediaCode = ref<string>("CNY");

// 数据中心查询条件表单
const searchFormInline = reactive({
  // 开户主体
  [DataCenterQueryKeys.ACCOUNT_OPENING_ENTITY]: "",
  // 账户ID
  [DataCenterQueryKeys.ACCOUNT_ID]: "",
  // 消耗时间
  [DataCenterQueryKeys.CONSUME_TIME]: [],
});

// 开户主体数据配置
const subjectList = ref([]);

// 总消耗金额
const totalConsumption = ref(0);
// 每日消耗折线图配置
const chartOption = ref({
  grid: {
    top: "26",
    left: "10",
    width: "98%",
    height: "82%",
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: "#000000a6",
    },
    axisLine: {
      show: true, // 显示坐标轴线
      lineStyle: {
        type: "solid", // 设置为实线
        color: "#DBDBDB",
        width: 2,
      },
    },
  },
  yAxis: {
    type: "value",
    axisLabel: {
      show: false,
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: "dashed", // 网格线设置为实线
        color: "#ddd",
      },
    },
  },
  series: [
    {
      data: [], // 数据源
      type: "line", // 折线图
      symbol: "none", // 去掉折线上的点
      lineStyle: { color: "#2893FF" },
    },
  ],
});

// 账户消耗排名列表
const accountConsumptionList = ref([]);

// 表格loading
const tableLoading = ref(false);
// 当前数据来源
const currentTab = ref<DataCenterAccountDataKeys>(
  DataCenterAccountDataKeys.ACCOUNT_CONSUME_DETAIL
);
// 数据表Columns
const tableColumns = computed(() => {
  return currentTab.value === DataCenterAccountDataKeys.ACCOUNT_CONSUME_DETAIL
    ? tableDetailKeys.value
    : tableSummaryKeys.value;
});

// 消耗数据明细Columns
const tableDetailKeys = ref([
  {
    label: "日期",
    prop: DataCenterTableKeys.DATE,
  },
  {
    label: "开户主体",
    prop: DataCenterTableKeys.ACCOUNT_OPENING_ENTITY,
  },
  {
    label: "媒体",
    prop: DataCenterTableKeys.MEDIA,
    formatter: formatterMediaType,
  },
  {
    label: "账户ID",
    prop: DataCenterTableKeys.ACCOUNT_ID,
  },
  {
    label: "账户名称",
    prop: DataCenterTableKeys.ACCOUNT_NAME,
  },
  {
    label: "币种",
    prop: DataCenterTableKeys.CURRENCY,
  },
  {
    label: "消耗金额",
    prop: DataCenterTableKeys.CONSUME_AMOUNT,
  },
]);
// 消耗数据汇总
const tableSummaryKeys = ref([
  {
    label: "账户ID",
    prop: DataCenterTableKeys.ACCOUNT_ID,
  },
  {
    label: "账户名称",
    prop: DataCenterTableKeys.ACCOUNT_NAME,
  },
  {
    label: "开户主体",
    prop: DataCenterTableKeys.ACCOUNT_OPENING_ENTITY,
  },
  {
    label: "媒体",
    prop: DataCenterTableKeys.MEDIA,
    formatter: formatterMediaType,
  },
  {
    label: "币种",
    prop: DataCenterTableKeys.CURRENCY,
  },
  {
    label: "消耗金额",
    prop: DataCenterTableKeys.CONSUME_AMOUNT,
  },
]);
// 表格数据
const tableData = ref([]);

// 分页数据配置
const pagination = reactive<IPaging>({
  pageIndex: 1,
  pageSize: 10,
  total: 0,
});

const tabChange = (val) => {
  console.log(val);
  if (val.type === currentMedia.value) return;
  currentMedia.value = val.type;
  if (val.type === EMediaType.GOOGLE || val.type === EMediaType.LINKEDIN) {
    currentMediaCode.value = "CNY";
  } else {
    currentMediaCode.value = "USD";
  }
  getDailyConsumptionData();
  getAccountConsumptionRanking();
  getAccountConsumptionData();
};

// 格式化请求参数
const formatRequestParams = () => {
  let params = {};
  const formData = JSON.parse(JSON.stringify(searchFormInline));
  // 格式化时间日期参数
  if (formData[DataCenterQueryKeys.CONSUME_TIME].length) {
    formData[DataCenterQueryKeys.START_TIME] =
      formData[DataCenterQueryKeys.CONSUME_TIME][0];
    formData[DataCenterQueryKeys.END_TIME] =
      formData[DataCenterQueryKeys.CONSUME_TIME][1];
  }
  delete formData[DataCenterQueryKeys.CONSUME_TIME];
  params = {
    ...formData,
    ...pagination,
    mediumType: currentMedia.value,
  };

  return params;
};

const onSearch = () => {
  getDailyConsumptionData();
  getAccountConsumptionRanking();
  getAccountConsumptionData();
};
const onReset = () => {
  for (const key in searchFormInline) {
    console.log(key, "key");
    if (key === DataCenterQueryKeys.CONSUME_TIME) {
      searchFormInline[key] = [];
    } else {
      searchFormInline[key] = "";
    }
  }
  pagination.pageSize = 10;
  pagination.pageIndex = 1;
  onSearch();
};

// 获取开户主体数据
const getSubjectList = () => {
  getAccountOptionListApi({}).then((res: any) => {
    if (Array.isArray(res.data) && res.data.length > 0) {
      subjectList.value = res.data.map((item) => {
        return {
          label: item.customerMainName,
          value: item.customerMainName,
        };
      });
    }
  });
};

// 渲染折线图数据
const renderDataChart = () => {
  const chartDom = document.querySelector(".consumption-chart-detail");
  const myChart = echarts.init(chartDom as any);
  chartOption.value && myChart.setOption(chartOption.value);
};

// 获取账户消耗排名
const getAccountConsumptionRanking = () => {
  getAccountSummary(formatRequestParams()).then((res) => {
    if (res && res.code === "200" && res.data) {
      accountConsumptionList.value = res.data.list;
    }
  });
};

// 表格数据切换
const tableDataChange = (type: DataCenterAccountDataKeys) => {
  currentTab.value = type;
  getAccountConsumptionData();
};

// 获取每日消耗数据
const getDailyConsumptionData = () => {
  getDailyConsumption(formatRequestParams()).then((res) => {
    if (res && res.code === "200" && res.data) {
      totalConsumption.value = res.data.cost;
      const data = res.data.reportDataDayResList;
      if (Array.isArray(data) && data.length > 0) {
        chartOption.value.xAxis.data = data.map((item) => item.reportDate);
        chartOption.value.series[0].data = data.map((item) => item.cost);
      } else {
        chartOption.value.xAxis.data = [];
        chartOption.value.series[0].data = [];
      }
      renderDataChart();
    }
  });
};

// 获取账户消耗明细数据
const getAccountConsumptionData = () => {
  const currentApiName =
    currentTab.value === DataCenterAccountDataKeys.ACCOUNT_CONSUME_DETAIL
      ? getAccountConsumption
      : getAccountSummary;
  tableLoading.value = true;
  tableData.value = [];
  currentApiName(formatRequestParams())
    .then((res) => {
      if (res && res.code === "200" && res.data) {
        tableData.value = res.data.list;
        tableLoading.value = false;
      }
    })
    .catch((err) => {
      tableLoading.value = false;
    });
};

// 账户消耗数据导出
const exportExcel = (type) => {
  const currentUrl =
    type == 1
      ? "/merchant/manage/cost/exportDataDetail"
      : "/merchant/manage/cost/exportDataTotal";
  const currentName = type == 1 ? "账户消耗明细" : "账户消耗汇总";
  downloadFile({
    url: configLocation.baseURL + "/ad-trade-web" + currentUrl,
    params: formatRequestParams(),
    method: "POST",
    headers: {},
    name: currentName,
  });
};

onMounted(() => {
  getSubjectList();
  getDailyConsumptionData();
  getAccountConsumptionRanking();
  getAccountConsumptionData();
});
</script>

<style lang="scss" scoped>
.data-center {
  // background: #ffffff;
  border-radius: 8px;
  // margin: 20px;
  height: 100%;
  .titleMain {
    color: #202020;
    font-size: 20px;
    font-weight: 500;
    font-family: "PingFang SC";
    margin-bottom: 20px;
  }
  .mainContent {
    background-color: #fff;
    border-radius: 8px;
    height: calc(100% - 50px);
    overflow-y: auto;
  }
  .data-search-form {
    padding: 0 20px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .demo-form-inline {
      flex: 1;

      .el-form-item {
        width: 25%;
        margin-right: 0;
        padding-right: 20px;
      }
    }
    .updataTime {
      color: #999999;
      font-size: 12px;
      font-weight: 400;
    }
  }

  .consumption-data {
    width: 100%;
    height: 390px;
    padding: 0 20px;
    display: flex;
    .consumption-statistics {
      flex: 1;
      .total-consumption {
        height: 100px;
        padding: 16px 0 0 20px;
        border-radius: 8px;
        opacity: 1;
        background: linear-gradient(134.1deg, #e1efff 0%, #f2f3ff 100%);

        .title {
          color: #000000d9;
          font-size: 14px;
          font-weight: 500;
        }
        .totalConsumption {
          display: flex;
          align-items: center;
          .totalConsumptionDesc {
            color: #000000d9;
            font-size: 12px;
            font-weight: 400;
            margin-left: 6px;
          }
        }
        .amount {
          color: #000000d9;
          font-size: 20px;
          font-weight: 600;
          display: flex;
          align-items: center;
          margin-top: 20px;
          span {
            color: #000000d9;
            font-size: 14px;
            font-weight: 400;
            margin-right: 6px;
          }
        }
      }

      .daily-consumption {
        height: calc(100% - 120px);
        margin-top: 20px;

        .title {
          color: #333333;
          font-size: 16px;
          font-weight: 600;
          padding-left: 6px;
        }

        .consumption-chart-detail {
          height: calc(100% - 16px);
        }
      }
    }
    .consumption-ranking {
      flex: 1;
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      padding-top: 20px;
      box-shadow: 0 2px 7px 1px #0000001a;
      margin-left: 20px;
      .consumption-ranking-header {
        height: 22px;
        display: flex;
        align-items: center;
        padding: 0 20px;
        .title {
          color: #333333;
          font-size: 16px;
          font-weight: 600;
          margin-right: 8px;
        }
        .tip {
          color: #666666;
          font-size: 12px;
          font-weight: 400;
        }
      }
      .consumption-ranking-body {
        width: 100%;
        height: calc(100% - 52px);
        margin-top: 17px;
        .ranking-list {
          width: 100%;
          height: 100%;
          padding: 0 20px;
          overflow-y: auto;
          .ranking-item {
            width: 100%;
            height: 50px;
            padding: 0 12px;
            border-radius: 8px;
            margin-bottom: 4px;
            background: #f3f5f7;
            display: flex;
            align-items: center;

            .ranking-serial-number {
              width: 23px;
              height: 26px;
              line-height: 26px;
              text-align: center;
              color: #333333;
              font-size: 16px;
              font-weight: 700;

              .svg-icon {
                width: 100%;
                height: 100%;
              }
            }
            .ranking-serial-icon {
              width: 22px;
              height: 22px;
              margin: 0 10px;
            }
            .ranking-icon {
              margin: 0 8px;
              .svg-icon {
                width: 24px;
                height: 24px;
              }
            }

            .ranking-info {
              .name {
                color: #333333;
                font-size: 12px;
                font-weight: 500;
              }
              .id {
                color: #999999;
                font-size: 12px;
                font-weight: 400;
                margin-top: 4px;
              }
            }

            .ranking-consumption {
              flex: 1;
              color: #333333;
              font-size: 14px;
              font-weight: 700;
              text-align: right;
            }
          }
        }
      }
    }
  }

  .consumption-data-table {
    padding: 0 20px;
    margin-top: 20px;

    .consumption-data-table-header {
      .filter-button-group {
        .el-button {
          width: 100px;
          height: 36px;
        }
      }
      .operation-button-group {
        margin-top: 16px;
        .el-button {
          width: 88px;
          height: 32px;
        }
      }
    }

    .consumption-data-table-body {
      margin-top: 12px;
    }
  }

  .consumption-data-paging {
    padding: 12px 20px 20px;
    display: flex;
    justify-content: flex-end;

    :deep(.el-pagination) {
      .btn-prev,
      .btn-next,
      .el-pager > .number {
        width: 32px;
        height: 32px;
        color: #000000a6;
        font-size: 14px;
        font-weight: 400;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        background: #ffffff;

        &.is-active {
          border: none;
          color: #ffffff;
          background: #1890ff;
        }
      }

      .el-pagination__sizes {
        background: #ffffff;
        .el-select {
          width: 95px;
          height: 32px;
          color: #000000a6;
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }
}
</style>
