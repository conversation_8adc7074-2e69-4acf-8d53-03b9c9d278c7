<template>
  <h2 class="ads-title">
    <Breadcrumb />
    <span style="margin-top: 10px">报告日期截止昨天</span>
  </h2>
  <div class="ads-remainAmount">
    <div
      class="remainAmount-item"
      v-for="(item, index) in remainAmountList"
      :key="item.key"
    >
      <div class="item-top">
        <img :src="item.icon" alt="" class="top-icon" />{{ item.name }}
        <span style="flex: 1">本月消耗</span>
      </div>
      <div class="item-bottom" style="display: flex; justify-content: flex-end">
        {{ item.mediumType == 1 || item.mediumType == 6 ? "￥" : "$"
        }}<el-statistic :value="outputValue[index]" v-if="outputValue[index]" />
        <el-statistic v-else :value="'--'"></el-statistic>
      </div>
    </div>
  </div>
  <div style="background-color: #ffffff; padding: 20px">
    <div class="details-text">账户消耗明细</div>
    <el-select
      v-model="mediumType"
      placeholder="请选择媒体平台"
      @change="changeMediaType"
      clearable
      style="width: 25%"
    >
      <el-option
        v-for="item in subsriptionMeaid"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-input
      v-model="thirdAccountName"
      placeholder="广告账户名称或ID"
      clearable
      style="width: 25%; margin: 20px"
      :suffix-icon="Search"
    ></el-input>
    <el-date-picker
      clearable
      v-model="date"
      type="daterange"
      format="YYYY/MM/DD"
      value-format="YYYY-MM-DD"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      style="width: 25%;"
    />
    <el-button type="primary" @click="search" style="margin-left: 20px">
      查询
    </el-button>
    <el-button
      type="primary"
      @click="downloadTemplate"
      style="margin-left: 20px"
      :disabled="!AccountConsumptionControls"
    >
      下载
    </el-button>
    <TableCustom
      :columns="columns"
      :height="0"
      :total="total"
      :tableData="tableData"
      :currentPage="currentPage"
      :changePage="handleCurrentChange"
      :changePageSize="handleCurrentPageSize"
      :isShowPagination="true"
      :isAdmin="true"
      v-loading="loading"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import {
  costDetailPageList,
  costMonth,
  downloadCostDetail,
} from "@/api/accountConsumptionListApi";
import TableCustom from "@/components/table-custom.vue";
import GoogleIcon from "@/assets/img/Google-icon.png";
import YandexIcon from "@/assets/img/Yandex-icon.png";
import FacebookIcon from "@/assets/img/facebook-icon.png";
import TikTokIcon from "@/assets/img/tikTok-icon.png";
import BingIcon from "@/assets/img/bing-icon.png";

import LinkedinIcon from "@/assets/img/Linkedin-icon.png";
import type { FormInstance } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { getLastDaysRange, getMonthRange } from "@/utils/index";
import usePermissionStore from "@/store/modules/permission";
import { useTransition } from "@vueuse/core";
import Breadcrumb from "@/components/Breadcrumb/index.vue";
const permissionStore = usePermissionStore();

console.log(permissionStore?.buttonAuth);
const AccountConsumptionControls = computed(
  () =>
    permissionStore?.buttonAuth?.AccountConsumptionList?.indexOf(
      "AccountConsumptionControls"
    ) > -1
);
const loading = ref(false);
const activeTabs = ref(1);
const mediumType = ref();
const thirdAccountName = ref("");
const date = ref([]);
const total = ref(1);
const currentPage = ref(1);
const pageSize = ref(10);
const columns = ref([
  { prop: "reportDate", label: "日期" },
  { prop: "companyName", label: "客户名称" },
  { prop: "mediumType", label: "媒体" },
  { prop: "thirdAccountName", label: "账户名称" },
  { prop: "thirdAccountId", label: "账户ID" },
  { prop: "currency", label: "币种" },
  { prop: "cost", label: "消耗金额" },
]);
const subsriptionMeaid = reactive([
  {
    value: 1,
    label: "Google Ads",
  },
  {
    value: 2,
    label: "Yandex",
  },
  {
    value: 3,
    label: "Facebook",
  },
  {
    value: 4,
    label: "TikTok For Business",
  },
  {
    value: 5,
    label: "Bing Ads",
  },
  {
    value: 6,
    label: "LinkedIn",
  },
]);
const mediumTypeMapping = {
  1: "Google Ads",
  2: "Yandex",
  3: "Facebook",
  4: "TikTok For Business",
  5: "Bing Ads",
  6: "LinkedIn",
};
const tableData = ref([]);
const remainAmountList = ref([
  {
    name: "Google Ads",
    key: "googleCostAmount",
    icon: GoogleIcon,
    mediumType: 1,
  },
  {
    name: "Yandex",
    key: "yandexCostAmount",
    icon: YandexIcon,
    mediumType: 2,
  },
  {
    name: "Facebook",
    key: "facebookCostAmount",
    icon: FacebookIcon,
    mediumType: 3,
  },
  {
    name: "TikTok For Business",
    key: "tiktokCostAmount",
    icon: TikTokIcon,
    mediumType: 4,
  },
  {
    name: "Bing Ads",
    key: "bingCostAmount",
    icon: BingIcon,
    mediumType: 5,
  },
  {
    name: "LinkedIn",
    key: "linkedinCostAmount",
    icon: LinkedinIcon,
    mediumType: 6,
  },
]);
let numList = ref([0, 0, 0, 0, 0, 0, 0]) as any;
const outputValue = useTransition(numList, {
  duration: 1000,
});
onMounted(() => {
  // date.value = getLastDaysRange(30);
  getCostMonth();
  getCostDetailPageList();
});

const getCostMonth = async () => {
  const { firstDay, lastDay } = getMonthRange();
  try {
    const res: any = await costMonth({
      startDate: firstDay,
      endDate: lastDay,
    });
    if (res?.code == 200) {
      if (!res.data) return;
      numList.value = [
        res?.data?.googleCostAmount,
        res?.data?.yandexCostAmount,
        res?.data?.facebookCostAmount,
        res?.data?.tiktokCostAmount,
        res?.data?.bingCostAmount,
        res?.data?.linkedinCostAmount,
      ];
    }
  } catch (error) {
    console.log(error);
  }
};
const getCostDetailPageList = async () => {
  try {
    const params = {
      mediumType: mediumType.value,
      startDate: date.value?.[0] || "",
      endDate: date.value?.[1] || "",
      thirdAccountName: thirdAccountName.value,
      pageIndex: currentPage.value,
      pageSize: pageSize.value,
    };
    loading.value = true;
    const res: any = await costDetailPageList(params);
    loading.value = false;
    if (res?.code == 200) {
      tableData.value = res?.data?.list;
      total.value = res?.data?.total;
      tableData.value.forEach((item) => {
        item.mediumType = mediumTypeMapping[item.mediumType];
      });
    }
  } catch (error) {
    console.log(error);
  }
};
const changeMediaType = () => {
  currentPage.value = 1;
  activeTabs.value = mediumType.value;
  getCostDetailPageList();
};
const changeTabs = () => {
  currentPage.value = 1;
  mediumType.value = activeTabs.value;
  getCostDetailPageList();
};
const downloadTemplate = async () => {
  try {
    const res: any = await downloadCostDetail({
      customerId: "",
      mediumType: mediumType.value,
      startDate: date.value?.[0] || "",
      endDate: date.value?.[1] || "",
    });
    if (res) {
      const binaryString = window.atob(res.data); // 解码Base64数据
      const len = binaryString.length;
      const bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const blob = new Blob([bytes], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `账户消耗报表.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error("下载模板时出错:", error); // 添加错误日志
  }
};
const search = () => {
  currentPage.value = 1;
  getCostDetailPageList();
};
// 分页
const handleCurrentChange = (value: number) => {
  console.log(value);
  currentPage.value = value;
  getCostDetailPageList();
};
// 选择每页条数
const handleCurrentPageSize = (val: number) => {
  console.log(val);
  pageSize.value = val;
  getCostDetailPageList();
};
// 确认新增订阅
const okSubscription = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
};
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: PingFangSC-Medium;
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0px 0 20px;
  span {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    display: flex;
    align-items: center;
  }
}
.details-text {
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  letter-spacing: 0;
  margin-bottom: 20px;
}
.ads-remainAmount {
  display: flex;
  gap: 15px;
  margin-bottom: 12px;
  width: 100%;
  flex-wrap: nowrap;
  overflow-x: auto;
  .remainAmount-item {
    width: 298px;
    height: 91px;
    background: #ffffff;
    border-radius: 3px;
    padding: 16px;
    box-sizing: border-box;

    .item-top {
      display: flex;
      text-align: right;
      align-items: center;
      font-family: PingFangSC-Medium;
      font-weight: bold;
      font-size: 14px;
      color: #303133;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #606266;
        letter-spacing: 0;
      }
      .top-icon {
        width: 16px;
        height: 16px;
        margin-right: 12px;
      }
    }
    .item-bottom {
      text-align: right;
      font-family: HelveticaNeue-Medium;
      font-weight: 500;
      font-size: 24px;
      color: #303133;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 12px;
    }
  }
}
.ads-remainAmount::-webkit-scrollbar {
  height: 5px; /* 滚动条的高度 */
}
.demo-tabs {
  box-sizing: border-box;
  padding: 20px;
  //   height: calc(100vh - 251px);
}

.custom-tabs-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;

  .tabs-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}
</style>
