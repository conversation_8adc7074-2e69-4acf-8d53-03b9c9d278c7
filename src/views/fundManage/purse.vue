<template>
  <h2 class="ads-title">
    <Breadcrumb />
  </h2>
  <div class="purseMain">
    <div class="purseTopMain">
      <div class="purseTop">
        <div class="topTitle">
          广告总预存金额
          <el-tooltip
            effect="dark"
            content="仅可用于广告账户的充值，充值时需扣除6%税点"
            placement="top"
          >
            <el-icon style="margin-left: 5px"><Warning /></el-icon>
          </el-tooltip>
        </div>
        <div class="amount">
          <div class="unit">¥</div>
          {{ balance }}
        </div>
        <div class="frozen">
          <div class="frozenLable">冻结金额</div>
          <div class="frozenvalue">
            <div class="unit">¥</div>
            {{ freezeAmount }}
          </div>
          <el-tooltip placement="top">
            <template #content>
              当发起媒体账户的充值申请后，系统会将指定充
              <br />
              值的金额从可用资金中扣除充值操作完成之前，
              <br />
              此项资金记录将被暂时标记为冻结资金。
            </template>
            <el-icon style="margin-left: 5px"><Warning /></el-icon>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="purseContent">
      <div class="contentMain" v-for="(item, index) in platList" :key="index">
        <div class="contentTitle">
          <img :src="item.icon" alt="" class="iconImg" />
          <div class="titleMain">{{ item.name }}</div>
        </div>
        <div class="contentMoney">
          <div class="unit">{{ item.currency }}</div>
          {{ item.balance || "0.00" }}
        </div>
        <div
          class="btn"
          @click="goUrl(item.path)"
          v-if="permissionStore?.buttonAuth?.Purse?.indexOf('PurseBtn') != -1"
        >
          充值
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import iconMeta from "@/assets/images/home/<USER>";
import iconGoogle from "@/assets/images/home/<USER>";
import iconTikTok from "@/assets/images/home/<USER>";
import iconBing from "@/assets/images/home/<USER>";
import iconYandex from "@/assets/images/home/<USER>";
import iconLink from "@/assets/images/home/<USER>";
import { getPurseAll, getPursePlat } from "@/api/fundManage";
import { EMediaType, MediaType } from "@/views/workbench/enum";
import usePermissionStore from "@/store/modules/permission";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const permissionStore = usePermissionStore();

let platList = ref([
  {
    type: "google",
    name: "Google Ads",
    currency: "¥",
    balance: 0.0,
    key: "googleRemainAmount",
    icon: iconGoogle,
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.GOOGLE}`,
  },
  {
    type: "yandex",
    name: "Yandex",
    currency: "$",
    balance: 0.0,
    key: "yandexRemainAmount",
    icon: iconYandex,
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.YANDEX}`,
  },
  {
    type: "meta",
    name: "Meta",
    currency: "$",
    balance: 0.0,
    key: "facebookRemainAmount",
    icon: iconMeta,
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.META}`,
  },
  {
    type: "tiktok",
    name: "TikTok",
    currency: "$",
    balance: 0.0,
    key: "tiktokRemainAmount",
    icon: iconTikTok,
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.TIKTOK}`,
  },
  {
    type: "bing",
    name: "Bing Ads",
    currency: "$",
    balance: 0.0,
    key: "bingRemainAmount",
    icon: iconBing,
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.BING}`,
  },
  {
    type: "linkedln",
    name: "LinkedIn",
    currency: "¥",
    balance: 0.0,
    key: "linkedinRemainAmount",
    icon: iconLink,
    path: `/ads/accountManage/account-list?mediaType=${EMediaType.LINKEDIN}`,
  },
]);
let balance = ref("") as any; // 总余额
let freezeAmount = ref("") as any; // 冻结金额
// 获取全部钱包额度
const getDataAll = async () => {
  try {
    let { code, data } = await getPurseAll();
    if (code && code == 200 && data) {
      // 保留两位小数
      balance.value = data.balance ? data.balance.toFixed(2) : "0.00";
      freezeAmount.value = data.freezeAmount;
    }
  } catch (error) {
    console.error(error);
  }
};
// 获取平台钱包额度
const getData = async () => {
  try {
    let { code, data } = await getPursePlat({ customerId: "" });
    if (code && code == 200 && data) {
      for (let key in data) {
        let index = platList.value.findIndex((item) => item.key == key);
        if (index != -1) {
          platList.value[index].balance = data[key];
        }
      }
    }
  } catch (error) {
    console.error(error);
  }
};
// 跳转链接
const goUrl = (path: string) => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: path,
      name: "ads",
      parentPath: "/ads/workbench",
      isChild: false,
    });
  }
};
onMounted(() => {
  getDataAll();
  getData();
});
</script>
<style lang="scss" scoped>
.ads-title {
  font-family: "PingFangSC-Medium";
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  cursor: pointer;
  display: flex;
  align-items: center;
  span {
    flex: 1;
    margin-left: 10px;
  }
}
.purseMain {
  width: 100%;
  height: calc(100% - 80px);
  border-radius: 8px;
  box-sizing: border-box;
  overflow-y: auto;
  .purseTopMain {
    background-color: #fff;
    border-radius: 12px;
    .purseTop {
      width: 100%;
      height: 196px;
      padding: 20px;
      box-sizing: border-box;
      background: url("@/assets/img/purseBg.png") no-repeat center;
      background-size: 100% 100%;
      .topTitle {
        color: #333333;
        font-size: 16px;
        font-weight: 500;
        font-family: "PingFang SC";
        display: flex;
        align-items: center;
      }
      .amount {
        color: var(--el-color-primary);
        font-size: 36px;
        font-weight: 500;
        font-family: "PingFang SC";
        display: flex;
        align-items: center;
        padding-top: 20px;
        .unit {
          font-size: 16px;
          margin-right: 6px;
          margin-top: 8px;
        }
      }
      .frozen {
        display: flex;
        align-items: center;
        padding-top: 40px;
        .frozenLable {
          color: #333333;
          font-size: 14px;
          font-weight: 400;
          font-family: "PingFang SC";
          margin-right: 12px;
        }
        .frozenvalue {
          color: #333333;
          font-size: 14px;
          font-weight: 500;
          font-family: "PingFang SC";
          display: flex;
          align-items: center;
          margin-right: 4px;
          .unit {
            margin-right: 2px;
          }
        }
      }
    }
  }
}
.purseContent {
  width: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .contentMain {
    width: calc(100% / 3 - 22px);
    height: 120px;
    border-radius: 8px;
    background-color: #fff;
    margin-bottom: 20px;
    padding: 16px;
    box-sizing: border-box;
    position: relative;
    .contentTitle {
      display: flex;
      align-items: center;
      .iconImg {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
      .titleMain {
        color: #333333;
        font-size: 14px;
        font-weight: 600;
        font-family: "PingFang SC";
      }
    }
    .contentMoney {
      display: flex;
      align-items: center;
      color: #333333;
      font-size: 24px;
      font-weight: 600;
      font-family: "PingFang SC";
      padding-top: 25px;
      .unit {
        font-size: 14px;
        margin-right: 4px;
        margin-top: 4px;
      }
    }
    .btn {
      position: absolute;
      width: 64px;
      height: 32px;
      border-radius: 4px;
      background: var(--el-color-primary);
      text-align: center;
      line-height: 32px;
      cursor: pointer;
      color: #ffffff;
      font-size: 14px;
      font-weight: 400;
      font-family: "PingFang SC";
      right: 24px;
      bottom: 24px;
    }
  }
}
</style>
