<template>
  <h2 class="ads-title">
    <Breadcrumb />
  </h2>
  <div class="recharge-history">
    <!-- 表格组件 -->
    <DynamicTable
      ref="dyTableRef"
      :form-items="formItems"
      :columns="columns"
      :initial-data="tableData"
      :showFormLable="false"
      v-loading="loading"
      @search="getData"
    >
      <template #formBtn>
        <el-button
          type="primary"
          @click="exportExcel"
          v-if="
            permissionStore?.buttonAuth?.DetailChanges?.indexOf(
              'DetailChangesBtn',
            ) != -1
          "
          >导出
        </el-button>
      </template>
      <template #headerSlot="{ row }">
        <div style="display: flex; align-items: center">
          {{ row.label }}
          <el-tooltip
            effect="dark"
            content="实际减款金额=申请减款金额*当日实时汇率"
            placement="top"
          >
            <el-icon style="margin-left: 5px">
              <Warning />
            </el-icon>
          </el-tooltip>
        </div>
      </template>
      <template #middle>
        <div class="forewarning">
          <div
            class="forewarningDesc"
            @click="goInvoice()"
            v-if="
              permissionStore?.buttonAuth?.DetailChanges?.indexOf(
                'DetailChangesBtn',
              ) != -1
            "
          >
            我要开票
          </div>
        </div>
      </template>
    </DynamicTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, h } from "vue";
import DynamicTable from "@/components/dynamicTable.vue";
import { ElInput, ElButton, ElDatePicker, ElSelect } from "element-plus";
import { getDetailsFunds } from "@/api/fundManage";
import { downloadFile } from "@/utils/common";
import configLocation from "@/config";
import usePermissionStore from "@/store/modules/permission";

const permissionStore = usePermissionStore();

// 表格实例组件
const dyTableRef = ref<InstanceType<typeof DynamicTable>>(null);
// 公司名称
const merchantName = ref("");
let businessTypeList = [
  {
    label: "钱包充值",
    value: 1,
  },
  {
    label: "广告账户充值",
    value: 2,
  },
  {
    label: "广告账户减款",
    value: 3,
  },
  {
    label: "订单退款",
    value: 5,
  },
];
// 动态表单配置
const formItems = [
  {
    label: "公司名称",
    prop: "merchantName",
    component: ElInput,
    placeholder: "请输入公司名称",
    defaultValue: merchantName.value,
  },
  {
    label: "完成交易时间",
    prop: "date",
    component: ElDatePicker,
    defaultValue: [],
    type: "daterange",
    rangeSeparator: "-",
    props: {
      startPlaceholder: "开始日期",
      endPlaceholder: "结束日期",
      valueFormat: "YYYY-MM-DD",
      clearable: true,
    },
    width: "380px",
  },
  {
    label: "交易类型",
    prop: "businessType",
    component: ElSelect,
    props: {
      selectOptions: businessTypeList,
    },
  },
];
const btnAction = (row: any) => {
  let path = "";
  if (row.businessType == 2) {
    path = "/ads/accountManage/rechargeHistory";
  } else if (row.businessType == 3) {
    path = "/ads/accountManage/deductionHistory";
  }
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: path,
      name: "ads",
      parentPath: "/ads/:page*",
      isChild: false,
    });
  }
};
const btnElement = (row: any) => {
  let btnList = [];
  if (
    row.businessType != 1 &&
    permissionStore?.buttonAuth?.DetailChanges?.indexOf("DetailChangesBtn") !=
      -1 &&
    row.businessType != 5
  ) {
    btnList = [
      {
        label: row.businessType == 2 ? "充值记录" : "减款记录",
        type: 1,
        callback: btnAction,
        color: "#519C66",
        bgColor: "rgba(50, 147, 111, 0.1)",
      },
    ];
  }
  if (btnList.length == 0) {
    return h(
      "div",
      {},
      h(
        {
          underline: false,
          type: "primary",
          style: {},
        },
        () => "--",
      ),
    );
  } else {
    return h(
      "div",
      {},
      btnList.map((item, index) => {
        return h(
          ElButton,
          {
            style: {
              height: "24px",
              fontSize: "13px",
              fontWeight: 400,
              color: item.color,
              backgroundColor: item.bgColor,
              border: "none",
              borderRadius: "8px",
              marginRight: "10px",
              padding: "4px 8px",
            },
            onClick: () => item.callback(row),
          },
          () => item.label,
        );
      }),
    );
  }
};
const columns = [
  {
    label: "流水编号",
    prop: "transactionNo",
  },
  {
    label: "公司名称",
    prop: "customerName",
  },
  {
    label: "交易类型 ",
    prop: "businessType",
    formatter: (row, column, cellValue) => {
      let item = businessTypeList.find((item) => item.value == cellValue);
      return item?.label;
    },
  },
  {
    label: "交易状态 ",
    prop: "transactionStatus",
    formatter: (row, column, cellValue) => {
      // 1: 进行中 2: 已完成 3: 交易失败
      return cellValue == 1
        ? "进行中"
        : cellValue == 2
        ? "已完成"
        : cellValue == 3
        ? "交易失败"
        : "";
    },
  },
  {
    label: "交易币种",
    prop: "currency",
  },
  {
    label: "交易金额",
    prop: "amount",
    formatter: (row, column, cellValue) => {
      return row.transactionType == 1 ? "+" + cellValue : "-" + cellValue;
    },
  },
  {
    label: "完成交易时间",
    prop: "updateTime",
  },
  {
    label: "备注",
    width: "100px",
    prop: (row: any) => btnElement(row),
  },
];
// loading
const loading = ref(false);
// 表格数据
const tableData = ref([]);
let baseParamsReport = ref({});

const handlerParams = (value) => {
  let params = {
    pageSize: value?.pageSize || 10,
    pageIndex: value?.currentPage || 1,
    updateTimeStart: value?.date.length == 0 ? "" : value?.date[0],
    updateTimeEnd: value?.date.length == 0 ? "" : value?.date[1],
    businessType: value?.businessType,
    transactionType: value?.transactionType,
    merchantName: value?.merchantName,
  };
  baseParamsReport.value = params;
  return params;
};
// 获取资金变动明细
const getData = async (data?: any) => {
  loading.value = true;
  const params = handlerParams(data);
  try {
    let { code, data } = await getDetailsFunds(params);
    if (code && code == 200 && data) {
      tableData.value = data.list;
      dyTableRef.value.setPageTotal(data.total);
    } else {
      tableData.value = [];
      dyTableRef.value.setPageTotal(0);
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// excel数据导出
const exportExcel = () => {
  downloadFile({
    url:
      configLocation.baseURL +
      "/ad-trade-web/merchant/manage/wallet/exportTrans",
    params: baseParamsReport.value,
    method: "POST",
    headers: {},
    name: "资金变动明细",
  });
};

const goInvoice = () => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type: "router",
      path: "/enterpriseCenter/myInvoice",
      name: "ads",
      parentPath: "/ads/:page*",
      isChild: false,
    });
  }
};

onMounted(() => {
  getData();
});
</script>

<style lang="scss" scoped>
.ads-title {
  font-family: "PingFangSC-Medium";
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  cursor: pointer;
  display: flex;
  align-items: center;

  span {
    flex: 1;
    margin-left: 10px;
  }
}

.recharge-history {
  width: 100%;
  height: calc(100% - 80px);
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  overflow-y: auto;
}

.tooltip-div {
  .select_icon {
    font-size: 17px;
    color: var(--el-color-primary);
    margin-left: 10px;
  }
}

.forewarning {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;

  .forewarningDesc {
    color: var(--el-color-primary);
    font-size: 14px;
    font-weight: 500;
    font-family: "PingFang SC";
    text-decoration: underline;
    cursor: pointer;
    margin-left: 24px;
  }
}
</style>
