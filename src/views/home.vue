<template>
  <div class="wrapper">
    <router-view v-slot="{ Component }">
      <keep-alive :include="tabs.nameList">
        <component :is="Component"></component>
      </keep-alive>
    </router-view>
  </div>
</template>
<script setup lang="ts">
import { useSidebarStore } from "@/store/sidebar";
import { useTabsStore } from "@/store/tabs";

const sidebar = useSidebarStore();
const tabs = useTabsStore();
</script>

<style>
.wrapper {
  width: 100%;
  height: calc(100vh - 60px);
  background-color: #f5f5fa;
  padding: 20px;
  position: relative;
}
</style>
