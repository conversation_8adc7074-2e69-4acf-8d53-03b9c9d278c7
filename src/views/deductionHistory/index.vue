<template>
  <h2 class="ads-title">
    <Breadcrumb />
  </h2>
  <div class="recharge-history">
    <!-- 公共tabs组件 -->
    <CommonTabs :tab-list="tabList" @tabs-change="tabsChange" />
    <!-- 表格组件 -->
    <DynamicTable
      ref="dyTableRef"
      :form-items="formItems"
      :columns="columns"
      :initial-data="tableData"
      :showFormLable="false"
      v-loading="loading"
      @search="getRechargeList"
    >
      <template #formBtn>
        <el-button
          type="primary"
          @click="exportExcel"
          v-if="
            permissionStore?.buttonAuth?.DeductionHistory?.indexOf(
              'DeductionHistoryBtn',
            ) != -1
          "
          >导出</el-button
        >
      </template>
      <template #headerSlot="{ row }">
        <div style="display: flex; align-items: center">
          {{ row.label }}
          <el-tooltip
            effect="dark"
            content="实际减款金额=申请减款金额*当日实时汇率"
            placement="top"
          >
            <el-icon style="margin-left: 5px"><Warning /></el-icon>
          </el-tooltip>
        </div>
      </template>
      <template #custom="{ row }">
        <div class="tooltip-div f aic">
          {{
            row[ETableColumnKeys.CLEARING_STATUS] == 1
              ? "减款中"
              : row[ETableColumnKeys.CLEARING_STATUS] == 2
              ? "减款成功"
              : "减款失败"
          }}
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.auditMsg"
            placement="bottom"
            v-if="row[ETableColumnKeys.CLEARING_STATUS] == 3"
          >
            <el-icon class="select_icon">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </div>
      </template>
    </DynamicTable>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from "vue";
import CommonTabs from "@/views/pages/accountList/components/common/commonTabs.vue";
import DynamicTable from "@/components/dynamicTable.vue";
import { EFormPropKeys, ETableColumnKeys } from "./enum/rechargeHistory";
import { EMediaType } from "./enum/components";
import { ElInput, ElDatePicker, ElSelect } from "element-plus";
import { getDeductionHistory } from "@/api/deductionHistory";
import { formatterMediaType, formatterRechargeStatus } from "@/utils/formatter";
import {
  downloadFile,
  getQueryParamsByKey,
  validateTimeRange,
} from "@/utils/common";
import configLocation from "@/config";
import { useRoute } from "vue-router";
import usePermissionStore from "@/store/modules/permission";
import Breadcrumb from "@/components/Breadcrumb/index.vue";

const permissionStore = usePermissionStore();
let route = useRoute();

// 当前tabs类型
const currentTab = ref("1") as any;
// tabs列表
const tabList = ref([
  {
    label: "减款进度",
    name: "1",
  },
  {
    label: "减款记录",
    name: "2",
  },
]);

// 表格实例组件
const dyTableRef = ref<InstanceType<typeof DynamicTable>>(null);
// 动态账户名称
const accountName = ref<string>(getQueryParamsByKey("accountName") ?? "");
// 动态表单配置
const formItems = computed(() => {
  return [
    {
      label: "广告账户名称",
      prop: EFormPropKeys.ACCOUNT_NAME,
      component: ElInput,
      placeholder: "广告账户名称或ID搜索",
      defaultValue: accountName.value,
    },
    {
      label: currentTab.value == "1" ? "申请充值时间" : "完成充值时间",
      prop: EFormPropKeys.APPLY_TIME,
      component: ElDatePicker,
      defaultValue: [],
      type: "datetimerange",
      rangeSeparator: "-",
      props: {
        startPlaceholder: "开始日期",
        endPlaceholder: "结束日期",
        valueFormat: "YYYY-MM-DD hh:mm:ss",
        clearable: true,
        onChange: validateTimeRange,
      },
    },
    {
      label: "减款状态",
      prop: EFormPropKeys.DEDUCTION_STATUS,
      component: ElSelect,
      placeholder: "请选择减款状态",
      props: {
        selectOptions: [
          {
            label: "减款中",
            value: 1,
          },
          {
            label: "减款完成",
            value: 2,
          },
          {
            label: "减款失败",
            value: 3,
          },
        ],
      },
    },
    {
      label: "媒体",
      prop: EFormPropKeys.MEDIA,
      component: ElSelect,
      placeholder: "请选择媒体",
      props: {
        selectOptions: [
          {
            label: "google",
            value: EMediaType.GOOGLE,
          },
          {
            label: "yandex",
            value: EMediaType.YANDEX,
          },
          {
            label: "meta",
            value: EMediaType.META,
          },
          {
            label: "tiktok",
            value: EMediaType.TIKTOK,
          },
          {
            label: "bing",
            value: EMediaType.BING,
          },
          {
            label: "linkedin",
            value: EMediaType.LINKEDIN,
          },
        ],
      },
    },
    {
      label: "订单号搜索",
      prop: EFormPropKeys.ORDER_NO,
      component: ElInput,
      placeholder: "订单号搜索",
      defaultValue: accountName.value,
    },
  ].filter((item) => {
    if (currentTab.value == "1") {
      return item.prop !== EFormPropKeys.ORDER_NO;
    } else {
      return item.prop !== EFormPropKeys.DEDUCTION_STATUS;
    }
  });
});

// 动态表格列配置
const columns = computed(() => {
  return currentTab.value == "2"
    ? rechargeRecordKeys.value
    : rechargeProgressKeys.value;
});

// 减款进度列配置
const rechargeProgressKeys = ref([
  {
    label: "媒体",
    prop: ETableColumnKeys.MEDIA,
    formatter: formatterMediaType,
  },
  { label: "广告账户名称", prop: ETableColumnKeys.ACCOUNT_NAME },
  { label: "广告账户ID", prop: ETableColumnKeys.ACCOUNT_ID },
  {
    label: "申请类型",
    prop: ETableColumnKeys.CLEARING_TYPE,
    formatter: (row: any) => {
      return row[ETableColumnKeys.CLEARING_TYPE] === 1
        ? "清零"
        : row[ETableColumnKeys.CLEARING_TYPE] === 2
        ? "减款"
        : "";
    },
  },
  { label: "账户币种", prop: ETableColumnKeys.CURRENCY },
  { label: "申请减款金额", prop: ETableColumnKeys.CLEARING_AMOUNT },
  {
    label: "减款状态",
    prop: ETableColumnKeys.CLEARING_STATUS,
    type: "custom",
  },
  { label: "订单号", prop: ETableColumnKeys.ORDER_NO },
  { label: "申请减款时间", prop: ETableColumnKeys.APPLY_TIME },
  { label: "操作人", prop: ETableColumnKeys.OPERATOR_NAME },
]) as any;

// 减款记录列配置
const rechargeRecordKeys = ref([
  { label: "订单号", prop: ETableColumnKeys.ORDER_NO },
  {
    label: "媒体",
    prop: ETableColumnKeys.MEDIA,
    formatter: formatterMediaType,
  },
  { label: "广告账户名称", prop: ETableColumnKeys.ACCOUNT_NAME },
  { label: "广告账户ID", prop: ETableColumnKeys.ACCOUNT_ID },
  {
    label: "申请类型",
    prop: ETableColumnKeys.CLEARING_TYPE,
    formatter: (row: any) => {
      return row[ETableColumnKeys.CLEARING_TYPE] === 1
        ? "清零"
        : row[ETableColumnKeys.CLEARING_TYPE] === 2
        ? "减款"
        : "";
    },
  },
  { label: "账户币种", prop: ETableColumnKeys.CURRENCY },
  { label: "申请减款金额", prop: ETableColumnKeys.CLEARING_AMOUNT },
  {
    label: "实际减款金额(CNY)",
    prop: ETableColumnKeys.ACTUAL_CLEARING_AMOUNT,
    headerType: "custom",
  },
  { label: "汇率", prop: ETableColumnKeys.EXCHANGE_RATE },
  { label: "减款完成时间", prop: ETableColumnKeys.CLEARING_TIME },
]) as any;
// loading
const loading = ref(false);
// 表格数据
const tableData = ref([]);
// 记录查询参数数据
const saveParams = ref({});

// 充值管理类型切换
const tabsChange = (tab) => {
  currentTab.value = tab;
  dyTableRef.value.resetForm();
};

// 格式化查询参数
const formatQueryParams = (data) => {
  let params = {} as any;
  if (data) {
    const [startDate, endDate] = data[EFormPropKeys.APPLY_TIME];
    params = {
      pageIndex: data.currentPage,
      pageSize: data.pageSize,
      [EFormPropKeys.ACCOUNT_NAME]: data[EFormPropKeys.ACCOUNT_NAME],
      [EFormPropKeys.DEDUCTION_STATUS]: data[EFormPropKeys.DEDUCTION_STATUS],
      startDate,
      endDate,
      [EFormPropKeys.MEDIA]: data[EFormPropKeys.MEDIA],
      selectType: currentTab.value,
      [EFormPropKeys.ORDER_NO]: data[EFormPropKeys.ORDER_NO],
    };
  } else {
    params = {
      pageIndex: 1,
      pageSize: 10,
      [EFormPropKeys.ACCOUNT_NAME]: accountName.value,
    };
  }
  // 区分 记录和进度
  params.selectType = currentTab.value;
  saveParams.value = params;
  return params;
};

// 获取减款列表
const getRechargeList = (data?) => {
  loading.value = true;
  const params = formatQueryParams(data);
  getDeductionHistory(params)
    .then((res) => {
      loading.value = false;
      tableData.value = res.data.list;
      dyTableRef.value.setPageTotal(res.data.total);
    })
    .catch((err) => {
      console.log(err, "err");
      loading.value = false;
    });
};

// excel数据导出
const exportExcel = () => {
  downloadFile({
    url: configLocation.baseURL + "/ad-trade-web/account/reset/viewDownload",
    params: {
      ...saveParams.value,
      selectType: currentTab.value,
    },
    method: "POST",
    headers: {},
    name: currentTab.value == "1" ? "清零进度" : "清零记录",
  });
};

onMounted(() => {
  if (route.query && route.query.currentTab) {
    currentTab.value = route.query.currentTab;
  }
  getRechargeList();
});
</script>

<style lang="scss" scoped>
.ads-title {
  font-family: "PingFangSC-Medium";
  font-weight: bold;
  font-size: 20px;
  color: #202020;
  margin: 0 0 20px;
  display: flex;
  cursor: pointer;
  display: flex;
  align-items: center;
  span {
    flex: 1;
    margin-left: 10px;
  }
}
.recharge-history {
  width: 100%;
  height: calc(100% - 80px);
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  overflow-y: auto;
}
.tooltip-div {
  .select_icon {
    font-size: 17px;
    color: var(--el-color-primary);
    margin-left: 10px;
  }
}
</style>
