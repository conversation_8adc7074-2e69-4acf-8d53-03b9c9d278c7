import { DirectiveBinding } from "vue";
import usePermissionStore from "@/store/modules/permission";

// 定义一个 v-focus 指令
const vPermission = {
  // 当绑定元素插入到 DOM 中时
  mounted(el: HTMLElement, binding: any) {
    const permissionStore = usePermissionStore();
    switch (binding.arg) {
      case "btn": // 控制按钮的显示
        if (
          !(
            permissionStore?.buttonAuth?.AccountList?.indexOf(
              binding?.modifiers,
            ) != -1 ||
            permissionStore?.buttonAuth?.Workbench?.indexOf(
              binding?.modifiers,
            ) != -1
          ) &&
          el?.parentNode
        ) {
          el.parentNode.removeChild(el);
        }
        break;
      case "other": // 控制其他内容
        if (
          !(
            permissionStore?.buttonAuth?.AccountList?.indexOf(binding?.value) !=
              -1 ||
            permissionStore?.buttonAuth?.Workbench?.indexOf(binding?.value) !=
              -1
          ) &&
          el?.parentNode
        ) {
          el.parentNode.removeChild(el);
        }
        break;
    }
  },
};
export default vPermission;
