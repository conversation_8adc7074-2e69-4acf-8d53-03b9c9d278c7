<template>
	<el-config-provider :locale="zhCn">
		<router-view :key="route.path"/>
	</el-config-provider>
</template>

<script setup lang="ts">
import usePermissionStore from "@/store/modules/permission"
import { onMounted } from "vue"
import { ElConfigProvider } from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import { useRoute } from 'vue-router';
const route = useRoute();

const permissionStore = usePermissionStore()
onMounted(() => {
  if (window.microApp) {
    const data = window.microApp.getData()
    permissionStore.setArrAuth(data.menuAuth)
    permissionStore.setButtonAuth(data.buttonAuth)
  }
})
</script>
<style>
@import './assets/css/main.css';
</style>
