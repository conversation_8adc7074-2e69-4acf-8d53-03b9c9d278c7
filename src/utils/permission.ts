const modules = import.meta.glob("/src/views/**/**.vue")
/**
 * 递归过滤有权限的动态路由
 *
 * @param routes 接口返回所有的动态路由
 * @returns 返回用户有权限的动态路由
 */
export const filterAsyncRoutes = (routes: any) => {
  const asyncRoutes: any = []
  routes.forEach((route: any) => {
    const tmpRoute = { ...route } // 深拷贝 route 对象 避免污染

    // 是否存在组件
    if (tmpRoute.component) {
      const component = modules[`/src/views/${tmpRoute.component}.vue`]
      // 获取到组件
      if (component) {
        tmpRoute.component = component
      } else {
        // tmpRoute.component = modules[`../views/error/404.vue`]
        tmpRoute.component = () => import(`@/views/pages/404.vue`)
      }
    }

    if (tmpRoute.children && !tmpRoute.meta?.isChild) {
      tmpRoute.children = filterAsyncRoutes(route.children)
    }
    asyncRoutes.push(tmpRoute)
  })

  return asyncRoutes
}
