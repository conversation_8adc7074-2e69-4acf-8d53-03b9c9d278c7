import {
  TiktokEnums,
  YandexEnums,
  BingEnums,
  MetaEnums,
  LinkedinEnums,
  BatchOpenAccountEnum,
  GoogleEnums,
} from "@/views/pages/accountOpenManagement/accountOpenApply/enums";
const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/;
import { cityList } from "./mapping";
// 身份证校验规则
const validateIdCard = (rule, value, callback) => {
  if (value) {
    // 基本格式验证
    const reg =
      /(^[1-9]\d{5}(18|19|([23]\d))\d{2}(0[1-9]|1[0-2])([0-2][1-9]|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}(0[1-9]|1[0-2])([0-2][1-9]|10|20|30|31)\d{2}[0-9Xx]$)/;

    if (!reg.test(value)) {
      return callback(new Error("身份证格式错误"));
    }

    // 地区码校验
    const cityCode = value.substring(0, 2);

    if (!cityList[cityCode]) {
      return callback(new Error("地区码无效"));
    }

    // 18位身份证校验位验证
    if (value.length === 18) {
      const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const parity = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
      let sum = 0;

      for (let i = 0; i < 17; i++) {
        sum += value[i] * factor[i];
      }

      const lastChar = value[17].toUpperCase();
      if (lastChar !== parity[sum % 11]) {
        return callback(new Error("校验码错误"));
      }
    }
    callback();
  } else {
    callback();
  }
};
export let googleRules = {
  [GoogleEnums.ADVERTISING_ACCOUNT_NAME]: [
    { required: true, message: "请输入账户名称", trigger: "change" },
  ],
  [GoogleEnums.CURRENCY]: [
    { required: true, message: "请选择币种", trigger: "change" },
  ],
  [GoogleEnums.ACCOUNT_TIMEZONE]: [
    { required: true, message: "请选择时区", trigger: "change" },
  ],
  [GoogleEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "change" },
  ],
  [GoogleEnums.OPEN_ACCOUNT_NUM]: [
    { required: true, message: "请输入开户数量", trigger: "change" },
  ],
};
export const commonRules = {
  [BatchOpenAccountEnum.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [BatchOpenAccountEnum.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择账户时区", trigger: "change" },
  ],
  [BatchOpenAccountEnum.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "blur" },
  ],
};
export const yandexRules = {
  [YandexEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [YandexEnums.ORGTYPE]: [
    { required: true, message: "请选择组织类型", trigger: "change" },
  ],
  [YandexEnums.UNIFIED_SOCIAL_CREDIT_CODE]: [
    { required: true, message: "请输入统一社会信用代码", trigger: "change" },
  ],
  [YandexEnums.OPEN_ACCOUNT_NUM]: [
    { required: true, message: "请输入开户数量", trigger: "change" },
  ],
  [YandexEnums.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择账户时区", trigger: "change" },
  ],
  [YandexEnums.INVITE_USERS]: [
    { required: true, message: "请输入邮箱账号", trigger: "change" },
  ],
};
export const linkedInRules = {
  [LinkedinEnums.COMPANY_NAME_E]: [
    { required: true, message: "请输入公司名称（英文）", trigger: "blur" },
  ],
  [LinkedinEnums.COMPANY_ADDRESS_E]: [
    { required: true, message: "请输入公司地址（英文）", trigger: "blur" },
  ],
  [LinkedinEnums.HQPHONE]: [
    { required: true, message: "请输入hqPhone总部电话", trigger: "blur" },
  ],
  [LinkedinEnums.OFFICIAL_WEBSITE]: [
    { required: true, message: "请输入官方网址", trigger: "blur" },
  ],
  [LinkedinEnums.RECHARGE_AMOUNT]: [
    { required: true, message: "请输入充值金额", trigger: "blur" },
  ],
  [LinkedinEnums.FIRST_ONLINE_TIME]: [
    { required: true, message: "请选择首次上线时间", trigger: "change" },
  ],
  [LinkedinEnums.PROMOTION_TIME]: [
    { required: true, message: "请输入推广时间", trigger: "blur" },
  ],
  [LinkedinEnums.LINKEDIN_COMPANY_PAGE]: [
    { required: true, message: "请输入LinkedIn Company Page", trigger: "blur" },
  ],
  [LinkedinEnums.PERSONAL_LINKEDIN_PAGE]: [
    { required: true, message: "请输入个人领英界面链接", trigger: "blur" },
  ],
  [LinkedinEnums.ACCOUNT_TIMEZONE]: [
    { required: true, message: "请选择时区", trigger: "change" },
  ],
  [LinkedinEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "change" },
  ],
};
export const tiktokRules = {
  [TiktokEnums.ACCOUNT_TYPE]: [
    { required: true, message: "请选择账户类型", trigger: "change" },
  ],
  [TiktokEnums.REGISTRATION_LOCATION]: [
    { required: true, message: "请输入注册地", trigger: "blur" },
  ],
  [TiktokEnums.INDUSTRY_TYPE]: [
    { required: true, message: "请选择行业类型", trigger: "change" },
  ],
  [TiktokEnums.UNIFIED_SOCIAL_CREDIT_CODE]: [
    { required: true, message: "请输入统一社会信用代码", trigger: "blur" },
  ],
  [TiktokEnums.ACCOUNT_NUMBER]: [
    { required: true, message: "请输入开户数量", trigger: "blur" },
  ],
  [TiktokEnums.ADVERTISING_ACCOUNT_NAME]: [
    { required: true, message: "请输入广告账户名称", trigger: "blur" },
  ],
  [TiktokEnums.LEGAL_PERSON_ID_CARD]: [
    { validator: validateIdCard, trigger: "blur" },
  ],
  [TiktokEnums.LEGAL_PERSON_PHONE]: [
    {
      pattern: /^1[3-9]\d{9}$/, // 最新号段正则（支持13-19开头）
      message: "请输入正确的手机号",
      trigger: ["blur", "change"],
    },
  ],
  [TiktokEnums.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择时区", trigger: "change" },
  ],
  [TiktokEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "change" },
  ],
};
export const bingRules = {
  [BingEnums.PROMOTION_COUNTRY]: [
    { required: true, message: "请输入推广国家", trigger: "change" },
  ],
  [BingEnums.PHONE]: [
    { required: true, message: "请输入电话", trigger: "blur" },
  ],
  [BingEnums.SUB_INDUSTRY]: [
    { required: true, message: "请输入子行业", trigger: "blur" },
  ],
  [BingEnums.BUSINESS_LICENSE_POSTAL_CODE]: [
    { required: true, message: "请输入营业执照邮编", trigger: "blur" },
  ],
  [BingEnums.REGISTRATION_PLACE]: [
    { required: true, message: "请输入注册地", trigger: "blur" },
  ],
  [BingEnums.INDUSTRY_TYPE]: [
    { required: true, message: "请选择一级行业类型", trigger: "blur" },
  ],
  [BingEnums.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择时区", trigger: "change" },
  ],
  [BingEnums.PROMOTIONAL_LINK]: [
    { required: true, message: "请输入推广链接", trigger: "change" },
  ],
};
export const metaRules = {
  [MetaEnums.FB_HOME_PAGE]: [
    { required: true, message: "请输入FB主页", trigger: "blur" },
  ],
  [MetaEnums.GENDER]: [
    { required: true, message: "请选择性别", trigger: "change" },
  ],
  [MetaEnums.BIRTHDAY]: [
    { required: true, message: "请选择生日", trigger: "change" },
  ],
  [MetaEnums.PASSWORD]: [
    {
      required: true,
      message: "请输入密码",
      trigger: "blur",
    },
    {
      min: 6,
      message: "密码至少输入6位",
      trigger: ["blur", "change"],
    },
  ],
  [MetaEnums.OFFICIAL_WEBSITE]: [
    { required: true, message: "请输入官方网址", trigger: "blur" },
  ],
  [MetaEnums.ACCOUNT_NAME]: [
    { required: true, message: "请输入账户名称", trigger: "blur" },
  ],
  [MetaEnums.MAIN_BODYOF_ACCOUNT_IS_OPENED]: [
    { required: true, message: "请选择开户主体", trigger: "change" },
  ],
  [MetaEnums.OEID]: [
    { required: true, message: "请输入OE申请编号", trigger: "change" },
  ],
  [MetaEnums.OPEN_ACCOUNT_NUM]: [
    { required: true, message: "请输入开户数量", trigger: "change" },
  ],
  [MetaEnums.ACCOUNT_TIME_ZONE]: [
    { required: true, message: "请选择时区", trigger: "change" },
  ],
};
