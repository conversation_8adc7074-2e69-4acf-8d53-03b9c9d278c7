import axios, { type AxiosInstance, type AxiosRequestConfig } from "axios";
import { ElMessage, ElLoading } from "element-plus";
import { get, merge } from "lodash-es";
import { useRouter } from "vue-router";
import Cookies from "js-cookie";
import configLocation from "@/config";
// console.log(router)
const router = useRouter();
// const baseURL = window.location.href.indexOf("pre-") !== -1 || window.location.href.indexOf("localhost") !== -1 ? "https://pre-ads.gmarketing.tech" : "https://ads.gmarketing.tech"
// const baseURL = 'https://pre-dongcha.300.cn'
/** 创建请求实例 */
function createService() {
  // 创建一个 axios 实例命名为 service
  const service = axios.create();
  // 请求拦截
  service.interceptors.request.use(
    (config) => config,
    // 发送失败
    (error) => Promise.reject(error)
  );
  // 响应拦截（可根据具体业务作出相应的调整）
  service.interceptors.response.use(
    (response) => {
      const apiData = response.data;
      // 二进制数据则直接返回
      const responseType = response.request?.responseType;
      if (responseType === "blob" || responseType === "arraybuffer")
        return apiData;
      // 这个 code 是和后端约定的业务 code

      const code = apiData?.code;
      // 如果没有 code, 代表这不是项目后端开发的 api
      if (code === undefined) {
        ElMessage.error("非本系统的接口");
        return Promise.reject(new Error("非本系统的接口"));
      }
      // console.log(code)
      // console.log(router)
      switch (code) {
        case "0":
          return apiData;
        case "401":
          // ElMessage.error("登录过期，请重新登录")
          const loading = ElLoading.service({
            lock: true,
            text: "鉴权失败，请重新登录",
            background: "rgba(0, 0, 0, 0.7)",
          });
          loading.close();
          router.push("/login");
          Cookies.remove("vuems_name");
          Cookies.remove("LoginToken");
          break;
        default:
          return apiData;
      }
    },
    (error) => {
      if (window && globalThis.$sentry) {
        // 自定义上报http错误
        const _error = JSON.parse(JSON.stringify(error));
        const _config = _error?.config;
        globalThis.$sentry.captureException(_error.message, (scope) => {
          scope.setExtra("headers", _config?.headers); // 设置到⾃定义信息
          _config?.params && scope.setExtra("params", _config?.params);
          scope.setTag("method", _config?.method);
          scope.setTag("url", _config?.url);
          _config?.data && scope.setExtra("data", _config?.data);
          return scope;
        });
      }
      const status = error.response.status;
      switch (status) {
        case 400:
          ElMessage.error("请求参数错误");
          break;
        case 401:
          const loading = ElLoading.service({
            lock: true,
            text: "鉴权失败，请重新登录",
            background: "rgba(0, 0, 0, 0.7)",
          });
          loading.close();
          // ElMessage.error("登录过期，请重新登录")
          router.push("/login");
          Cookies.remove("vuems_name");
          Cookies.remove("LoginToken");
          break;
        case 403:
          ElMessage.error("拒绝访问");
          break;
        case 404:
          ElMessage.error("请求地址出错");
          break;
        case 408:
          ElMessage.error("请求超时");
          break;
        case 500:
          ElMessage.error("服务器内部错误");

          break;
        case 501:
          ElMessage.error("服务未实现");

          break;
        case 502:
          ElMessage.error("请求超时");
          break;
        case 503:
          ElMessage.error("服务不可用");
          break;
        case 504:
          ElMessage.error("网关超时");
          break;
        case 505:
          ElMessage.error("HTTP 版本不受支持");
          error.message = "HTTP 版本不受支持";
          break;
        default:
          break;
      }
      return Promise.reject(error);
    }
  );
  return service;
}
/** 创建请求方法 */
function createRequest(service: AxiosInstance) {
  return function <T>(config: AxiosRequestConfig): Promise<T> {
    const token = localStorage.getItem("accessToken");
    const defaultConfig = {
      headers: {
        Authorization: token,
        // "Content-Type": "application/json;charset=utf-8;",
        "Access-Control-Allow-Origin": "*",
      },
      withCredentials: true,
      timeout: 500000,
      baseURL: configLocation.baseURL,
    };
    // 将默认配置 defaultConfig 和传入的自定义配置 config 进行合并成为 mergeConfig
    const mergeConfig = merge(defaultConfig, config);
    return service(mergeConfig);
  };
}

/** 用于网络请求的实例 */
const service = createService();
/** 用于网络请求的方法 */
export const request = createRequest(service);
