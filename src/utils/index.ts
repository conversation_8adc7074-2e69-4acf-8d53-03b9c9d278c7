export const setProperty = (
  prop: string,
  val: any,
  dom = document.documentElement
) => {
  dom.style.setProperty(prop, val);
};

export const mix = (
  color1: string,
  color2: string,
  weight: number = 0.5
): string => {
  let color = "#";
  for (let i = 0; i <= 2; i++) {
    const c1 = parseInt(color1.substring(1 + i * 2, 3 + i * 2), 16);
    const c2 = parseInt(color2.substring(1 + i * 2, 3 + i * 2), 16);
    const c = Math.round(c1 * weight + c2 * (1 - weight));
    color += c.toString(16).padStart(2, "0");
  }
  return color;
};

export const microForceDispatch = (type, data) => {
  if (window.microApp) {
    window.microApp?.forceDispatch({
      type,
      data,
    });
  }
};
// 时间日期选择器默认选择 day 数据
export const getLastDaysRange = (day = 30) => {
  const today = new Date();
  const end = new Date(today.setDate(today.getDate() - 1)); // 昨天
  const start = new Date(today.setDate(today.getDate() - (day - 1))); //
  return [formatDate(start), formatDate(end)];
};
// 转换Date格式
export const formatDate = (date) => {
  return new Intl.DateTimeFormat("en-CA", {
    // en-CA 格式为 YYYY-MM-DD
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  }).format(date);
};
// 获取当月时间区间 第一天和最后一天
export const getMonthRange = () => {
  const today = new Date();

  // 获取本月第一天
  const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

  // 获取本月最后一天
  const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  // 格式化为 YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  return {
    firstDay: formatDate(firstDay),
    lastDay: formatDate(lastDay),
  };
};
export function validateEmail(email) {
  const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return regex.test(email);
}