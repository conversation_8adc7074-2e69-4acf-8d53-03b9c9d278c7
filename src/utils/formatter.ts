/**
 * 渠道类型formatter
 */
export const formatterMediaType = (row, col, cellValue) => {
  switch (cellValue) {
    case 1:
      return "Google";
    case 2:
      return "Yandex";
    case 3:
      return "Meta";
    case 4:
      return "Tiktok";
    case 5:
      return "Bing";
    case 6:
      return "LinkedIn";
  }
};

/**
 * 数据中心渠道类型Icon formatter
 */
export const formatterMediaTypeIcon = (cellValue) => {
  switch (cellValue) {
    case 1:
      return "google";
    case 2:
      return "yandex";
    case 3:
      return "meta";
    case 4:
      return "tiktok";
    case 5:
      return "bing";
    case 6:
      return "linkedln";
  }
};

/**
 * 广告账户管理列表渠道类型 formatter
 */
export const formatterMediaTypeInList = (cellValue) => {
  switch (cellValue) {
    case 1:
      return "Google Ads";
    case 2:
      return "Yandex";
    case 3:
      return "Meta";
    case 4:
      return "TikTok For Business";
    case 5:
      return "Bing Ads";
    case 6:
      return "LinkedIn";
  }
};

/**
 * 跨媒体转款状态 formatter
 */
export const formatterTransferStatus = (row, col, cellValue) => {
  switch (cellValue) {
    case 1:
      return "转款中";
    case 2:
      return "转款成功";
    case 3:
      return "转款失败";
  }
};

/**
 * 充值状态 formatter
 */
export const formatterRechargeStatus = (row, col, cellValue) => {
  switch (cellValue) {
    case 1:
      return "充值中";
    case 2:
      return "充值成功";
    case 3:
      return "充值失败";
  }
};
