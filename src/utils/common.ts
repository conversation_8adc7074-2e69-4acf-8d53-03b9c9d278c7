import axios from "axios";
import { getToken } from "./auth";
import { ElMessage } from "element-plus";

/**
 * 通用文件下载方法
 * @param {string} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {string} method - 请求方法 (默认: POST)
 */
export async function downloadFile({
  url,
  params = {},
  method = "POST",
  headers = {},
  name = "商品列表",
}) {
  try {
    // 发起请求，设置 responseType 为 'blob'
    const response = await axios({
      url,
      method,
      data: method === "POST" ? params : undefined, // 仅 POST 时传递请求体
      params: method === "GET" ? params : undefined, // 仅 GET 时传递查询参数
      headers: {
        ...headers, // 合并传入的 headers
        authorization: getToken(), // 添加 auth 参数
      },
      withCredentials: true,
    });
    if (response.data.code != 200) {
      ElMessage.error(response.data.msg);
      return;
    }
    const data = window.atob(response.data.data);
    const len = data.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = data.charCodeAt(i);
    }
    // 创建 Blob 对象
    const blob = new Blob([bytes], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    // 从响应头中获取文件名
    const disposition = response.headers["content-disposition"];
    const filename = disposition
      ? decodeURIComponent(
          disposition.split("filename=")[1]?.replace(/['"]/g, "")
        )
      : name + ".xlsx"; // 默认文件名
    // 创建下载链接并触发下载
    const urlObject = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = urlObject;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // 释放 Blob URL
    window.URL.revokeObjectURL(urlObject);
  } catch (error) {
    console.error("文件下载失败:", error);
    throw error; // 抛出错误，便于外部处理
  }
}

/**
 * 获取url 指定参数
 */
export function getQueryParamsByKey(key: string): string {
  const url = window.location.href;
  const params = new URL(url).searchParams;
  return params.get(key) ?? "";
}

// 格式化日期为 "YYYY-MM-DD HH:mm:ss" 格式
export function formatDateToCustomString(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * @description 限制日期控件 选择范围
 * @param date
 */
export function validateTimeRange(date: any): Date[] {
  if (!date || date.length !== 2) return;

  let [startDate, endDate] = date;

  // 计算最大允许的结束日期（开始日期 + 31 天）
  const maxEndDate = new Date(startDate);
  maxEndDate.setDate(maxEndDate.getDate() + 31); // 增加 31 天

  // 如果结束日期超出了 31 天范围，则重置为最大允许日期
  if (new Date(endDate).getTime() > new Date(maxEndDate).getTime()) {
    date[1] = formatDateToCustomString(maxEndDate);
  }

  return date;
}

export default {};
