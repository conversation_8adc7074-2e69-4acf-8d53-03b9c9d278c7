const fs = require('fs');
const path = require('path');

function incrementVersion(version) {
  const versionParts = version.split('.').map(Number);
  versionParts[2] += 1; // 增加补丁版本号
  return versionParts.join('.');
}

const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

packageJson.version = incrementVersion(packageJson.version);

fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');

console.log(`Version updated to ${packageJson.version}`);
