import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import VueSetupExtend from 'vite-plugin-vue-setup-extend';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import path from "path";
// import sentryVitePlugin  from 'vite-plugin-sentry';
export default defineConfig({

	base: '/ads/',
	plugins: [
		vue(),
		VueSetupExtend(),
		AutoImport({
			resolvers: [ElementPlusResolver()]
		}),
	    Components({
		resolvers: [
			ElementPlusResolver({
			importStyle: "sass",
				// directives: true,
				// version: "2.1.5",
				}),
			],
		}),
		createSvgIconsPlugin({
			// Specify the icon folder to be cached
			iconDirs: [path.resolve(process.cwd(), "src/assets/svg")],
			// Specify symbolId format
			symbolId: "icon-[dir]-[name]"
		}),
		// sentryVitePlugin({
		// 	// 必填参数
		// 	authToken: 'eead8592599b4db0b2bf1da5a906ff3f7b46a72ee79f472abac1aba831660d5f',
		// 	org: 'sentry',
		// 	project: 'cbo-ads-operation',
		// 	release: process.env.npm_package_version, // 使用 package.json 中的版本号
		// 	// 可选参数
		// 	url: 'https://monitor.aiyouyi.cn/', // 默认为 https://sentry.io
		// 	deploy: {
		// 	  env: 'production'
		// 	},
		// 	setCommits: {
		// 	  auto: true
		// 	},
		// 	sourceMaps: {
		// 	  include: ['./dist/assets'], // 根据实际情况设置
		// 	  ignore: ['node_modules'],
		// 	  urlPrefix: '~/assets'
		// 	}
		//   })
	],
	optimizeDeps: {
		include: ['schart.js']
	},
	resolve: {
		alias: {
			'@': '/src',
			'~': '/src/assets',
			'&': '/public',
		}
	},
	define: {
		__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: "true",
		'process.env': process.env
	},
	build : {
		// 去除console和debugger
        minify : 'terser' ,
        terserOptions : {
            compress : {
                drop_console : true ,
                drop_debugger : true ,
            },
        }, 
		rollupOptions: {
			output: {
			  manualChunks(id) {
				if (id.includes('node_modules')) {
				  return id.toString().split('node_modules/')[1].split('/')[0].toString();
				}
			  }
			}
		  },
		// sourcemap: true // 确保生成 sourcemap 文件
	},
	css: {
		devSourcemap: true,
		preprocessorOptions: {
			css: { charset: false },
			// 定义全局 SCSS 变量
			scss: {
				javascriptEnabled: true,
				additionalData: `@use "@/assets/css/variables.scss" as *;`
			}
		}
	},
	server: {
		port: 9000, // 确保这里和本地启动的端口一致
		host: true,
		headers: {
		  "Access-Control-Allow-Origin": "*"
		},
		// Allow services to be provided for non root directories of projects
		fs: {
		  strict: false
		},
		// proxy: {
		// 	/** 代理前缀为 /dev-api 的请求  */
		// 	"/ad-trade-web": {
		// 	  changeOrigin: true,
		// 	  target: "http://**************:8897",
		// 	  rewrite: (path: any) => path.replace("/ad-trade-web", "")
		// 	}
		//   }
		// proxy: {
		//   // 如果您想要将所有以 '/api' 开头的请求代理到远程服务器，可以这样配置：
		//   '/ad-trade-web': {
		// 	target: 'http://pre-ads.gmarketing.tech',
		// 	changeOrigin: true,
		// 	// rewrite: (path) => path.replace(/^\/ad-trade-web/, ''),
		//   },
		// }
	  }
   
});
